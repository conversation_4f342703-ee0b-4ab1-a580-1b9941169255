@echo off
title AI-Vision China Mirrors Test
color 0A

echo.
echo ========================================
echo AI-Vision China Mirrors Test
echo ========================================
echo.

echo Now using China mirrors by default:
echo.

echo Mirror sources (priority order):
echo 1. Tsinghua University (pypi.tuna.tsinghua.edu.cn)
echo 2. Alibaba Cloud (mirrors.aliyun.com)  
echo 3. Douban Mirror (pypi.douban.com)
echo 4. PyTorch Official (download.pytorch.org)
echo.

echo Expected improvements:
echo - PyTorch download speed 5-10x faster
echo - More stable dependency installation
echo - Auto-retry with multiple mirror sources
echo - Reduced network timeout issues
echo.

echo Download speed comparison:
echo - Official source: 100-500 KB/s (in China)
echo - Tsinghua mirror: 5-20 MB/s (in China)
echo - Alibaba mirror: 3-15 MB/s (in China)
echo.

echo Estimated installation time (with China mirrors):
echo - PyTorch 2.7.1: 5-15 minutes (was 30-60 minutes)
echo - Dependencies: 2-5 minutes (was 10-20 minutes)
echo - Total: 10-25 minutes (was 40-80 minutes)
echo.

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit
)

echo Environment check passed
echo.

if not exist "node_modules" (
    echo Installing dependencies...
    npm install
)

echo.
echo ========================================
echo Starting China Mirrors Test
echo ========================================
echo.

echo Configuration changes:
echo - Default to Tsinghua PyPI mirror
echo - PyTorch packages from Tsinghua source
echo - Auto-fallback to other China mirrors
echo - Added trusted-host configurations
echo.

echo Notes:
echo - First time using mirrors may need sync time
echo - Auto-switch if one mirror is slow
echo - All packages prioritize China sources
echo - Keep network connection stable
echo.

set /p start="Start China mirrors installation test? (y/N): "
if /i not "%start%"=="y" (
    echo Test cancelled
    pause
    exit
)

echo.
echo Starting installer (China mirrors mode)...
echo Watch console output for:
echo - "using Tsinghua mirror"
echo - Faster download speeds
echo - Fewer timeout errors
echo.

npm start

echo.
if %errorlevel% equ 0 (
    echo ========================================
    echo China mirrors test successful!
    echo ========================================
    echo.
    echo Installation speed should be much faster
    echo PyTorch download time significantly reduced
    echo.
) else (
    echo ========================================
    echo Installation encountered issues
    echo ========================================
    echo.
    echo Possible causes:
    echo - Network connection issues
    echo - Mirror source temporarily unavailable
    echo - Package version compatibility issues
    echo.
    echo Solutions:
    echo - Check network connection
    echo - Retry later
    echo - Installer will auto-try multiple sources
    echo.
)

pause
