/**
 * AI-Vision 安装器构建脚本
 * 用于构建和打包安装器
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始构建 AI-Vision 安装器...');

// 检查必要的文件
function checkRequiredFiles() {
    const requiredFiles = [
        'installer.html',
        'installer-main.js',
        'installer-electron-main.js',
        'installer-preload.js',
        'installer-config.js',
        'package.json'
    ];
    
    console.log('📋 检查必要文件...');
    
    for (const file of requiredFiles) {
        if (!fs.existsSync(file)) {
            console.error(`❌ 缺少必要文件: ${file}`);
            process.exit(1);
        }
    }
    
    console.log('✅ 所有必要文件检查完成');
}

// 安装依赖
function installDependencies() {
    console.log('📦 安装依赖包...');
    
    try {
        execSync('npm install', { stdio: 'inherit' });
        console.log('✅ 依赖包安装完成');
    } catch (error) {
        console.error('❌ 依赖包安装失败:', error.message);
        process.exit(1);
    }
}

// 构建安装器
function buildInstaller() {
    console.log('🔨 构建安装器...');
    
    try {
        // 构建Windows版本
        execSync('npm run build:win', { stdio: 'inherit' });
        console.log('✅ Windows安装器构建完成');
        
        // 构建便携版
        execSync('npm run build:portable', { stdio: 'inherit' });
        console.log('✅ 便携版安装器构建完成');
        
    } catch (error) {
        console.error('❌ 构建失败:', error.message);
        process.exit(1);
    }
}

// 显示构建结果
function showBuildResults() {
    console.log('\n🎉 构建完成！');
    console.log('📁 输出目录: ./dist/');
    
    const distDir = path.join(__dirname, 'dist');
    if (fs.existsSync(distDir)) {
        const files = fs.readdirSync(distDir);
        console.log('\n📦 生成的文件:');
        files.forEach(file => {
            const filePath = path.join(distDir, file);
            const stats = fs.statSync(filePath);
            const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            console.log(`  - ${file} (${sizeInMB} MB)`);
        });
    }
    
    console.log('\n🚀 安装器已准备就绪！');
    console.log('💡 提示: 您可以将生成的安装器分发给用户使用');
}

// 主函数
function main() {
    try {
        checkRequiredFiles();
        installDependencies();
        buildInstaller();
        showBuildResults();
    } catch (error) {
        console.error('❌ 构建过程中发生错误:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    checkRequiredFiles,
    installDependencies,
    buildInstaller,
    showBuildResults,
    main
};
