/**
 * AI-Vision 智能安装器主脚本
 * 处理安装流程和用户界面交互
 */

// 导入配置
const config = typeof INSTALLER_CONFIG !== 'undefined' ? INSTALLER_CONFIG : require('./installer-config.js');

// 全局变量
let currentStep = 'welcome';
let selectedInstallPath = config.installation.defaultPath;
let installationCancelled = false;
let totalProgress = 0;
let currentStepProgress = 0;
let installationStartTime = 0;

// 步骤权重映射
const stepWeights = {};
config.installSteps.forEach(step => {
    stepWeights[step.id] = step.weight;
});

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化界面
    updateAvailableSpace();

    // 设置事件监听器
    document.getElementById('install-path').addEventListener('input', updateAvailableSpace);

    // 监听安装进度
    if (window.electronAPI) {
        window.electronAPI.onInstallationProgress((progress) => {
            handleInstallationProgress(progress);
        });
    }
});

// 导航函数
function nextStep() {
    const currentStepElement = document.getElementById(`${currentStep}-step`);
    let nextStepId;
    
    switch (currentStep) {
        case 'welcome':
            nextStepId = 'path';
            break;
        case 'path':
            nextStepId = 'install';
            // 开始安装流程
            setTimeout(() => startInstallation(), 500);
            break;
        case 'install':
            nextStepId = 'complete';
            break;
        default:
            return;
    }
    
    const nextStepElement = document.getElementById(`${nextStepId}-step`);
    
    if (currentStepElement && nextStepElement) {
        currentStepElement.classList.remove('active');
        nextStepElement.classList.add('active');
        currentStep = nextStepId;
    }
}

function prevStep() {
    const currentStepElement = document.getElementById(`${currentStep}-step`);
    let prevStepId;
    
    switch (currentStep) {
        case 'path':
            prevStepId = 'welcome';
            break;
        case 'install':
            // 不允许从安装步骤返回
            return;
        case 'complete':
            // 不允许从完成步骤返回
            return;
        default:
            return;
    }
    
    const prevStepElement = document.getElementById(`${prevStepId}-step`);
    
    if (currentStepElement && prevStepElement) {
        currentStepElement.classList.remove('active');
        prevStepElement.classList.add('active');
        currentStep = prevStepId;
    }
}

// 路径选择函数
function browsePath() {
    // 在Electron环境中，这将调用原生对话框
    // 在Web环境中，这是一个模拟
    if (window.electronAPI && window.electronAPI.selectDirectory) {
        window.electronAPI.selectDirectory().then(result => {
            if (result && !result.canceled && result.filePaths.length > 0) {
                const path = result.filePaths[0];
                document.getElementById('install-path').value = path;
                selectedInstallPath = path;
                updateAvailableSpace();
            }
        });
    } else {
        // 模拟选择目录
        console.log('浏览目录功能需要Electron环境');
        alert('浏览目录功能需要在桌面应用中使用');
    }
}

function updateAvailableSpace() {
    const pathInput = document.getElementById('install-path');
    selectedInstallPath = pathInput.value;
    
    // 在Electron环境中，这将获取实际的磁盘空间
    if (window.electronAPI && window.electronAPI.getDiskSpace) {
        window.electronAPI.getDiskSpace(selectedInstallPath).then(space => {
            const availableGB = Math.floor(space.available / (1024 * 1024 * 1024));
            document.getElementById('available-space').textContent = `${availableGB} GB`;
            
            validatePath(space.available);
        });
    } else {
        // 模拟磁盘空间检查
        document.getElementById('available-space').textContent = '模拟: 100 GB';
        validatePath(100 * 1024 * 1024 * 1024);
    }
}

function validatePath(availableSpace) {
    const validationDiv = document.getElementById('path-validation');
    const nextButton = document.getElementById('path-next-btn');
    const requiredSpace = config.installation.requiredSpace;
    
    // 检查路径是否有效
    const isValid = selectedInstallPath && selectedInstallPath.trim() !== '';
    const hasEnoughSpace = availableSpace >= requiredSpace;
    
    if (!isValid) {
        validationDiv.className = 'validation-info error';
        validationDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 请输入有效的安装路径';
        nextButton.disabled = true;
    } else if (!hasEnoughSpace) {
        const requiredGB = Math.ceil(requiredSpace / (1024 * 1024 * 1024));
        const availableGB = Math.floor(availableSpace / (1024 * 1024 * 1024));
        
        validationDiv.className = 'validation-info error';
        validationDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 磁盘空间不足，需要至少 ${requiredGB} GB，当前可用 ${availableGB} GB`;
        nextButton.disabled = true;
    } else {
        validationDiv.className = 'validation-info success';
        validationDiv.innerHTML = '<i class="fas fa-check-circle"></i> 安装路径有效，空间充足';
        nextButton.disabled = false;
    }
}

function validateAndNext() {
    const nextButton = document.getElementById('path-next-btn');
    if (!nextButton.disabled) {
        nextStep();
    }
}

// 安装流程函数
async function startInstallation() {
    installationStartTime = Date.now();
    installationCancelled = false;

    // 更新UI
    updateInstallStatus('正在准备安装...');
    updateProgress(0);

    try {
        if (window.electronAPI) {
            // 使用真实的安装流程
            const result = await window.electronAPI.startInstallation(selectedInstallPath);

            if (result.success) {
                updateInstallStatus('安装完成！');
                updateProgress(100);
                setTimeout(() => {
                    nextStep();
                    updateCompletionInfo();
                }, 1000);
            } else {
                throw new Error(result.error || '安装失败');
            }
        } else {
            // 模拟安装流程（用于测试）
            await runInstallationSteps();

            if (!installationCancelled) {
                updateInstallStatus('安装完成！');
                updateProgress(100);
                setTimeout(() => {
                    nextStep();
                    updateCompletionInfo();
                }, 1000);
            }
        }
    } catch (error) {
        if (!installationCancelled) {
            updateInstallStatus(`安装失败: ${error.message}`, 'error');
            logToInstall(`❌ 错误: ${error.message}`, 'error');
            console.error('安装错误:', error);
        }
    }
}

async function runInstallationSteps() {
    // 1. 环境检测
    await runInstallStep('environment', async () => {
        updateInstallStatus('正在检测系统环境...');
        logToInstall('🔍 开始检测系统环境');
        
        // 模拟环境检测
        await simulateOperation(1000);
        logToInstall('✅ 系统环境检测完成');
        
        // 检测GPU
        logToInstall('🔍 检测GPU...');
        await simulateOperation(500);
        logToInstall('✅ 检测到NVIDIA GPU，支持CUDA');
        
        // 检测网络
        logToInstall('🔍 检测网络连接...');
        await simulateOperation(500);
        logToInstall('✅ 网络连接正常');
        
        return true;
    });
    
    // 2. 下载Python
    await runInstallStep('download-python', async () => {
        updateInstallStatus('正在下载Python 3.12.9...');
        logToInstall('📦 开始下载Python 3.12.9');
        
        // 模拟下载过程
        for (let i = 0; i <= 10; i++) {
            if (installationCancelled) break;
            updateStepProgress(i * 10);
            logToInstall(`📥 Python下载进度: ${i * 10}%`);
            await simulateOperation(300);
        }
        
        logToInstall('✅ Python 3.12.9下载完成');
        return true;
    });
    
    // 3. 配置Python环境
    await runInstallStep('extract-python', async () => {
        updateInstallStatus('正在配置Python环境...');
        logToInstall('🔧 开始配置Python环境');
        
        // 模拟解压和配置
        await simulateOperation(1500);
        logToInstall('📂 解压Python包');
        await simulateOperation(1000);
        logToInstall('🔧 配置Python路径');
        await simulateOperation(500);
        logToInstall('📦 安装pip');
        await simulateOperation(1000);
        
        logToInstall('✅ Python环境配置完成');
        return true;
    });
    
    // 4. 下载ComfyUI
    await runInstallStep('download-comfyui', async () => {
        updateInstallStatus('正在下载ComfyUI...');
        logToInstall('📦 开始下载ComfyUI');
        
        // 模拟下载过程
        for (let i = 0; i <= 10; i++) {
            if (installationCancelled) break;
            updateStepProgress(i * 10);
            logToInstall(`📥 ComfyUI下载进度: ${i * 10}%`);
            await simulateOperation(400);
        }
        
        logToInstall('✅ ComfyUI下载完成');
        return true;
    });
    
    // 5. 配置ComfyUI
    await runInstallStep('extract-comfyui', async () => {
        updateInstallStatus('正在配置ComfyUI...');
        logToInstall('🔧 开始配置ComfyUI');
        
        // 模拟解压和配置
        await simulateOperation(1000);
        logToInstall('📂 解压ComfyUI包');
        await simulateOperation(1000);
        logToInstall('🔧 配置ComfyUI启动脚本');
        await simulateOperation(500);
        
        logToInstall('✅ ComfyUI配置完成');
        return true;
    });
    
    // 6. 安装PyTorch
    await runInstallStep('install-pytorch', async () => {
        updateInstallStatus('正在安装PyTorch 2.7.1...');
        logToInstall('📦 开始安装PyTorch 2.7.1 (CUDA 12.8)');
        
        // 模拟安装过程
        for (let i = 0; i <= 10; i++) {
            if (installationCancelled) break;
            updateStepProgress(i * 10);
            logToInstall(`📥 PyTorch安装进度: ${i * 10}%`);
            await simulateOperation(800);
        }
        
        logToInstall('✅ PyTorch 2.7.1安装完成');
        return true;
    });
    
    // 7. 安装依赖包
    await runInstallStep('install-dependencies', async () => {
        updateInstallStatus('正在安装依赖包...');
        logToInstall('📦 开始安装依赖包');
        
        // 模拟安装过程
        const dependencies = [
            'Pillow', 'numpy', 'safetensors', 'transformers', 
            'tokenizers', 'opencv-python', 'diffusers', 'accelerate'
        ];
        
        for (let i = 0; i < dependencies.length; i++) {
            if (installationCancelled) break;
            const progress = Math.floor((i / dependencies.length) * 100);
            updateStepProgress(progress);
            logToInstall(`📥 安装: ${dependencies[i]}`);
            await simulateOperation(600);
        }
        
        logToInstall('✅ 所有依赖包安装完成');
        return true;
    });
    
    // 8. 配置环境
    await runInstallStep('configure', async () => {
        updateInstallStatus('正在配置环境...');
        logToInstall('🔧 开始配置环境');
        
        // 模拟配置过程
        await simulateOperation(800);
        logToInstall('📝 创建启动脚本');
        await simulateOperation(500);
        logToInstall('🔧 配置AI-Vision启动器');
        await simulateOperation(700);
        logToInstall('🔧 配置卸载程序');
        await simulateOperation(500);
        
        logToInstall('✅ 环境配置完成');
        return true;
    });
    
    // 9. 完成安装
    await runInstallStep('finalize', async () => {
        updateInstallStatus('正在完成安装...');
        logToInstall('🎉 正在完成安装');
        
        // 模拟最终步骤
        await simulateOperation(500);
        logToInstall('📝 创建桌面快捷方式');
        await simulateOperation(500);
        logToInstall('📝 添加到开始菜单');
        await simulateOperation(500);
        logToInstall('🧹 清理临时文件');
        await simulateOperation(500);
        
        const totalTime = Math.floor((Date.now() - installationStartTime) / 1000);
        logToInstall(`🎉 安装完成！总用时: ${totalTime} 秒`);
        return true;
    });
}

async function runInstallStep(stepId, stepFunction) {
    if (installationCancelled) {
        throw new Error('安装已取消');
    }
    
    // 更新UI状态
    const stepItems = document.querySelectorAll('.detail-item');
    stepItems.forEach(item => {
        if (item.dataset.step === stepId) {
            item.className = 'detail-item active';
            item.querySelector('i').className = 'fas fa-spinner fa-spin';
        }
    });
    
    // 重置当前步骤进度
    currentStepProgress = 0;
    
    try {
        // 执行步骤
        const result = await stepFunction();
        
        // 更新步骤状态为完成
        stepItems.forEach(item => {
            if (item.dataset.step === stepId) {
                item.className = 'detail-item completed';
                item.querySelector('i').className = 'fas fa-check-circle';
            }
        });
        
        // 更新总进度
        updateTotalProgress(stepId);
        
        return result;
    } catch (error) {
        // 更新步骤状态为错误
        stepItems.forEach(item => {
            if (item.dataset.step === stepId) {
                item.className = 'detail-item error';
                item.querySelector('i').className = 'fas fa-times-circle';
            }
        });
        
        throw error;
    }
}

// 进度更新函数
function updateStepProgress(progress) {
    currentStepProgress = progress;
    // 不更新总进度，保持在步骤之间的值
}

function updateTotalProgress(completedStepId) {
    // 计算已完成步骤的总权重
    let completedWeight = 0;
    let totalWeight = 0;
    
    // 计算总权重
    for (const stepId in stepWeights) {
        totalWeight += stepWeights[stepId];
    }
    
    // 找到当前步骤的索引
    const stepIds = Object.keys(stepWeights);
    const currentIndex = stepIds.indexOf(completedStepId);
    
    // 计算已完成的权重
    for (let i = 0; i <= currentIndex; i++) {
        completedWeight += stepWeights[stepIds[i]];
    }
    
    // 计算总进度百分比
    totalProgress = Math.floor((completedWeight / totalWeight) * 100);
    updateProgress(totalProgress);
}

function updateProgress(percent) {
    document.getElementById('progress-fill').style.width = `${percent}%`;
    document.getElementById('progress-percent').textContent = `${percent}%`;
}

function updateInstallStatus(message, type = 'info') {
    document.getElementById('install-status').textContent = message;
    document.getElementById('current-step').textContent = message;
    
    // 可以根据类型设置不同的样式
    const statusElement = document.getElementById('install-status');
    statusElement.className = '';
    if (type === 'error') {
        statusElement.classList.add('error');
    } else if (type === 'warning') {
        statusElement.classList.add('warning');
    } else if (type === 'success') {
        statusElement.classList.add('success');
    }
}

function logToInstall(message, type = 'info') {
    const logElement = document.getElementById('install-log');
    const timestamp = new Date().toLocaleTimeString();
    
    let className = '';
    if (type === 'error') className = 'error';
    if (type === 'warning') className = 'warning';
    if (type === 'success') className = 'success';
    
    const logLine = document.createElement('div');
    logLine.className = className;
    logLine.textContent = `[${timestamp}] ${message}`;
    
    logElement.appendChild(logLine);
    logElement.scrollTop = logElement.scrollHeight;
}

// 完成和取消函数
function cancelInstall() {
    if (confirm('确定要取消安装吗？已下载的文件将被删除。')) {
        installationCancelled = true;
        updateInstallStatus('安装已取消', 'warning');
        logToInstall('❌ 用户取消了安装', 'warning');

        // 调用Electron API取消安装
        if (window.electronAPI) {
            window.electronAPI.cancelInstallation();
        }
    }
}

function updateCompletionInfo() {
    document.getElementById('final-install-path').textContent = selectedInstallPath;
    document.getElementById('used-space').textContent = '计算中...';
    
    // 在实际应用中，这里应该计算实际使用的空间
    setTimeout(() => {
        document.getElementById('used-space').textContent = '约 15 GB';
    }, 1000);
}

function finishInstall() {
    const launchNow = document.getElementById('launch-now').checked;
    const createShortcut = document.getElementById('create-desktop-shortcut').checked;
    const addToStartMenu = document.getElementById('add-to-start-menu').checked;

    // 启动AI-Vision启动器
    if (launchNow && window.electronAPI) {
        window.electronAPI.launchAIVision(selectedInstallPath)
            .then(result => {
                if (!result.success) {
                    console.error('启动AI-Vision失败:', result.error);
                    alert(`启动AI-Vision失败: ${result.error}`);
                }
            });
    }

    // 关闭安装器
    if (window.electronAPI) {
        window.electronAPI.closeInstaller();
    } else {
        window.close();
    }
}

function openInstallFolder() {
    // 打开安装目录
    if (window.electronAPI) {
        window.electronAPI.openFolder(selectedInstallPath)
            .then(result => {
                if (!result.success) {
                    console.error('打开文件夹失败:', result.error);
                    alert(`打开文件夹失败: ${result.error}`);
                }
            });
    } else {
        alert(`安装目录: ${selectedInstallPath}`);
    }
}

// 处理Electron安装进度
function handleInstallationProgress(progress) {
    console.log('安装进度:', progress);

    // 更新UI
    if (progress.step) {
        // 更新步骤状态
        const stepItems = document.querySelectorAll('.detail-item');
        stepItems.forEach(item => {
            if (item.dataset.step === progress.step) {
                // 设置当前步骤状态
                if (progress.status === 'active') {
                    item.className = 'detail-item active';
                    item.querySelector('i').className = 'fas fa-spinner fa-spin';
                } else if (progress.status === 'completed') {
                    item.className = 'detail-item completed';
                    item.querySelector('i').className = 'fas fa-check-circle';
                } else if (progress.status === 'error') {
                    item.className = 'detail-item error';
                    item.querySelector('i').className = 'fas fa-times-circle';
                }
            }
        });
    }

    // 更新状态消息
    if (progress.message) {
        updateInstallStatus(progress.message);
    }

    // 更新进度条
    if (progress.progress !== undefined) {
        updateProgress(progress.progress);
    }

    // 添加日志
    if (progress.message) {
        logToInstall(progress.message, progress.status === 'error' ? 'error' : 'info');
    }
}

// 辅助函数
function simulateOperation(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
