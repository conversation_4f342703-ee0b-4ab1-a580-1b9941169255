/* 精简的Font Awesome图标集 - 仅包含启动器使用的图标 */
@font-face {
    font-family: 'FontAwesome';
    src: local('FontAwesome'), local('Font Awesome 6 Free');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

/* 使用Unicode字符替代Font Awesome图标，减少依赖 */
.fa-home::before { content: '🏠'; }
.fa-cog::before, .fa-settings::before { content: '⚙️'; }
.fa-download::before { content: '⬇️'; }
.fa-upload::before { content: '⬆️'; }
.fa-play::before { content: '▶️'; }
.fa-pause::before { content: '⏸️'; }
.fa-stop::before { content: '⏹️'; }
.fa-refresh::before, .fa-reload::before { content: '🔄'; }
.fa-folder::before { content: '📁'; }
.fa-file::before { content: '📄'; }
.fa-edit::before { content: '✏️'; }
.fa-trash::before { content: '🗑️'; }
.fa-check::before { content: '✅'; }
.fa-times::before, .fa-close::before { content: '❌'; }
.fa-warning::before, .fa-exclamation::before { content: '⚠️'; }
.fa-info::before { content: 'ℹ️'; }
.fa-question::before { content: '❓'; }
.fa-search::before { content: '🔍'; }
.fa-list::before { content: '📋'; }
.fa-grid::before { content: '▦'; }
.fa-minimize::before { content: '➖'; }
.fa-maximize::before { content: '➕'; }
.fa-close-window::before { content: '✖️'; }

/* 保持Font Awesome的基础样式 */
.fa, .fas, .far, .fal, .fab {
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-size: inherit;
}