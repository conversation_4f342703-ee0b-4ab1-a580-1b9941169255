/**
 * 音效配置跨域同步工具
 * Audio Configuration Cross-Domain Sync Tool
 */

class AudioConfigSync {
    constructor() {
        this.configFile = 'audio-config.json';
        this.backupKey = 'launcher_audio_config_backup';
    }

    /**
     * 保存配置到文件和localStorage
     */
    async saveConfig(soundMap) {
        const configData = {
            timestamp: new Date().toISOString(),
            soundMap: soundMap,
            version: '1.0',
            source: 'audio-config-tool'
        };

        try {
            // 方法1：保存到localStorage（同域）
            localStorage.setItem('launcher_audio_config', JSON.stringify(configData));
            console.log('配置已保存到localStorage');

            // 方法2：保存到文件（跨域）
            await this.saveToFile(configData);
            console.log('配置已保存到文件');

            // 方法3：保存备份到另一个key
            localStorage.setItem(this.backupKey, JSON.stringify(configData));
            console.log('配置备份已保存');

            return true;
        } catch (error) {
            console.error('保存配置失败:', error);
            return false;
        }
    }

    /**
     * 保存配置到文件
     */
    async saveToFile(configData) {
        try {
            // 优先尝试保存到启动器后端API
            const launcherResponse = await fetch('http://127.0.0.1:8404/audio-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(configData)
            });

            if (launcherResponse.ok) {
                console.log('配置已保存到启动器后端');
                return true;
            }
        } catch (error) {
            console.log('启动器后端API不可用:', error.message);
        }

        try {
            // 备用方案：尝试配置工具API
            const response = await fetch('/api/save-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(configData)
            });

            if (response.ok) {
                console.log('配置已通过配置工具API保存到文件');
                return true;
            } else {
                throw new Error('配置工具API保存失败');
            }
        } catch (error) {
            console.log('配置工具API不可用，使用本地存储:', error.message);
            // 如果API不可用，将配置保存到特殊的localStorage key
            localStorage.setItem('audio_config_for_launcher', JSON.stringify(configData));
            return false;
        }
    }

    /**
     * 从多个来源加载配置
     */
    async loadConfig() {
        console.log('开始加载音效配置...');

        // 方法1：从localStorage加载
        let config = this.loadFromLocalStorage();
        if (config) {
            console.log('从localStorage加载配置成功');
            return config;
        }

        // 方法2：从文件加载
        config = await this.loadFromFile();
        if (config) {
            console.log('从文件加载配置成功');
            return config;
        }

        // 方法3：从备份加载
        config = this.loadFromBackup();
        if (config) {
            console.log('从备份加载配置成功');
            return config;
        }

        // 方法4：从特殊key加载
        config = this.loadFromSpecialKey();
        if (config) {
            console.log('从特殊key加载配置成功');
            return config;
        }

        console.log('没有找到任何音效配置');
        return null;
    }

    /**
     * 从localStorage加载配置
     */
    loadFromLocalStorage() {
        try {
            const configData = localStorage.getItem('launcher_audio_config');
            if (configData) {
                return JSON.parse(configData);
            }
        } catch (error) {
            console.warn('从localStorage加载配置失败:', error);
        }
        return null;
    }

    /**
     * 从文件加载配置
     */
    async loadFromFile() {
        try {
            const response = await fetch(`/${this.configFile}`);
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.warn('从文件加载配置失败:', error);
        }
        return null;
    }

    /**
     * 从备份加载配置
     */
    loadFromBackup() {
        try {
            const configData = localStorage.getItem(this.backupKey);
            if (configData) {
                return JSON.parse(configData);
            }
        } catch (error) {
            console.warn('从备份加载配置失败:', error);
        }
        return null;
    }

    /**
     * 从特殊key加载配置
     */
    loadFromSpecialKey() {
        try {
            const configData = localStorage.getItem('audio_config_for_launcher');
            if (configData) {
                return JSON.parse(configData);
            }
        } catch (error) {
            console.warn('从特殊key加载配置失败:', error);
        }
        return null;
    }

    /**
     * 应用配置到音效管理器
     */
    applyConfig(audioManager, config) {
        if (!audioManager || !config || !config.soundMap) {
            console.warn('无法应用配置：参数无效');
            return false;
        }

        try {
            console.log('应用音效配置:', config.soundMap);
            Object.assign(audioManager.soundMap, config.soundMap);
            console.log('音效配置应用成功');
            console.log('更新后的音效映射:', audioManager.soundMap);
            return true;
        } catch (error) {
            console.error('应用配置失败:', error);
            return false;
        }
    }

    /**
     * 清空所有配置
     */
    clearAllConfig() {
        localStorage.removeItem('launcher_audio_config');
        localStorage.removeItem(this.backupKey);
        localStorage.removeItem('audio_config_for_launcher');
        localStorage.removeItem('audioConfigTool_mapping');
        localStorage.removeItem('audioConfigTool_soundMap');
        console.log('所有音效配置已清空');
    }

    /**
     * 获取配置状态
     */
    getConfigStatus() {
        const status = {
            localStorage: !!localStorage.getItem('launcher_audio_config'),
            backup: !!localStorage.getItem(this.backupKey),
            special: !!localStorage.getItem('audio_config_for_launcher'),
            toolMapping: !!localStorage.getItem('audioConfigTool_mapping'),
            toolSoundMap: !!localStorage.getItem('audioConfigTool_soundMap')
        };

        console.log('配置状态:', status);
        return status;
    }
}

// 全局实例
window.audioConfigSync = new AudioConfigSync();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioConfigSync;
}
