# AI-Vision Installer Configuration Fix Test
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AI-Vision Installer - Configuration Fix Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔧 Configuration Fix Applied:" -ForegroundColor Yellow
Write-Host "  ✅ Added directory existence checks" -ForegroundColor Green
Write-Host "  ✅ Improved error handling in copyDirectory" -ForegroundColor Green
Write-Host "  ✅ Added safe file operations" -ForegroundColor Green
Write-Host "  ✅ Enhanced logging for debugging" -ForegroundColor Green
Write-Host "  ✅ Graceful handling of missing source files" -ForegroundColor Green
Write-Host ""

Write-Host "🧪 This test will focus on the configuration step that previously failed" -ForegroundColor Yellow
Write-Host ""

# Check environment
Write-Host "🔍 Environment Check:" -ForegroundColor Green
Write-Host "  Node.js: $(node --version)" -ForegroundColor White
Write-Host "  npm: $(npm --version)" -ForegroundColor White
Write-Host ""

# Check dependencies
if (!(Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "✅ Dependencies ready" -ForegroundColor Green
Write-Host ""

Write-Host "🚀 Starting Installer with Configuration Fix..." -ForegroundColor Green
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "                NOTICE" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🔧 Configuration step has been fixed:" -ForegroundColor Yellow
Write-Host "   - Better directory handling" -ForegroundColor White
Write-Host "   - Improved error messages" -ForegroundColor White
Write-Host "   - Safe file operations" -ForegroundColor White
Write-Host "   - Graceful fallback for missing files" -ForegroundColor White
Write-Host ""
Write-Host "📝 Watch the console for detailed logging" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Press Enter to start the installer..." -ForegroundColor Yellow
Read-Host

try {
    npm start
    Write-Host ""
    Write-Host "✅ Installer completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎉 Configuration step should now work properly" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "❌ Installer encountered an error" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 If the error persists:" -ForegroundColor Yellow
    Write-Host "1. Check the console output for detailed error messages" -ForegroundColor White
    Write-Host "2. Verify disk permissions" -ForegroundColor White
    Write-Host "3. Ensure sufficient disk space" -ForegroundColor White
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Configuration Fix Test Completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to exit"
