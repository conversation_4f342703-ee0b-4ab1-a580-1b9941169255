# 🎵 音效配置工具快速指南

## 🚀 快速开始

### 1. 启动工具
```bash
# 双击运行
start-audio-config.bat

# 或者直接运行
python audio-config-server.py
```

### 2. 访问界面
- 工具会自动打开浏览器
- 访问地址：http://localhost:8406/audio-config-tool.html
- 如果端口被占用，会自动切换到下一个端口

## 📁 上传音效文件

### 支持的格式
- **WAV**：推荐格式，高质量
- **MP3**：压缩格式，文件小
- **OGG**：开源格式

### 上传方式
1. **拖拽上传**：直接拖拽文件到上传区域
2. **点击上传**：点击上传区域选择文件
3. **批量上传**：可以同时选择多个文件

### 文件要求
- 最大文件大小：10MB
- 推荐时长：0.1-2秒（UI音效），2-5秒（系统音效）

## 🔧 配置音效映射

### 音效分类

#### UI交互音效（6种）
- **按钮点击**：普通按钮点击音效
- **主要按钮点击**：重要按钮点击音效
- **悬停音效**：鼠标悬停音效
- **切换操作**：开关、切换音效
- **输入聚焦**：输入框聚焦音效
- **标签页切换**：页面切换音效

#### 反馈音效（6种）
- **操作成功**：成功操作反馈
- **警告提示**：警告信息音效
- **错误提示**：错误信息音效
- **系统通知**：一般通知音效
- **确认操作**：确认对话框音效
- **任务完成**：任务完成音效

#### 系统音效（6种）
- **启动ComfyUI**：ComfyUI启动音效
- **ComfyUI启动成功**：启动成功反馈
- **关闭ComfyUI**：ComfyUI关闭音效
- **ComfyUI关闭成功**：关闭成功反馈
- **关闭启动器**：启动器关闭音效
- **加载过程**：数据加载音效

### 配置步骤
1. 为每种音效类型选择对应的音效文件
2. 点击"预览"按钮试听音效
3. 使用"预览配置"功能测试整体效果
4. 点击"保存配置"保存设置

## ⚙️ 控制功能

### 🎧 预览配置
- 按顺序播放所有配置的音效
- 每个音效间隔1.5秒
- 可以快速检查整体音效体验

### 💾 保存配置
- 将配置保存到本地存储
- 启动器下次启动时自动加载
- 当前会话中配置立即生效

### 📂 加载配置
- 加载之前保存的配置
- 自动应用到界面

### 🔄 重置配置
- 清空所有音效映射设置
- 不会删除已上传的文件

### 🗑️ 清空文件
- 删除所有上传的文件
- 保留系统默认文件

## 🎯 使用技巧

### 音效选择建议
1. **UI音效**：选择简短、清脆的音效
2. **成功音效**：选择愉悦、正面的音效
3. **警告音效**：选择引起注意但不刺耳的音效
4. **系统音效**：可以选择较长、有特色的音效

### 配置策略
1. **统一风格**：保持所有音效风格一致
2. **分类配置**：可以为同类音效使用相同文件
3. **重点突出**：为重要操作配置特色音效

### 性能优化
1. **文件大小**：尽量使用较小的文件
2. **格式选择**：WAV质量最好，MP3文件最小
3. **音量控制**：建议-12dB到-6dB

## 🚨 常见问题

### 文件上传失败
- 检查文件格式是否支持
- 确认文件大小不超过10MB
- 尝试使用不同的浏览器

### 音效不播放
- 检查浏览器音频权限
- 确认音量设置正确
- 尝试刷新页面重新加载

### 配置不生效
- 确认已点击保存配置
- 重启启动器应用程序
- 检查配置文件是否正确保存

## 📝 状态说明

### 上传状态
- **正在上传**：蓝色信息提示
- **上传成功**：绿色成功提示
- **上传失败**：红色错误提示

### 配置状态
- **配置保存成功**：绿色成功提示
- **配置加载成功**：绿色成功提示
- **配置重置**：蓝色信息提示

## 🔧 技术说明

### 文件存储
- 上传的文件保存在 `assets/sounds/custom/` 目录
- 配置信息保存在浏览器本地存储
- 启动器自动读取配置并应用

### 兼容性
- 支持现代浏览器（Chrome、Firefox、Edge）
- 需要浏览器支持Web Audio API
- 需要允许浏览器播放音频

### 安全性
- 只允许上传音频文件
- 文件大小限制防止滥用
- 本地服务器确保数据安全

---

**享受你的个性化音效体验！** 🎵✨
