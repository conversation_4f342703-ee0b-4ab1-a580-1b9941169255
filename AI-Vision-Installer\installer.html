<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Vision 智能安装器</title>
    <link href="../AI-Vision-Launcher/assets/fonts/local-fonts.css" rel="stylesheet">
    <link href="../AI-Vision-Launcher/assets/icons/font-awesome-subset.css" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #0a0a1a;
            --bg-secondary: #12122a;
            --bg-tertiary: #1a1a3a;
            --text-primary: #e0e0ff;
            --text-secondary: #a0a0d0;
            --accent-blue: #0088ff;
            --accent-cyan: #00e5ff;
            --accent-purple: #8855ff;
            --neon-glow: rgba(0, 245, 255, 0.7);
            --success-color: #00ff88;
            --warning-color: #ffcc00;
            --error-color: #ff3366;
            --card-bg: rgba(26, 26, 46, 0.8);
            --card-border: rgba(0, 245, 255, 0.3);
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Orbitron', 'Microsoft YaHei', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-image: 
                radial-gradient(circle at 10% 20%, rgba(0, 50, 100, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(100, 0, 100, 0.2) 0%, transparent 50%);
        }
        
        .installer-container {
            width: 90%;
            max-width: 900px;
            min-height: 600px;
            background-color: var(--bg-secondary);
            border-radius: 12px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.5), 0 0 15px var(--neon-glow);
            overflow: hidden;
            position: relative;
            border: 1px solid var(--card-border);
            display: flex;
            flex-direction: column;
        }
        
        .installer-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--neon-glow), transparent);
            z-index: 10;
        }
        
        .install-step {
            display: none;
            flex-direction: column;
            padding: 30px;
            height: 100%;
        }
        
        .install-step.active {
            display: flex;
        }
        
        .step-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .step-header h1, .step-header h2 {
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-blue));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 10px;
            font-weight: 700;
            letter-spacing: 1px;
        }
        
        .step-header p {
            color: var(--text-secondary);
            font-size: 16px;
        }
        
        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 30px 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 15px;
            background-color: var(--bg-tertiary);
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid var(--accent-cyan);
        }
        
        .feature-item i {
            color: var(--accent-cyan);
            font-size: 20px;
        }
        
        .step-actions {
            margin-top: auto;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            padding-top: 20px;
        }
        
        .btn-primary, .btn-secondary {
            padding: 12px 24px;
            border-radius: 6px;
            border: none;
            font-family: 'Orbitron', 'Microsoft YaHei', sans-serif;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, var(--accent-blue), var(--accent-cyan));
            color: #000;
            box-shadow: 0 0 10px rgba(0, 229, 255, 0.5);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 15px rgba(0, 229, 255, 0.7);
        }
        
        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .path-selector {
            margin: 20px 0;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .input-group input {
            flex: 1;
            padding: 12px 15px;
            border-radius: 6px;
            border: 1px solid var(--card-border);
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
            font-family: 'Consolas', monospace;
        }
        
        .input-group button {
            padding: 12px 15px;
            border-radius: 6px;
            border: 1px solid var(--card-border);
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
            cursor: pointer;
        }
        
        .path-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .info-item .label {
            color: var(--text-secondary);
        }
        
        .validation-info {
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
        }
        
        .validation-info.error {
            background-color: rgba(255, 51, 102, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }
        
        .validation-info.success {
            background-color: rgba(0, 255, 136, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }
        
        .progress-container {
            margin: 30px 0;
        }
        
        .progress-bar {
            height: 10px;
            background-color: var(--bg-tertiary);
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-cyan));
            width: 0%;
            transition: width 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 100%
            );
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .progress-text {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .install-details {
            margin-top: 20px;
            flex: 1;
            overflow-y: auto;
        }
        
        .detail-section {
            margin-bottom: 20px;
        }
        
        .detail-section h3 {
            margin-bottom: 10px;
            color: var(--accent-cyan);
            font-size: 16px;
        }
        
        .detail-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 6px;
            background-color: var(--bg-tertiary);
        }
        
        .detail-item i {
            font-size: 14px;
        }
        
        .detail-item.pending i {
            color: var(--text-secondary);
        }
        
        .detail-item.active i {
            color: var(--accent-cyan);
            animation: pulse 1.5s infinite;
        }
        
        .detail-item.completed i {
            color: var(--success-color);
        }
        
        .detail-item.error i {
            color: var(--error-color);
        }
        
        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }
        
        .completion-info {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-card {
            background-color: var(--bg-tertiary);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--card-border);
        }
        
        .info-card h3 {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            color: var(--accent-cyan);
        }
        
        .info-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .next-steps {
            background-color: var(--bg-tertiary);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid var(--card-border);
        }
        
        .next-steps h3 {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            color: var(--accent-cyan);
        }
        
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--accent-cyan);
        }
        
        .success {
            color: var(--success-color);
        }
        
        .warning {
            color: var(--warning-color);
        }
        
        .error {
            color: var(--error-color);
        }
    </style>
</head>
<body>
    <div class="installer-container">
        <!-- 步骤1: 欢迎页面 -->
        <div id="welcome-step" class="install-step active">
            <div class="step-header">
                <h1><i class="fas fa-rocket"></i> AI-Vision 智能安装器</h1>
                <p>一键安装 ComfyUI + AI-Vision 启动器完整环境</p>
            </div>
            
            <div class="feature-list">
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>自动配置 Python 3.12.9 便携环境</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>安装 PyTorch 2.7.1 + CUDA 12.8</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>智能镜像切换，加速下载</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>完整的卸载功能</span>
                </div>
            </div>
            
            <div class="step-actions">
                <button class="btn-primary" onclick="nextStep()"><i class="fas fa-play"></i> 开始安装</button>
                <button class="btn-secondary" onclick="window.close()"><i class="fas fa-times"></i> 退出</button>
            </div>
        </div>
        
        <!-- 步骤2: 路径选择 -->
        <div id="path-step" class="install-step">
            <div class="step-header">
                <h2><i class="fas fa-folder"></i> 选择安装路径</h2>
                <p>请选择 AI-Vision 的安装位置</p>
            </div>
            
            <div class="path-selector">
                <div class="input-group">
                    <input type="text" id="install-path" value="C:\AI-Vision" />
                    <button onclick="browsePath()"><i class="fas fa-folder-open"></i> 浏览</button>
                </div>
                
                <div class="path-info">
                    <div class="info-item">
                        <span class="label">所需空间:</span>
                        <span class="value">约 50GB</span>
                    </div>
                    <div class="info-item">
                        <span class="label">可用空间:</span>
                        <span class="value" id="available-space">计算中...</span>
                    </div>
                </div>
                
                <div id="path-validation" class="validation-info"></div>
            </div>
            
            <div class="step-actions">
                <button class="btn-secondary" onclick="prevStep()"><i class="fas fa-arrow-left"></i> 上一步</button>
                <button class="btn-primary" onclick="validateAndNext()" id="path-next-btn"><i class="fas fa-arrow-right"></i> 下一步</button>
            </div>
        </div>
        
        <!-- 步骤3: 安装进度 -->
        <div id="install-step" class="install-step">
            <div class="step-header">
                <h2><i class="fas fa-cog fa-spin"></i> 正在安装</h2>
                <p id="install-status">准备安装...</p>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-text">
                    <span id="current-step">环境检测中...</span>
                    <span id="progress-percent">0%</span>
                </div>
            </div>
            
            <div class="install-details">
                <div class="detail-section">
                    <h3>安装进度</h3>
                    <div class="detail-list" id="install-progress-list">
                        <div class="detail-item pending" data-step="environment">
                            <i class="fas fa-circle"></i>
                            <span>环境检测</span>
                        </div>
                        <div class="detail-item pending" data-step="download-python">
                            <i class="fas fa-circle"></i>
                            <span>下载 Python 3.12.9</span>
                        </div>
                        <div class="detail-item pending" data-step="extract-python">
                            <i class="fas fa-circle"></i>
                            <span>配置 Python 环境</span>
                        </div>
                        <div class="detail-item pending" data-step="download-comfyui">
                            <i class="fas fa-circle"></i>
                            <span>下载 ComfyUI</span>
                        </div>
                        <div class="detail-item pending" data-step="extract-comfyui">
                            <i class="fas fa-circle"></i>
                            <span>配置 ComfyUI</span>
                        </div>
                        <div class="detail-item pending" data-step="install-pytorch">
                            <i class="fas fa-circle"></i>
                            <span>安装 PyTorch 2.7.1</span>
                        </div>
                        <div class="detail-item pending" data-step="install-dependencies">
                            <i class="fas fa-circle"></i>
                            <span>安装依赖包</span>
                        </div>
                        <div class="detail-item pending" data-step="configure">
                            <i class="fas fa-circle"></i>
                            <span>配置环境</span>
                        </div>
                        <div class="detail-item pending" data-step="finalize">
                            <i class="fas fa-circle"></i>
                            <span>完成安装</span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3>安装日志</h3>
                    <div id="install-log" style="background-color: var(--bg-tertiary); padding: 10px; border-radius: 6px; font-family: 'Consolas', monospace; font-size: 12px; height: 200px; overflow-y: auto;">
                        <!-- 安装日志将在这里显示 -->
                    </div>
                </div>
            </div>
            
            <div class="step-actions">
                <button class="btn-secondary" onclick="cancelInstall()" id="cancel-btn"><i class="fas fa-times"></i> 取消安装</button>
            </div>
        </div>
        
        <!-- 步骤4: 安装完成 -->
        <div id="complete-step" class="install-step">
            <div class="step-header">
                <h2><i class="fas fa-check-circle success"></i> 安装完成</h2>
                <p>AI-Vision 已成功安装到您的计算机</p>
            </div>
            
            <div class="completion-info">
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> 安装信息</h3>
                    <div class="info-list">
                        <div class="info-item">
                            <span class="label">安装路径:</span>
                            <span class="value" id="final-install-path"></span>
                        </div>
                        <div class="info-item">
                            <span class="label">Python版本:</span>
                            <span class="value">3.12.9 (便携式)</span>
                        </div>
                        <div class="info-item">
                            <span class="label">PyTorch版本:</span>
                            <span class="value">2.7.1+cu128</span>
                        </div>
                        <div class="info-item">
                            <span class="label">TorchVision版本:</span>
                            <span class="value">0.22.1+cu128</span>
                        </div>
                        <div class="info-item">
                            <span class="label">CUDA版本:</span>
                            <span class="value">12.8</span>
                        </div>
                        <div class="info-item">
                            <span class="label">ComfyUI版本:</span>
                            <span class="value">最新稳定版</span>
                        </div>
                        <div class="info-item">
                            <span class="label">占用空间:</span>
                            <span class="value" id="used-space">计算中...</span>
                        </div>
                    </div>
                </div>
                
                <div class="next-steps">
                    <h3><i class="fas fa-play-circle"></i> 下一步操作</h3>
                    <div class="checkbox-group">
                        <label>
                            <input type="checkbox" id="launch-now" checked>
                            <span>立即启动 AI-Vision 启动器</span>
                        </label>
                        <label>
                            <input type="checkbox" id="create-desktop-shortcut" checked>
                            <span>创建桌面快捷方式</span>
                        </label>
                        <label>
                            <input type="checkbox" id="add-to-start-menu" checked>
                            <span>添加到开始菜单</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="step-actions">
                <button class="btn-primary" onclick="finishInstall()"><i class="fas fa-check"></i> 完成</button>
                <button class="btn-secondary" onclick="openInstallFolder()"><i class="fas fa-folder-open"></i> 打开安装目录</button>
            </div>
        </div>
    </div>
    
    <script src="installer-config.js"></script>
    <script src="installer-main.js"></script>
</body>
</html>
