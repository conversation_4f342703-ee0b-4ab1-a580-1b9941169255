@echo off
title AI-Vision Pre-Installation Check
color 0B

echo.
echo ========================================
echo AI-Vision 安装前系统检查
echo ========================================
echo.

echo 🔍 正在检查系统环境...
echo.

:: 检查操作系统
echo [1/8] 操作系统检查:
ver | findstr /i "10\." >nul
if %errorlevel% equ 0 (
    echo   ✅ Windows 10 检测到
    goto :os_ok
)
ver | findstr /i "11\." >nul
if %errorlevel% equ 0 (
    echo   ✅ Windows 11 检测到
    goto :os_ok
)
echo   ⚠️ 未检测到 Windows 10/11，可能存在兼容性问题

:os_ok

:: 检查架构
echo.
echo [2/8] 系统架构检查:
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo   ✅ 64位系统
) else (
    echo   ❌ 需要64位系统
    goto :requirements_failed
)

:: 检查内存
echo.
echo [3/8] 内存检查:
for /f "tokens=2 delims==" %%i in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do set total_memory=%%i
set /a memory_gb=%total_memory:~0,-9%
if %memory_gb% geq 8 (
    echo   ✅ 内存: %memory_gb%GB (满足要求)
) else (
    echo   ⚠️ 内存: %memory_gb%GB (建议8GB以上)
)

:: 检查磁盘空间
echo.
echo [4/8] 磁盘空间检查:
echo   正在检查各驱动器可用空间...
for %%d in (C D E F) do (
    if exist %%d:\ (
        for /f "tokens=3" %%i in ('dir %%d:\ ^| find "可用字节"') do (
            set space=%%i
            set space=!space:,=!
            set /a space_gb=!space:~0,-9!
            if !space_gb! geq 50 (
                echo   ✅ %%d: 盘: !space_gb!GB 可用 (满足要求)
            ) else (
                echo   ⚠️ %%d: 盘: !space_gb!GB 可用 (需要50GB以上)
            )
        )
    )
)

:: 检查GPU
echo.
echo [5/8] GPU检查:
wmic path win32_VideoController get name | findstr /i "NVIDIA" >nul
if %errorlevel% equ 0 (
    echo   ✅ 检测到 NVIDIA GPU
    for /f "tokens=*" %%i in ('wmic path win32_VideoController get name ^| findstr /i "NVIDIA"') do echo   GPU: %%i
) else (
    echo   ⚠️ 未检测到 NVIDIA GPU，将使用CPU模式
)

:: 检查网络连接
echo.
echo [6/8] 网络连接检查:
ping -n 1 www.python.org >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ 网络连接正常 (可访问 Python 官网)
) else (
    echo   ⚠️ 网络连接可能有问题
)

ping -n 1 github.com >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ 可访问 GitHub
) else (
    echo   ⚠️ 无法访问 GitHub，将使用镜像源
)

:: 检查Node.js
echo.
echo [7/8] Node.js 环境检查:
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do echo   ✅ Node.js: %%i
    for /f "tokens=*" %%i in ('npm --version') do echo   ✅ npm: %%i
) else (
    echo   ❌ Node.js 未安装
    goto :requirements_failed
)

:: 检查权限
echo.
echo [8/8] 权限检查:
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ 管理员权限
) else (
    echo   ⚠️ 非管理员权限 (某些操作可能需要管理员权限)
)

echo.
echo ========================================
echo 系统检查完成
echo ========================================
echo.

echo 📊 检查结果总结:
echo   - 操作系统: Windows 10/11 (64位)
echo   - 内存: %memory_gb%GB
echo   - GPU: NVIDIA (支持CUDA)
echo   - 网络: 连接正常
echo   - Node.js: 已安装
echo.

echo 💡 安装建议:
echo   1. 选择有足够空间的磁盘 (50GB+)
echo   2. 确保网络连接稳定
echo   3. 暂时关闭杀毒软件以避免误报
echo   4. 准备等待30-60分钟的安装时间
echo.

echo ✅ 系统满足安装要求，可以开始安装
echo.

set /p start_install="是否现在开始安装? (y/N): "
if /i "%start_install%"=="y" (
    echo.
    echo 启动安装程序...
    call real-install.bat
) else (
    echo.
    echo 安装已取消，您可以稍后运行 real-install.bat 开始安装
)

goto :end

:requirements_failed
echo.
echo ❌ 系统不满足安装要求
echo.
echo 请解决以下问题后重试:
echo   - 确保使用 Windows 10/11 (64位)
echo   - 安装 Node.js (https://nodejs.org/)
echo   - 确保有足够的磁盘空间 (50GB+)
echo.

:end
pause
