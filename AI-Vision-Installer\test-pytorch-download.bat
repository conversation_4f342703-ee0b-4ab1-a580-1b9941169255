@echo off
title AI-Vision PyTorch Download Fix Test
color 0F

echo.
echo ========================================
echo AI-Vision PyTorch Download Fix Test
echo ========================================
echo.

echo PyTorch download improvements:
echo - Extended timeout to 30 minutes
echo - Added multiple mirror sources
echo - Enhanced progress reporting
echo - Automatic fallback to different sources
echo - Better error handling and retry logic
echo.

echo Download sources (in order):
echo 1. Official PyTorch (CUDA 12.8)
echo 2. Tsinghua Mirror (China)
echo 3. Default PyPI (fallback)
echo.

echo Expected behavior:
echo - Will try official source first
echo - If slow/fails, auto-switch to Tsinghua mirror
echo - Shows "downloading..." messages during process
echo - May take 10-30 minutes for PyTorch (3.5GB)
echo.

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit
)

echo Environment ready
echo.

if not exist "node_modules" (
    echo Installing dependencies...
    npm install
)

echo.
echo ========================================
echo PYTORCH DOWNLOAD TEST
echo ========================================
echo.

echo IMPORTANT NOTES:
echo 1. PyTorch files are VERY LARGE (3.5GB total)
echo 2. Download may appear "stuck" but is actually working
echo 3. Check console for "downloading..." messages
echo 4. Be patient - can take 10-30 minutes
echo 5. Installer will auto-retry with different sources
echo.

echo Network tips:
echo - Ensure stable internet connection
echo - Close other download applications
echo - Consider using wired connection if possible
echo.

set /p start="Start PyTorch download test? (y/N): "
if /i not "%start%"=="y" (
    echo Test cancelled
    pause
    exit
)

echo.
echo Starting installer...
echo Watch for download progress messages in the console
echo.

npm start

echo.
echo Test completed
echo.
echo If download was slow or failed:
echo 1. Check your internet connection speed
echo 2. Try again during off-peak hours
echo 3. Consider using mobile hotspot if home internet is slow
echo 4. The installer automatically tries multiple sources
echo.

pause
