@echo off
title AI-Vision Real Installation
color 0E

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                  AI-Vision 真实安装测试                      ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo ⚠️  重要提示:
echo   - 这将进行真实的安装，会下载实际文件
echo   - 需要稳定的网络连接
echo   - 安装过程可能需要30-60分钟
echo   - 将下载约4-5GB的文件
echo.

echo 📋 系统要求检查:
echo   - Windows 10/11 (64位): ✅
echo   - 内存 8GB+: ✅ 
echo   - 磁盘空间 50GB+: 需要确认
echo   - NVIDIA GPU: ✅ (RTX 4070 Ti SUPER)
echo   - 网络连接: 需要确认
echo.

echo 🔍 环境检查:
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未找到
    echo 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do echo   ✅ Node.js: %%i
for /f "tokens=*" %%i in ('npm --version') do echo   ✅ npm: %%i
echo.

echo 📦 检查依赖:
if not exist "node_modules" (
    echo 正在安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)
echo   ✅ 依赖包已准备

echo.
echo 🎯 建议的安装设置:
echo   - 安装路径: D:\AI-Vision-Test (或您选择的路径)
echo   - 确保选择的磁盘有足够空间
echo   - 安装完成后可以测试启动器功能
echo.

echo ⏰ 预计安装时间:
echo   - 下载 Python: 2-5分钟
echo   - 下载 ComfyUI: 3-8分钟  
echo   - 安装 PyTorch: 10-20分钟
echo   - 安装依赖包: 5-15分钟
echo   - 配置环境: 2-5分钟
echo   总计: 约30-60分钟 (取决于网络速度)
echo.

echo 🌐 下载源:
echo   - Python: 官方源 + 国内镜像
echo   - PyTorch: 官方源 + 清华镜像  
echo   - ComfyUI: GitHub + 代理镜像
echo   (安装器会自动选择最快的源)
echo.

set /p confirm="确定要开始真实安装吗? (y/N): "
if /i not "%confirm%"=="y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始真实安装...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        注意事项                              ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ 1. 请保持网络连接稳定                                       ║
echo ║ 2. 不要关闭安装器窗口                                       ║
echo ║ 3. 安装过程中可能会有杀毒软件提示，请允许                   ║
echo ║ 4. 如果下载速度慢，安装器会自动切换镜像                     ║
echo ║ 5. 安装完成后会自动创建桌面快捷方式                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 按任意键开始安装...
pause >nul

echo 启动安装器...
npm start

if %errorlevel% equ 0 (
    echo.
    echo 🎉 安装完成!
    echo.
    echo 📁 安装位置: 您在安装器中选择的路径
    echo 🚀 可以通过桌面快捷方式启动 AI-Vision 启动器
    echo 📖 查看安装目录中的说明文档了解使用方法
    echo.
    echo 🧪 建议测试:
    echo   1. 启动 AI-Vision 启动器
    echo   2. 测试 ComfyUI 启动功能
    echo   3. 检查插件管理功能
    echo   4. 验证后端服务连接
    echo.
) else (
    echo.
    echo ❌ 安装过程中出现错误
    echo.
    echo 🔍 可能的原因:
    echo   - 网络连接中断
    echo   - 磁盘空间不足
    echo   - 杀毒软件阻止
    echo   - 权限不足
    echo.
    echo 💡 解决建议:
    echo   1. 检查网络连接
    echo   2. 确保磁盘空间充足
    echo   3. 暂时关闭杀毒软件
    echo   4. 以管理员身份运行
    echo   5. 重新尝试安装
    echo.
)

echo.
echo 安装测试完成
pause
