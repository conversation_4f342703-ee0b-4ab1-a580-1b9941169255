/**
 * 关键错误捕获和处理模块
 * 提供全局错误处理、网络错误恢复和应用程序健康监控
 */

class ErrorHandler {
    constructor() {
        this.errorCount = 0;
        this.maxErrors = 10; // 最大错误数量阈值
        this.errorHistory = [];
        this.isRecovering = false;
        this.networkRetryCount = 0;
        this.maxNetworkRetries = 3;
        
        this.setupGlobalErrorHandling();
        this.setupNetworkErrorHandling();
        this.setupPerformanceMonitoring();
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 捕获JavaScript运行时错误
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                source: event.filename,
                line: event.lineno,
                column: event.colno,
                error: event.error,
                stack: event.error?.stack
            });
        });

        // 捕获Promise未处理的拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || '未处理的Promise拒绝',
                reason: event.reason,
                stack: event.reason?.stack
            });
            
            // 防止默认的控制台错误输出
            event.preventDefault();
        });

        // 捕获资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target && event.target !== window) {
                this.handleError({
                    type: 'resource',
                    message: `资源加载失败: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    source: event.target.src || event.target.href
                });
            }
        }, true);
    }

    /**
     * 设置网络错误处理
     */
    setupNetworkErrorHandling() {
        // 监听网络状态变化
        window.addEventListener('online', () => {
            console.log('网络连接已恢复');
            this.handleNetworkRecovery();
        });

        window.addEventListener('offline', () => {
            console.log('网络连接已断开');
            this.handleNetworkError('网络连接断开');
        });

        // 拦截fetch请求并添加错误处理
        this.wrapFetchWithErrorHandling();
    }

    /**
     * 包装fetch请求添加错误处理
     */
    wrapFetchWithErrorHandling() {
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch.apply(window, args);
                
                // 重置网络重试计数
                if (response.ok) {
                    this.networkRetryCount = 0;
                }
                
                // 处理HTTP错误状态
                if (!response.ok) {
                    this.handleNetworkError(`HTTP ${response.status}: ${response.statusText}`, args[0]);
                }
                
                return response;
            } catch (error) {
                return this.handleFetchError(error, ...args);
            }
        };
    }

    /**
     * 处理fetch错误
     */
    async handleFetchError(error, url, options = {}) {
        console.error('Fetch错误:', error);
        
        // 检查是否为网络连接问题
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return this.retryFetchWithBackoff(url, options, error);
        }
        
        // 记录其他类型的错误
        this.handleError({
            type: 'network',
            message: `网络请求失败: ${error.message}`,
            url: url,
            error: error
        });
        
        throw error;
    }

    /**
     * 使用指数退避重试fetch请求
     */
    async retryFetchWithBackoff(url, options, originalError) {
        if (this.networkRetryCount >= this.maxNetworkRetries) {
            this.handleError({
                type: 'network',
                message: `网络请求重试失败，已达到最大重试次数 (${this.maxNetworkRetries})`,
                url: url,
                error: originalError
            });
            throw originalError;
        }

        this.networkRetryCount++;
        const delay = Math.pow(2, this.networkRetryCount) * 1000; // 指数退避：2^n秒
        
        console.log(`网络请求重试 ${this.networkRetryCount}/${this.maxNetworkRetries}，${delay/1000}秒后重试`);
        
        await this.delay(delay);
        
        try {
            const response = await fetch(url, options);
            this.networkRetryCount = 0; // 成功后重置计数
            return response;
        } catch (retryError) {
            return this.retryFetchWithBackoff(url, options, originalError);
        }
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        // 监控页面性能
        if ('PerformanceObserver' in window) {
            // 监控长任务
            try {
                const longTaskObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.duration > 50) { // 超过50ms的任务
                            this.handlePerformanceIssue({
                                type: 'long-task',
                                duration: entry.duration,
                                startTime: entry.startTime
                            });
                        }
                    }
                });
                longTaskObserver.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                console.log('长任务监控不支持:', e.message);
            }

            // 监控布局偏移
            try {
                const clsObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.value > 0.1) { // CLS > 0.1
                            this.handlePerformanceIssue({
                                type: 'layout-shift',
                                value: entry.value,
                                sources: entry.sources
                            });
                        }
                    }
                });
                clsObserver.observe({ entryTypes: ['layout-shift'] });
            } catch (e) {
                console.log('布局偏移监控不支持:', e.message);
            }
        }

        // 监控内存使用情况
        setInterval(() => {
            this.checkMemoryUsage();
        }, 30000); // 每30秒检查一次
    }

    /**
     * 检查内存使用情况
     */
    checkMemoryUsage() {
        if ('memory' in performance) {
            const memory = performance.memory;
            const usedPercent = (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100;
            
            if (usedPercent > 80) { // 内存使用超过80%
                this.handlePerformanceIssue({
                    type: 'high-memory',
                    usedPercent: usedPercent,
                    usedMB: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                    totalMB: Math.round(memory.totalJSHeapSize / 1024 / 1024)
                });
            }
        }
    }

    /**
     * 处理错误
     */
    handleError(errorInfo) {
        this.errorCount++;
        this.errorHistory.push({
            ...errorInfo,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        });

        // 保持错误历史记录在合理大小
        if (this.errorHistory.length > 50) {
            this.errorHistory = this.errorHistory.slice(-30);
        }

        // 输出到控制台（开发模式）
        console.error('[ErrorHandler]', errorInfo);

        // 检查是否需要降级模式
        if (this.errorCount > this.maxErrors && !this.isRecovering) {
            this.enterRecoveryMode();
        }

        // 尝试通知用户（非关键错误时静默处理）
        if (this.shouldNotifyUser(errorInfo)) {
            this.notifyUser(errorInfo);
        }

        // 尝试自动恢复
        this.attemptAutoRecovery(errorInfo);
    }

    /**
     * 处理网络错误
     */
    handleNetworkError(message, url = '') {
        console.warn('网络错误:', message, url);
        
        if (window.launcherInstance && typeof window.launcherInstance.showNotification === 'function') {
            window.launcherInstance.showNotification(
                `⚠️ 网络连接问题: ${message}`,
                'warning'
            );
        }
    }

    /**
     * 处理网络恢复
     */
    handleNetworkRecovery() {
        this.networkRetryCount = 0;
        
        if (window.launcherInstance && typeof window.launcherInstance.showNotification === 'function') {
            window.launcherInstance.showNotification(
                '✅ 网络连接已恢复',
                'success'
            );
        }
    }

    /**
     * 处理性能问题
     */
    handlePerformanceIssue(performanceInfo) {
        console.warn('[性能警告]', performanceInfo);
        
        // 记录性能问题
        this.errorHistory.push({
            type: 'performance',
            ...performanceInfo,
            timestamp: new Date().toISOString()
        });

        // 对于严重的性能问题，触发优化措施
        if (performanceInfo.type === 'high-memory' && performanceInfo.usedPercent > 90) {
            this.triggerMemoryOptimization();
        }
    }

    /**
     * 触发内存优化
     */
    triggerMemoryOptimization() {
        console.log('触发紧急内存优化');
        
        // 调用内存优化器的清理功能
        if (window.memoryOptimizer) {
            window.memoryOptimizer.performMaintenance();
        }
        
        // 强制垃圾回收（如果可用）
        if (window.gc) {
            window.gc();
        }
        
        // 通知启动器执行清理
        if (window.launcherInstance && typeof window.launcherInstance.performMemoryMaintenance === 'function') {
            window.launcherInstance.performMemoryMaintenance();
        }
    }

    /**
     * 判断是否需要通知用户
     */
    shouldNotifyUser(errorInfo) {
        // 对于关键错误或频繁错误才通知用户
        return errorInfo.type === 'network' || 
               errorInfo.type === 'resource' ||
               this.errorCount > 5;
    }

    /**
     * 通知用户错误
     */
    notifyUser(errorInfo) {
        if (window.launcherInstance && typeof window.launcherInstance.showNotification === 'function') {
            let message = '应用程序遇到问题';
            
            switch (errorInfo.type) {
                case 'network':
                    message = '网络连接出现问题，请检查网络设置';
                    break;
                case 'resource':
                    message = '资源加载失败，请刷新页面重试';
                    break;
                case 'javascript':
                    message = '应用程序运行出错，请联系技术支持';
                    break;
                default:
                    message = `遇到${errorInfo.type}错误，请重试`;
            }
            
            window.launcherInstance.showNotification(`⚠️ ${message}`, 'error');
        }
    }

    /**
     * 尝试自动恢复
     */
    attemptAutoRecovery(errorInfo) {
        switch (errorInfo.type) {
            case 'resource':
                // 对于资源加载失败，尝试重新加载
                this.retryResourceLoad(errorInfo);
                break;
                
            case 'network':
                // 对于网络错误，已在fetch包装中处理重试
                break;
                
            case 'javascript':
                // 对于JavaScript错误，尝试重新初始化相关功能
                this.attemptFunctionRecovery(errorInfo);
                break;
        }
    }

    /**
     * 重试资源加载
     */
    retryResourceLoad(errorInfo) {
        if (errorInfo.element && errorInfo.source) {
            console.log('尝试重新加载资源:', errorInfo.source);
            
            setTimeout(() => {
                const element = document.querySelector(`${errorInfo.element.toLowerCase()}[src="${errorInfo.source}"], ${errorInfo.element.toLowerCase()}[href="${errorInfo.source}"]`);
                if (element) {
                    if (element.src) {
                        element.src = errorInfo.source + '?retry=' + Date.now();
                    } else if (element.href) {
                        element.href = errorInfo.source + '?retry=' + Date.now();
                    }
                }
            }, 2000);
        }
    }

    /**
     * 尝试功能恢复
     */
    attemptFunctionRecovery(errorInfo) {
        // 对于插件相关的错误，尝试重新初始化插件管理
        if (errorInfo.message && errorInfo.message.includes('plugin')) {
            console.log('尝试恢复插件管理功能');
            
            setTimeout(() => {
                if (window.launcherInstance && typeof window.launcherInstance.initPluginManagement === 'function') {
                    try {
                        window.launcherInstance.initPluginManagement();
                    } catch (e) {
                        console.error('插件管理恢复失败:', e);
                    }
                }
            }, 3000);
        }
    }

    /**
     * 进入恢复模式
     */
    enterRecoveryMode() {
        this.isRecovering = true;
        console.warn('应用程序进入恢复模式');
        
        // 通知用户进入恢复模式
        if (window.launcherInstance && typeof window.launcherInstance.showNotification === 'function') {
            window.launcherInstance.showNotification(
                '🔧 检测到多个错误，正在尝试自动修复...',
                'warning'
            );
        }
        
        // 执行恢复操作
        this.performRecoveryActions();
        
        // 5分钟后退出恢复模式
        setTimeout(() => {
            this.exitRecoveryMode();
        }, 5 * 60 * 1000);
    }

    /**
     * 执行恢复操作
     */
    performRecoveryActions() {
        // 清理缓存
        if (window.launcherInstance) {
            try {
                window.launcherInstance.performMemoryMaintenance();
            } catch (e) {
                console.error('内存维护失败:', e);
            }
        }
        
        // 触发内存优化
        this.triggerMemoryOptimization();
        
        // 重置错误计数
        this.errorCount = Math.floor(this.errorCount / 2);
    }

    /**
     * 退出恢复模式
     */
    exitRecoveryMode() {
        this.isRecovering = false;
        this.errorCount = 0;
        console.log('退出恢复模式');
        
        if (window.launcherInstance && typeof window.launcherInstance.showNotification === 'function') {
            window.launcherInstance.showNotification(
                '✅ 应用程序已恢复正常运行',
                'success'
            );
        }
    }

    /**
     * 获取错误统计信息
     */
    getErrorStats() {
        const stats = {
            totalErrors: this.errorCount,
            recentErrors: this.errorHistory.slice(-10),
            errorsByType: {},
            isRecovering: this.isRecovering
        };
        
        // 按类型统计错误
        this.errorHistory.forEach(error => {
            stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1;
        });
        
        return stats;
    }

    /**
     * 清除错误历史
     */
    clearErrorHistory() {
        this.errorHistory = [];
        this.errorCount = 0;
        console.log('错误历史已清除');
    }
}

// 创建全局错误处理器实例
window.errorHandler = new ErrorHandler();

console.log('关键错误捕获系统已初始化');