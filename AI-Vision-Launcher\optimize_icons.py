#!/usr/bin/env python3
"""
优化图标显示质量 - 确保所有图标都基于512x512高分辨率源
"""

from PIL import Image, ImageDraw, ImageFont
import os

def optimize_all_icons():
    """优化所有图标文件，确保最佳显示质量"""
    print("开始优化图标显示质量...")
    
    # 加载512x512的高分辨率源图标
    source_icon_path = os.path.join("assets", "icon.png")
    
    if not os.path.exists(source_icon_path):
        print(f"错误：找不到源图标文件 {source_icon_path}")
        return False
    
    try:
        # 加载高分辨率源图标
        source_icon = Image.open(source_icon_path)
        print(f"加载源图标: {source_icon_path} ({source_icon.size})")
        
        if source_icon.mode != 'RGBA':
            source_icon = source_icon.convert('RGBA')
        
        # 确保源图标是512x512
        if source_icon.size != (512, 512):
            print(f"调整源图标尺寸从 {source_icon.size} 到 (512, 512)")
            source_icon = source_icon.resize((512, 512), Image.Resampling.LANCZOS)
            source_icon.save(source_icon_path, "PNG")
        
        # 创建各种尺寸的优化图标
        icon_configs = [
            # (尺寸, 文件名, 描述, 优化选项)
            (16, "icon-16.png", "16x16 小图标", {"optimize": True}),
            (24, "icon-24.png", "24x24 小图标", {"optimize": True}),
            (32, "icon-32.png", "32x32 标准图标", {"optimize": True}),
            (48, "icon-48.png", "48x48 任务栏图标", {"optimize": True}),
            (64, "icon-64.png", "64x64 中等图标", {"optimize": True}),
            (96, "icon-96.png", "96x96 大图标", {"optimize": True}),
            (128, "icon-128.png", "128x128 大图标", {"optimize": True}),
            (256, "icon-256.png", "256x256 高清图标", {"optimize": True}),
            
            # 专用图标
            (32, "tray-icon.png", "系统托盘图标", {"optimize": True}),
            (48, "taskbar-icon.png", "任务栏图标", {"optimize": True}),
            (256, "desktop-icon.png", "桌面快捷方式图标", {"optimize": True}),
        ]
        
        print("\n生成优化图标:")
        for size, filename, description, options in icon_configs:
            # 使用高质量重采样算法
            resized_icon = source_icon.resize((size, size), Image.Resampling.LANCZOS)
            
            # 对小尺寸图标进行锐化处理
            if size <= 48:
                # 小尺寸图标需要更清晰的边缘
                from PIL import ImageFilter
                resized_icon = resized_icon.filter(ImageFilter.SHARPEN)
            
            # 保存图标
            icon_path = os.path.join("assets", filename)
            resized_icon.save(icon_path, "PNG", **options)
            print(f"  ✓ {description}: {icon_path} ({size}x{size})")
        
        # 重新生成ICO文件，包含所有尺寸
        print("\n重新生成ICO文件:")
        ico_sizes = [16, 24, 32, 48, 64, 96, 128, 256]
        ico_icons = []
        
        for size in ico_sizes:
            resized = source_icon.resize((size, size), Image.Resampling.LANCZOS)
            if size <= 48:
                # 小尺寸图标锐化
                from PIL import ImageFilter
                resized = resized.filter(ImageFilter.SHARPEN)
            ico_icons.append(resized)
        
        # 保存ICO文件
        ico_path = os.path.join("assets", "icon.ico")
        ico_icons[0].save(ico_path, format='ICO', sizes=[(s, s) for s in ico_sizes])
        print(f"  ✓ 多尺寸ICO文件: {ico_path} (包含{len(ico_sizes)}个尺寸)")
        
        print(f"\n✅ 图标优化完成！")
        print("所有图标都基于512x512高分辨率源，确保最佳显示质量。")
        
        return True
        
    except Exception as e:
        print(f"优化图标时出错: {e}")
        return False

def main():
    """主函数"""
    print("AI视界启动器 - 图标显示质量优化工具")
    print("=" * 50)
    
    success = optimize_all_icons()
    
    if success:
        print("\n🎉 优化完成！重启应用以查看效果。")
    else:
        print("\n❌ 优化失败，请检查错误信息。")

if __name__ == "__main__":
    main()
