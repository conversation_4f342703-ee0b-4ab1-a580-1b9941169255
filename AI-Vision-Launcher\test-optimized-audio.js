/**
 * 优化后音效方案测试脚本
 * 在启动器控制台中运行此脚本来测试音效优化效果
 */

console.log('🎵 开始测试优化后的音效方案...');

// 测试音效配置
const testAudioConfig = () => {
    console.log('=== 音效配置检查 ===');
    console.log('当前音效映射:', window.audioManager?.soundMap);
    
    // 检查关键音效配置
    const expectedConfig = {
        'click': 'custom/按钮科技音效.WAV',
        'click-primary': 'custom/按钮科技音效.WAV',
        'tab-switch': 'custom/导航标签点击的声音.WAV',
        'switch': 'custom/导航标签点击的声音.WAV',
        'success': 'custom/任务完成音效.WAV',
        'complete': 'custom/操作成功反馈音效.WAV',
        'warning': 'custom/提醒、警告音效.WAV'
    };
    
    let configCorrect = true;
    for (const [key, expected] of Object.entries(expectedConfig)) {
        const actual = window.audioManager?.soundMap?.[key];
        if (actual === expected) {
            console.log(`✅ ${key}: ${actual}`);
        } else {
            console.log(`❌ ${key}: 期望 ${expected}, 实际 ${actual}`);
            configCorrect = false;
        }
    }
    
    return configCorrect;
};

// 测试音效播放
const testAudioPlayback = async () => {
    console.log('=== 音效播放测试 ===');
    
    const testSounds = [
        { name: 'click', description: '普通按钮点击 (按钮科技音效)' },
        { name: 'click-primary', description: '主要按钮点击 (按钮科技音效)' },
        { name: 'tab-switch', description: '标签页切换 (导航标签音效)' },
        { name: 'switch', description: '开关切换 (导航标签音效)' },
        { name: 'success', description: '操作成功 (任务完成音效)' },
        { name: 'complete', description: '任务完成 (操作成功反馈音效)' },
        { name: 'warning', description: '警告提示 (提醒警告音效)' }
    ];
    
    for (let i = 0; i < testSounds.length; i++) {
        const sound = testSounds[i];
        console.log(`🔊 播放 ${sound.name}: ${sound.description}`);
        
        if (window.audioManager) {
            window.audioManager.play(sound.name);
        } else {
            console.error('音效管理器未初始化');
            break;
        }
        
        // 等待1.5秒再播放下一个音效
        if (i < testSounds.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1500));
        }
    }
};

// 测试按钮类型识别
const testButtonTypeRecognition = () => {
    console.log('=== 按钮类型识别测试 ===');
    
    // 创建测试按钮
    const testButtons = [
        { text: '安装', class: '', expected: 'click-primary' },
        { text: '更新', class: '', expected: 'click-primary' },
        { text: '卸载', class: '', expected: 'warning' },
        { text: '启用', class: '', expected: 'switch' },
        { text: '禁用', class: '', expected: 'switch' },
        { text: '确认', class: '', expected: 'click-primary' },
        { text: '取消', class: '', expected: 'warning' },
        { text: '普通按钮', class: '', expected: 'click' }
    ];
    
    testButtons.forEach(({ text, class: className, expected }) => {
        const button = document.createElement('button');
        button.textContent = text;
        if (className) button.className = className;
        
        // 假设存在 getButtonType 函数
        if (typeof getButtonType === 'function') {
            const actual = getButtonType(button);
            if (actual === expected) {
                console.log(`✅ "${text}" -> ${actual}`);
            } else {
                console.log(`❌ "${text}" -> 期望 ${expected}, 实际 ${actual}`);
            }
        } else {
            console.log(`⚠️ getButtonType 函数未找到，无法测试按钮类型识别`);
            break;
        }
    });
};

// 主测试函数
const runOptimizedAudioTest = async () => {
    console.log('🎯 启动器音效优化方案测试');
    console.log('=' * 50);
    
    // 1. 检查配置
    const configOK = testAudioConfig();
    console.log('');
    
    // 2. 测试按钮类型识别
    testButtonTypeRecognition();
    console.log('');
    
    // 3. 测试音效播放
    if (configOK) {
        console.log('配置正确，开始音效播放测试...');
        await testAudioPlayback();
    } else {
        console.log('❌ 配置有误，跳过音效播放测试');
    }
    
    console.log('');
    console.log('🎉 测试完成！');
    console.log('💡 提示：如果音效不正确，请运行以下命令重新加载配置：');
    console.log('   window.audioManager.sounds = {};');
    console.log('   await window.audioManager.preloadCoreSounds();');
};

// 导出测试函数
window.testOptimizedAudio = runOptimizedAudioTest;

console.log('📖 使用方法：');
console.log('   运行 testOptimizedAudio() 开始完整测试');
console.log('   或者运行 window.audioManager.play("音效名称") 测试单个音效');
