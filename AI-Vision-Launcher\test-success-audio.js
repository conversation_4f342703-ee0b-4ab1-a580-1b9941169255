/**
 * 成功音效测试脚本
 * 在启动器控制台中运行此脚本来测试成功音效修复效果
 */

console.log('🎵 测试成功音效修复...');

// 测试成功音效配置
const testSuccessAudioConfig = () => {
    console.log('=== 成功音效配置检查 ===');
    
    const expectedSuccessConfig = {
        'success': 'custom/任务完成音效.WAV',                  // 一般操作成功
        'plugin-success': 'custom/任务完成音效.WAV',           // 插件操作成功
        'version-success': 'custom/任务完成音效.WAV',          // 版本切换成功
        'install-success': 'custom/任务完成音效.WAV',          // 安装成功
        'update-success': 'custom/任务完成音效.WAV',           // 更新成功
        'complete': 'custom/操作成功反馈音效.WAV',              // 任务完成
        'startup-success': 'custom/任务完成音效.WAV'           // 启动成功
    };
    
    let configCorrect = true;
    for (const [key, expected] of Object.entries(expectedSuccessConfig)) {
        const actual = window.audioManager?.soundMap?.[key];
        if (actual === expected) {
            console.log(`✅ ${key}: ${actual}`);
        } else {
            console.log(`❌ ${key}: 期望 ${expected}, 实际 ${actual || '未配置'}`);
            configCorrect = false;
        }
    }
    
    return configCorrect;
};

// 测试成功音效播放
const testSuccessAudioPlayback = async () => {
    console.log('=== 成功音效播放测试 ===');
    
    const successSounds = [
        { name: 'success', description: '一般操作成功 (任务完成音效)' },
        { name: 'plugin-success', description: '插件操作成功 (任务完成音效)' },
        { name: 'version-success', description: '版本切换成功 (任务完成音效)' },
        { name: 'complete', description: '任务完成 (操作成功反馈音效)' }
    ];
    
    for (let i = 0; i < successSounds.length; i++) {
        const sound = successSounds[i];
        console.log(`🔊 播放 ${sound.name}: ${sound.description}`);
        
        if (window.audioManager) {
            window.audioManager.play(sound.name);
        } else {
            console.error('音效管理器未初始化');
            break;
        }
        
        // 等待2秒再播放下一个音效
        if (i < successSounds.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
};

// 模拟showNotification测试
const testShowNotificationAudio = () => {
    console.log('=== showNotification 音效测试 ===');
    
    if (!window.launcherInstance || !window.launcherInstance.showNotification) {
        console.error('❌ launcherInstance 或 showNotification 方法不可用');
        return;
    }
    
    const testNotifications = [
        { message: '插件安装成功', type: 'success', context: 'plugin', expectedSound: 'plugin-success' },
        { message: '版本切换成功', type: 'success', context: 'version', expectedSound: 'version-success' },
        { message: '安装完成', type: 'success', context: 'install', expectedSound: 'install-success' },
        { message: '更新成功', type: 'success', context: 'update', expectedSound: 'update-success' },
        { message: '操作成功', type: 'success', context: null, expectedSound: 'success' }
    ];
    
    testNotifications.forEach((test, index) => {
        setTimeout(() => {
            console.log(`📢 测试通知 ${index + 1}: "${test.message}" (期望音效: ${test.expectedSound})`);
            window.launcherInstance.showNotification(test.message, test.type, test.context);
        }, index * 3000);
    });
    
    console.log('⏰ 将在接下来的15秒内播放5个测试通知...');
};

// 检查音效文件加载状态
const checkSuccessAudioFiles = () => {
    console.log('=== 成功音效文件检查 ===');
    
    const successSoundNames = ['success', 'plugin-success', 'version-success', 'complete'];
    
    successSoundNames.forEach(soundName => {
        const soundObj = window.audioManager?.sounds?.[soundName];
        const soundPath = window.audioManager?.soundMap?.[soundName];
        
        console.log(`${soundName}:`);
        console.log(`  映射路径: ${soundPath}`);
        console.log(`  已加载: ${soundObj ? '✅' : '❌'}`);
        
        if (soundObj && soundObj.audio) {
            console.log(`  实际路径: ${decodeURIComponent(soundObj.audio.src)}`);
        }
        console.log('');
    });
};

// 强制重新加载成功音效
const reloadSuccessAudio = async () => {
    console.log('=== 强制重新加载成功音效 ===');
    
    if (!window.audioManager) {
        console.error('❌ 音效管理器不可用');
        return;
    }
    
    const successSoundNames = ['success', 'plugin-success', 'version-success', 'complete'];
    
    // 清除已加载的成功音效
    successSoundNames.forEach(soundName => {
        delete window.audioManager.sounds[soundName];
        console.log(`🗑️ 已清除 ${soundName} 音效缓存`);
    });
    
    // 重新加载成功音效
    for (const soundName of successSoundNames) {
        try {
            await window.audioManager.loadSound(soundName);
            console.log(`✅ ${soundName} 重新加载成功`);
        } catch (error) {
            console.error(`❌ ${soundName} 重新加载失败:`, error);
        }
    }
    
    console.log('🎉 成功音效重新加载完成');
};

// 主测试函数
const runSuccessAudioTest = async () => {
    console.log('🎯 成功音效修复测试');
    console.log('=' * 50);
    
    // 1. 检查配置
    const configOK = testSuccessAudioConfig();
    console.log('');
    
    // 2. 检查音效文件
    checkSuccessAudioFiles();
    
    // 3. 如果配置不正确，尝试重新加载
    if (!configOK) {
        console.log('⚠️ 配置不正确，尝试重新加载音效...');
        await reloadSuccessAudio();
        console.log('');
    }
    
    // 4. 测试音效播放
    console.log('🔊 开始音效播放测试...');
    await testSuccessAudioPlayback();
    console.log('');
    
    // 5. 测试showNotification
    console.log('📢 开始通知音效测试...');
    testShowNotificationAudio();
    
    console.log('');
    console.log('🎉 测试完成！');
    console.log('💡 现在可以在版本管理和插件管理页面测试实际操作的成功音效');
};

// 导出测试函数
window.testSuccessAudio = runSuccessAudioTest;
window.reloadSuccessAudio = reloadSuccessAudio;

console.log('📖 使用方法：');
console.log('   运行 testSuccessAudio() 开始完整测试');
console.log('   运行 reloadSuccessAudio() 重新加载成功音效');
console.log('   或者直接测试: window.audioManager.play("version-success")');
