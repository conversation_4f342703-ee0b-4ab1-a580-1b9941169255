@echo off
title AI-Vision 安装器测试
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                  AI-Vision 安装器测试脚本                    ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

:: 防止脚本出错时立即关闭窗口
setlocal EnableDelayedExpansion

:: 显示当前目录
echo 📁 当前目录: %CD%
echo.

:: 检查Node.js是否安装
echo [1/4] 检查Node.js环境...
node --version >nul 2>&1
if !errorlevel! neq 0 (
    echo ❌ 错误: 未找到Node.js
    echo.
    echo 请先安装Node.js:
    echo 1. 访问 https://nodejs.org/
    echo 2. 下载并安装最新版本
    echo 3. 重新运行此脚本
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

:: 显示Node.js版本
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js环境检查通过 - 版本: !NODE_VERSION!

:: 检查必要文件
echo.
echo [2/4] 检查必要文件...

set MISSING_FILES=0
set FILES_TO_CHECK=installer.html installer-main.js installer-electron-main.js installer-preload.js installer-config.js package.json

for %%f in (%FILES_TO_CHECK%) do (
    if not exist "%%f" (
        echo ❌ 错误: 缺少 %%f
        set /a MISSING_FILES+=1
    ) else (
        echo   - %%f [存在]
    )
)

if !MISSING_FILES! gtr 0 (
    echo.
    echo ❌ 发现 !MISSING_FILES! 个缺失文件，请确保所有文件都存在
    echo.
    echo 按任意键继续...
    pause >nul
) else (
    echo ✅ 必要文件检查通过
)

:: 安装依赖
echo.
echo [3/4] 检查和安装依赖包...
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.

    npm install
    if !errorlevel! neq 0 (
        echo ❌ 错误: 依赖包安装失败
        echo.
        echo 可能的原因:
        echo 1. 网络连接问题
        echo 2. npm配置问题
        echo 3. 权限不足
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已存在，跳过安装
)

:: 启动测试
echo.
echo [4/4] 启动安装器测试...
echo 正在启动安装器，请稍候...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                          注意事项                            ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ 💡 这是测试模式，不会进行实际安装                           ║
echo ║ 💡 安装器将在新窗口中打开                                   ║
echo ║ 💡 按 Ctrl+C 可以随时退出测试                               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 启动Electron应用
echo 启动命令: npm start
echo.
npm start

:: 检查启动结果
if !errorlevel! neq 0 (
    echo.
    echo ❌ 安装器启动失败，错误代码: !errorlevel!
    echo.
    echo 可能的解决方案:
    echo 1. 确保所有依赖已正确安装
    echo 2. 检查package.json中的启动脚本
    echo 3. 尝试手动运行: electron .
    echo.
) else (
    echo.
    echo ✅ 安装器测试完成
)

echo.
echo ========================================
echo 测试结束
echo ========================================
echo.
echo 按任意键退出...
pause >nul
