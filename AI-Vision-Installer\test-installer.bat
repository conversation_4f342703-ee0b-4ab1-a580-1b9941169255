@echo off
echo ========================================
echo AI-Vision 安装器测试脚本
echo ========================================
echo.

:: 检查Node.js是否安装
echo [1/4] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查必要文件
echo.
echo [2/4] 检查必要文件...
if not exist "installer.html" (
    echo ❌ 错误: 缺少 installer.html
    pause
    exit /b 1
)
if not exist "installer-main.js" (
    echo ❌ 错误: 缺少 installer-main.js
    pause
    exit /b 1
)
if not exist "installer-electron-main.js" (
    echo ❌ 错误: 缺少 installer-electron-main.js
    pause
    exit /b 1
)
if not exist "package.json" (
    echo ❌ 错误: 缺少 package.json
    pause
    exit /b 1
)
echo ✅ 必要文件检查通过

:: 安装依赖
echo.
echo [3/4] 安装依赖包...
if not exist "node_modules" (
    echo 正在安装依赖包，请稍候...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 错误: 依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖包已存在，跳过安装
)

:: 启动测试
echo.
echo [4/4] 启动安装器测试...
echo 正在启动安装器，请稍候...
echo.
echo 💡 提示: 这是测试模式，不会进行实际安装
echo 💡 提示: 按 Ctrl+C 可以随时退出测试
echo.

npm start

echo.
echo ========================================
echo 测试完成
echo ========================================
pause
