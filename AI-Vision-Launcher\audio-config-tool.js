/**
 * AI Vision Launcher 音效配置工具
 * Audio Configuration Tool for AI Vision Launcher
 */

class AudioConfigTool {
    constructor() {
        this.uploadedFiles = new Map(); // 存储上传的文件
        this.audioMapping = new Map(); // 存储音效映射配置
        this.audioManager = null;
        
        // 音效类型定义
        this.audioTypes = {
            'UI交互音效': {
                'click': '按钮点击',
                'click-primary': '主要按钮点击',
                'hover': '悬停音效',
                'switch': '切换操作',
                'input': '输入聚焦',
                'tab-switch': '标签页切换'
            },
            '反馈音效': {
                'success': '操作成功',
                'warning': '警告提示',
                'error': '错误提示',
                'notification': '系统通知',
                'confirm': '确认操作',
                'complete': '任务完成'
            },
            '专用成功音效': {
                'plugin-success': '插件操作成功',
                'version-success': '版本切换成功',
                'install-success': '安装成功',
                'update-success': '更新成功'
            },
            '系统音效': {
                'startup': '启动ComfyUI',
                'startup-success': 'ComfyUI启动成功',
                'shutdown': '关闭ComfyUI',
                'shutdown-success': 'ComfyUI关闭成功',
                'app-close': '关闭启动器',
                'loading': '加载过程'
            }
        };
        
        this.init();
    }

    async init() {
        try {
            // 初始化音效管理器
            if (window.TechAudioManager) {
                this.audioManager = new TechAudioManager();
                await this.audioManager.init();
                console.log('音效管理器初始化成功');

                // 绑定音效到按钮
                this.bindAudioToButtons();
            }

            // 绑定事件
            this.bindEvents();

            // 渲染音效映射界面
            this.renderAudioMapping();

            // 加载现有配置
            this.loadExistingConfig();

        } catch (error) {
            console.error('音效配置工具初始化失败:', error);
            this.showStatus('初始化失败: ' + error.message, 'error');
        }
    }

    bindEvents() {
        // 文件上传相关事件
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // 控制按钮事件
        document.getElementById('previewBtn').addEventListener('click', this.previewConfig.bind(this));
        document.getElementById('saveBtn').addEventListener('click', this.saveConfig.bind(this));
        document.getElementById('loadBtn').addEventListener('click', this.loadConfig.bind(this));
        document.getElementById('resetBtn').addEventListener('click', this.resetConfig.bind(this));
        document.getElementById('clearBtn').addEventListener('click', this.clearFiles.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('uploadArea').classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('uploadArea').classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('uploadArea').classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files);
        this.processFiles(files);
    }

    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processFiles(files);
    }

    async processFiles(files) {
        for (const file of files) {
            if (this.validateFile(file)) {
                await this.uploadFile(file);
            }
        }
        this.renderUploadedFiles();
        this.updateMappingOptions();
    }

    validateFile(file) {
        // 检查文件类型
        const allowedTypes = ['audio/wav', 'audio/mpeg', 'audio/ogg', 'audio/mp3'];
        const allowedExtensions = ['.wav', '.mp3', '.ogg'];
        
        const isValidType = allowedTypes.includes(file.type) || 
                           allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
        
        if (!isValidType) {
            this.showStatus(`文件 ${file.name} 格式不支持`, 'error');
            return false;
        }

        // 检查文件大小 (10MB)
        if (file.size > 10 * 1024 * 1024) {
            this.showStatus(`文件 ${file.name} 超过10MB限制`, 'error');
            return false;
        }

        return true;
    }

    async uploadFile(file) {
        try {
            // 显示上传中状态
            this.showStatus(`正在上传文件 ${file.name}...`, 'info');

            // 上传文件到服务器
            const result = await this.copyFileToCustomDir(file);

            // 创建文件URL用于播放
            const fileUrl = URL.createObjectURL(file);

            // 存储文件信息
            this.uploadedFiles.set(file.name, {
                file: file,
                url: fileUrl,
                uploadTime: new Date(),
                serverPath: result.path
            });

            this.showStatus(`文件 ${file.name} 上传成功`, 'success');

        } catch (error) {
            console.error('文件上传失败:', error);
            this.showStatus(`文件 ${file.name} 上传失败: ${error.message}`, 'error');
        }
    }

    async copyFileToCustomDir(file) {
        try {
            const formData = new FormData();
            formData.append('audioFile', file);

            const response = await fetch('/api/upload-audio', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ error: response.statusText }));
                throw new Error(errorData.error || `上传失败: ${response.statusText}`);
            }

            const result = await response.json();
            console.log(`文件 ${file.name} 已保存到 ${result.path}`);
            return result;

        } catch (error) {
            console.error('文件上传失败:', error);
            throw error; // 重新抛出错误，让调用者处理
        }
    }

    renderUploadedFiles() {
        const container = document.getElementById('uploadedFiles');
        container.innerHTML = '';

        if (this.uploadedFiles.size === 0) {
            container.innerHTML = '<div style="text-align: center; color: #888; padding: 20px;">暂无上传的文件</div>';
            return;
        }

        this.uploadedFiles.forEach((fileInfo, fileName) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-icon">🎵</div>
                <div class="file-name">${fileName}</div>
                <div class="file-actions">
                    <button class="action-btn" onclick="audioConfigTool.playFile('${fileName}')">播放</button>
                    <button class="action-btn delete" onclick="audioConfigTool.deleteFile('${fileName}')">删除</button>
                </div>
            `;
            container.appendChild(fileItem);
        });
    }

    renderAudioMapping() {
        const container = document.getElementById('audioMapping');
        container.innerHTML = '';

        Object.entries(this.audioTypes).forEach(([category, types]) => {
            const categoryDiv = document.createElement('div');
            categoryDiv.innerHTML = `
                <h4 style="color: #00f5ff; margin: 20px 0 15px 0; font-size: 1.1rem;">${category}</h4>
            `;
            container.appendChild(categoryDiv);

            Object.entries(types).forEach(([key, label]) => {
                const mappingItem = document.createElement('div');
                mappingItem.className = 'mapping-item';
                mappingItem.innerHTML = `
                    <div class="mapping-label">${label}</div>
                    <select class="mapping-select" id="mapping-${key}">
                        <option value="">请选择音效文件</option>
                    </select>
                    <button class="play-btn" onclick="audioConfigTool.previewMapping('${key}')">预览</button>
                `;
                container.appendChild(mappingItem);
            });
        });

        this.updateMappingOptions();
    }

    updateMappingOptions() {
        // 更新所有下拉框的选项
        Object.keys(this.audioTypes).forEach(category => {
            Object.keys(this.audioTypes[category]).forEach(key => {
                const select = document.getElementById(`mapping-${key}`);
                if (select) {
                    // 保存当前选择
                    const currentValue = select.value;
                    
                    // 清空选项
                    select.innerHTML = '<option value="">请选择音效文件</option>';
                    
                    // 添加上传的文件选项
                    this.uploadedFiles.forEach((fileInfo, fileName) => {
                        const option = document.createElement('option');
                        option.value = fileName;
                        option.textContent = fileName;
                        select.appendChild(option);
                    });
                    
                    // 恢复选择
                    if (currentValue && this.uploadedFiles.has(currentValue)) {
                        select.value = currentValue;
                    }
                }
            });
        });
    }

    playFile(fileName) {
        const fileInfo = this.uploadedFiles.get(fileName);
        if (fileInfo) {
            const audio = new Audio(fileInfo.url);
            audio.volume = 0.5;
            audio.play().catch(e => console.log('播放失败:', e));
        }
    }

    deleteFile(fileName) {
        if (confirm(`确定要删除文件 "${fileName}" 吗？`)) {
            const fileInfo = this.uploadedFiles.get(fileName);
            if (fileInfo) {
                URL.revokeObjectURL(fileInfo.url);
                this.uploadedFiles.delete(fileName);
                this.renderUploadedFiles();
                this.updateMappingOptions();
                this.showStatus(`文件 ${fileName} 已删除`, 'info');
            }
        }
    }

    previewMapping(key) {
        const select = document.getElementById(`mapping-${key}`);
        const fileName = select.value;
        
        if (fileName) {
            this.playFile(fileName);
        } else {
            this.showStatus('请先选择音效文件', 'error');
        }
    }

    previewConfig() {
        this.showStatus('开始预览配置...', 'info');
        
        // 收集当前配置
        const config = this.collectCurrentConfig();
        
        // 播放每种音效的预览
        let delay = 0;
        Object.entries(config).forEach(([key, fileName]) => {
            if (fileName) {
                setTimeout(() => {
                    this.playFile(fileName);
                    this.showStatus(`预览: ${this.getAudioTypeLabel(key)}`, 'info');
                }, delay);
                delay += 1500; // 每个音效间隔1.5秒
            }
        });
        
        setTimeout(() => {
            this.showStatus('配置预览完成', 'success');
        }, delay);
    }

    collectCurrentConfig() {
        const config = {};
        console.log('开始收集配置...');

        Object.keys(this.audioTypes).forEach(category => {
            console.log(`处理分类: ${category}`);
            Object.keys(this.audioTypes[category]).forEach(key => {
                const selectId = `mapping-${key}`;
                const select = document.getElementById(selectId);
                console.log(`查找元素: ${selectId}, 找到: ${!!select}, 值: ${select?.value}`);
                if (select && select.value) {
                    config[key] = select.value;
                    console.log(`添加配置: ${key} = ${select.value}`);
                }
            });
        });

        console.log('收集到的配置:', config);
        return config;
    }

    getAudioTypeLabel(key) {
        for (const category of Object.values(this.audioTypes)) {
            if (category[key]) {
                return category[key];
            }
        }
        return key;
    }

    async saveConfig() {
        try {
            console.log('=== 开始保存配置 ===');
            const config = this.collectCurrentConfig();
            console.log('收集到的原始配置:', config);

            if (Object.keys(config).length === 0) {
                this.showStatus('没有配置任何音效映射，请先选择音效文件', 'error');
                return;
            }

            // 生成新的音效映射配置
            const soundMap = {};
            Object.entries(config).forEach(([key, fileName]) => {
                if (fileName) {
                    soundMap[key] = `custom/${fileName}`;
                }
            });
            console.log('生成的音效映射:', soundMap);

            // 保存配置到localStorage
            console.log('保存到localStorage...');
            localStorage.setItem('audioConfigTool_mapping', JSON.stringify(config));
            localStorage.setItem('audioConfigTool_soundMap', JSON.stringify(soundMap));
            console.log('localStorage保存完成');

            // 使用新的同步机制保存配置
            console.log('使用同步机制保存配置...');
            if (window.audioConfigSync) {
                const success = await window.audioConfigSync.saveConfig(soundMap);
                if (success) {
                    console.log('同步机制保存成功');
                } else {
                    console.log('同步机制保存失败，使用备用方法');
                    this.saveConfigForLauncher(soundMap);
                }
            } else {
                console.log('同步机制不可用，使用原始方法');
                this.saveConfigForLauncher(soundMap);
            }

            // 尝试更新音效管理器配置文件（可选）
            try {
                await this.updateAudioManagerConfig(soundMap);
            } catch (error) {
                console.log('API更新失败，但本地配置已保存:', error.message);
            }

            // 更新当前音效管理器实例
            if (this.audioManager) {
                Object.assign(this.audioManager.soundMap, soundMap);
            }

            this.showStatus('配置保存成功！启动器将自动应用新配置', 'success');
            console.log('=== 配置保存完成 ===');

        } catch (error) {
            console.error('保存配置失败:', error);
            this.showStatus('保存配置失败: ' + error.message, 'error');
        }
    }

    async updateAudioManagerConfig(soundMap) {
        try {
            // 调用API更新音效管理器配置文件
            const response = await fetch('/api/update-audio-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ soundMap })
            });

            if (!response.ok) {
                throw new Error(`更新配置失败: ${response.statusText}`);
            }

            console.log('音效管理器配置已更新');

        } catch (error) {
            console.log('API不可用，配置已保存到本地存储');
            // 配置已经在saveConfig中保存到启动器可读取的位置
        }
    }

    saveConfigForLauncher(soundMap) {
        // 将配置保存到启动器可以读取的位置
        const configData = {
            timestamp: new Date().toISOString(),
            soundMap: soundMap,
            version: '1.0'
        };

        console.log('准备保存启动器配置:', configData);
        localStorage.setItem('launcher_audio_config', JSON.stringify(configData));

        // 验证保存是否成功
        const savedData = localStorage.getItem('launcher_audio_config');
        if (savedData) {
            console.log('启动器配置保存成功，验证数据:', JSON.parse(savedData));
        } else {
            console.error('启动器配置保存失败！');
        }
    }

    loadConfig() {
        try {
            const savedConfig = localStorage.getItem('audioConfigTool_mapping');
            if (savedConfig) {
                const config = JSON.parse(savedConfig);
                
                // 应用配置到界面
                Object.entries(config).forEach(([key, fileName]) => {
                    const select = document.getElementById(`mapping-${key}`);
                    if (select && this.uploadedFiles.has(fileName)) {
                        select.value = fileName;
                    }
                });
                
                this.showStatus('配置加载成功', 'success');
            } else {
                this.showStatus('没有找到保存的配置', 'info');
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            this.showStatus('加载配置失败: ' + error.message, 'error');
        }
    }

    loadExistingConfig() {
        // 加载现有的音效文件
        this.loadExistingFiles();

        // 加载保存的配置
        this.loadConfig();
    }

    // 为配置工具的按钮绑定音效
    bindAudioToButtons() {
        if (!this.audioManager) {
            console.log('音效管理器未初始化，跳过音效绑定');
            return;
        }

        console.log('开始为配置工具绑定音效...');

        // 为所有按钮绑定点击音效
        document.querySelectorAll('button').forEach(button => {
            if (!button.hasAttribute('data-audio-bound')) {
                button.addEventListener('click', (e) => {
                    const buttonType = this.getButtonType(button);
                    console.log(`按钮点击: ${button.textContent} -> 音效类型: ${buttonType}`);
                    this.audioManager.play(buttonType);
                });

                // 悬停音效（如果启用）
                button.addEventListener('mouseenter', () => {
                    if (this.audioManager.settings.hoverSounds) {
                        this.audioManager.play('hover');
                    }
                });

                button.setAttribute('data-audio-bound', 'true');
            }
        });

        // 为标签页绑定切换音效（如果有的话）
        document.querySelectorAll('.tab-button, .nav-tab, [data-tab]').forEach(tab => {
            if (!tab.hasAttribute('data-audio-bound')) {
                tab.addEventListener('click', () => {
                    console.log('标签页切换音效');
                    this.audioManager.play('tab-switch');
                });
                tab.setAttribute('data-audio-bound', 'true');
            }
        });

        console.log('配置工具音效绑定完成');
    }

    // 获取按钮类型（用于确定播放哪种音效）
    getButtonType(button) {
        const classList = button.classList;
        const id = button.id;
        const text = button.textContent.toLowerCase();

        // 根据按钮ID和类名确定音效类型
        if (id === 'saveBtn' || text.includes('保存')) {
            return 'success';
        }
        if (id === 'clearBtn' || text.includes('清空') || text.includes('删除')) {
            return 'warning';
        }
        if (id === 'resetBtn' || text.includes('重置')) {
            return 'confirm';
        }
        if (classList.contains('play-btn') || text.includes('预览') || text.includes('播放')) {
            return 'click-primary';
        }
        if (classList.contains('success')) {
            return 'success';
        }
        if (classList.contains('danger')) {
            return 'warning';
        }

        // 默认使用普通点击音效
        return 'click';
    }

    async loadExistingFiles() {
        try {
            // 从服务器获取现有文件列表
            const response = await fetch('/api/list-audio-files');
            if (response.ok) {
                const files = await response.json();
                files.forEach(fileName => {
                    this.uploadedFiles.set(fileName, {
                        file: null,
                        url: `assets/sounds/custom/${fileName}`,
                        uploadTime: new Date(),
                        existing: true
                    });
                });
            } else {
                // 如果API不可用，使用已知的文件列表
                const existingFiles = [
                    '导航标签点击的声音.WAV',
                    '操作成功反馈音效.WAV',
                    '关闭comfyui.WAV',
                    '启动comfyui的音效.WAV',
                    'comfyui启动成功的声音.WAV',
                    '关闭启动器窗口的音效.WAV'
                ];

                existingFiles.forEach(fileName => {
                    this.uploadedFiles.set(fileName, {
                        file: null,
                        url: `assets/sounds/custom/${fileName}`,
                        uploadTime: new Date(),
                        existing: true
                    });
                });
            }
        } catch (error) {
            console.warn('加载现有文件失败:', error);
        }

        this.renderUploadedFiles();
        this.updateMappingOptions();
    }

    resetConfig() {
        if (confirm('确定要重置所有配置吗？这将清空所有音效映射设置。')) {
            // 清空所有选择
            Object.keys(this.audioTypes).forEach(category => {
                Object.keys(this.audioTypes[category]).forEach(key => {
                    const select = document.getElementById(`mapping-${key}`);
                    if (select) {
                        select.value = '';
                    }
                });
            });
            
            // 清空保存的配置
            localStorage.removeItem('audioConfigTool_mapping');
            localStorage.removeItem('audioConfigTool_soundMap');
            
            this.showStatus('配置已重置', 'info');
        }
    }

    clearFiles() {
        if (confirm('确定要清空所有上传的文件吗？这将删除所有自定义音效文件。')) {
            // 释放文件URL
            this.uploadedFiles.forEach(fileInfo => {
                if (fileInfo.url && !fileInfo.existing) {
                    URL.revokeObjectURL(fileInfo.url);
                }
            });
            
            // 清空文件列表
            this.uploadedFiles.clear();
            
            // 重新加载现有文件
            this.loadExistingFiles();
            
            this.showStatus('文件已清空', 'info');
        }
    }

    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('statusMessage');
        statusElement.textContent = message;
        statusElement.className = `status-message ${type}`;
        statusElement.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 3000);
    }
}

// 全局实例
let audioConfigTool;

// 调试函数
function debugConfig() {
    console.log('=== 调试配置信息 ===');
    if (window.audioConfigTool) {
        console.log('audioConfigTool实例:', window.audioConfigTool);
        console.log('音效类型定义:', window.audioConfigTool.audioTypes);

        // 检查所有下拉框
        Object.keys(window.audioConfigTool.audioTypes).forEach(category => {
            console.log(`分类: ${category}`);
            Object.keys(window.audioConfigTool.audioTypes[category]).forEach(key => {
                const selectId = `mapping-${key}`;
                const select = document.getElementById(selectId);
                console.log(`  ${key}: 元素存在=${!!select}, 值="${select?.value}"`);
            });
        });

        // 测试配置收集
        const config = window.audioConfigTool.collectCurrentConfig();
        console.log('当前配置:', config);

        // 检查localStorage
        console.log('localStorage数据:');
        console.log('  launcher_audio_config:', localStorage.getItem('launcher_audio_config'));
        console.log('  audioConfigTool_mapping:', localStorage.getItem('audioConfigTool_mapping'));
        console.log('  audioConfigTool_soundMap:', localStorage.getItem('audioConfigTool_soundMap'));
    } else {
        console.log('audioConfigTool实例不存在');
    }
    console.log('=== 调试完成 ===');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('初始化音效配置工具...');
    audioConfigTool = new AudioConfigTool();
    // 确保全局可访问
    window.audioConfigTool = audioConfigTool;
    window.debugConfig = debugConfig;
    console.log('音效配置工具初始化完成');
});
