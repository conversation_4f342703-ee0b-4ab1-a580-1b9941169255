/**
 * 直接应用音效配置的脚本
 * 在启动器控制台中运行此脚本来应用你的音效配置
 */

// 优化后的音效配置方案 - 基于用户配置工具设置
const customAudioConfig = {
    // UI交互音效
    'click': 'custom/按钮科技音效.WAV',                    // 普通按钮点击
    'click-primary': 'custom/按钮科技音效.WAV',            // 主要按钮点击
    'hover': 'custom/提醒、警告音效.WAV',                  // 悬停音效
    'input': 'custom/按钮科技音效.WAV',                    // 输入聚焦
    'switch': 'custom/导航标签点击的声音.WAV',              // 切换操作
    'tab-switch': 'custom/导航标签点击的声音.WAV',          // 标签页切换

    // 反馈音效
    'success': 'custom/任务完成音效.WAV',                  // 操作成功
    'complete': 'custom/操作成功反馈音效.WAV',              // 任务完成
    'confirm': 'custom/导航标签点击的声音.WAV',             // 确认操作
    'warning': 'custom/提醒、警告音效.WAV',                // 警告提示
    'error': 'custom/提醒、警告音效.WAV',                  // 错误提示
    'notification': 'custom/提醒、警告音效.WAV',           // 系统通知

    // 系统音效
    'startup': 'custom/启动程序音效.WAV',                  // 启动ComfyUI
    'startup-success': 'custom/任务完成音效.WAV',          // 启动成功
    'shutdown': 'custom/关闭comfyui.WAV',                 // 关闭ComfyUI
    'shutdown-success': 'custom/comfyui关闭成功的音效.WAV', // 关闭成功
    'app-close': 'custom/关闭启动器窗口的音效.WAV',         // 关闭启动器
    'loading': 'custom/按钮科技音效.WAV'                   // 加载过程
};

// 从配置文件获取最新配置的函数
async function getLatestConfigFromTool() {
    try {
        console.log('🔍 检查配置工具的配置...');

        // 方法1: 尝试从localStorage获取（Electron环境可能有数据）
        const toolConfig = localStorage.getItem('audioConfigTool_mapping');
        const toolSoundMap = localStorage.getItem('audioConfigTool_soundMap');
        const launcherConfig = localStorage.getItem('launcher_audio_config');

        console.log('localStorage检查:');
        console.log('toolConfig:', toolConfig);
        console.log('toolSoundMap:', toolSoundMap);
        console.log('launcherConfig:', launcherConfig);

        // 优先使用launcher_audio_config
        if (launcherConfig) {
            const config = JSON.parse(launcherConfig);
            if (config.soundMap) {
                console.log('✅ 找到localStorage中的launcher_audio_config配置');
                return config.soundMap;
            }
        }

        // 其次使用toolSoundMap
        if (toolSoundMap) {
            const soundMap = JSON.parse(toolSoundMap);
            console.log('✅ 找到localStorage中的toolSoundMap配置');
            return soundMap;
        }

        // 方法2: 尝试通过API获取配置文件
        try {
            console.log('🌐 尝试通过API获取配置...');
            const response = await fetch('http://127.0.0.1:8404/audio-config');
            if (response.ok) {
                const apiConfig = await response.json();
                if (apiConfig && apiConfig.soundMap) {
                    console.log('✅ 通过API获取到配置:', apiConfig);
                    return apiConfig.soundMap;
                }
            }
        } catch (apiError) {
            console.log('⚠️ API获取配置失败:', apiError.message);
        }

        // 方法3: 尝试读取本地配置文件
        try {
            console.log('📁 尝试读取本地配置文件...');
            const fileResponse = await fetch('./audio-config.json');
            if (fileResponse.ok) {
                const fileConfig = await fileResponse.json();
                if (fileConfig && fileConfig.soundMap) {
                    console.log('✅ 从本地文件获取到配置:', fileConfig);
                    return fileConfig.soundMap;
                }
            }
        } catch (fileError) {
            console.log('⚠️ 本地文件读取失败:', fileError.message);
        }

        console.log('❌ 所有方法都未找到配置工具的配置，使用默认配置');
        return null;
    } catch (error) {
        console.error('❌ 获取配置工具配置失败:', error);
        return null;
    }
}

// 应用配置函数
function applyCustomAudioConfig() {
    console.log('🎵 开始应用自定义音效配置...');

    if (!window.audioManager) {
        console.error('❌ 音效管理器不可用');
        return false;
    }

    console.log('📋 当前音效映射:', window.audioManager.soundMap);

    // 尝试获取配置工具的最新配置
    const latestConfig = getLatestConfigFromTool();
    const configToApply = latestConfig || customAudioConfig;

    if (latestConfig) {
        console.log('🎯 使用配置工具的最新配置');
    } else {
        console.log('🎯 使用默认硬编码配置');
    }

    console.log('📋 将要应用的配置:', configToApply);

    // 应用配置
    Object.assign(window.audioManager.soundMap, configToApply);

    console.log('✅ 自定义音效配置已应用');
    console.log('📋 更新后的音效映射:', window.audioManager.soundMap);

    // 清空已加载的音效，强制重新加载
    window.audioManager.sounds = {};
    console.log('🔄 已清空音效缓存，将重新加载');

    return true;
}

// 测试音效函数
function testCustomAudio() {
    console.log('🎧 开始测试自定义音效...');
    
    if (!window.audioManager) {
        console.error('❌ 音效管理器不可用');
        return;
    }
    
    const testSounds = [
        { name: 'click', label: '按钮点击' },
        { name: 'success', label: '操作成功' },
        { name: 'warning', label: '警告提示' },
        { name: 'startup', label: '启动程序' }
    ];
    
    testSounds.forEach((sound, index) => {
        setTimeout(() => {
            console.log(`🔊 播放测试: ${sound.label} (${sound.name})`);
            try {
                window.audioManager.play(sound.name);
            } catch (error) {
                console.error(`❌ 播放 ${sound.name} 失败:`, error);
            }
        }, index * 1500);
    });
}

// 检查音效文件状态
function checkAudioFiles() {
    console.log('🔍 检查音效文件状态...');
    
    const uniqueFiles = [...new Set(Object.values(customAudioConfig))];
    
    uniqueFiles.forEach(filePath => {
        const fullPath = `assets/sounds/${filePath}`;
        const audio = new Audio(fullPath);
        
        audio.addEventListener('canplaythrough', () => {
            console.log(`✅ 文件可用: ${filePath}`);
        });
        
        audio.addEventListener('error', (e) => {
            console.log(`❌ 文件不可用: ${filePath}`, e);
        });
        
        // 尝试加载
        audio.load();
    });
}

// 重置音效配置
function resetAudioConfig() {
    console.log('🔄 重置音效配置...');
    
    if (!window.audioManager) {
        console.error('❌ 音效管理器不可用');
        return;
    }
    
    // 重新初始化音效管理器
    window.audioManager.sounds = {};
    window.audioManager.init();
    
    console.log('✅ 音效配置已重置');
}

// 手动同步配置工具的配置
function syncFromConfigTool() {
    console.log('🔄 手动同步配置工具的配置...');

    // 配置工具中的最新实际配置数据（基于控制台日志）
    const configToolData = {
        "click": "custom/按钮科技音效.WAV",
        "click-primary": "custom/按钮科技音效.WAV",
        "hover": "custom/提醒、警告音效.WAV",
        "switch": "custom/导航标签点击的声音.WAV",
        "tab-switch": "custom/导航标签点击的声音.WAV",
        "success": "custom/任务完成音效.WAV",
        "warning": "custom/提醒、警告音效.WAV",
        "error": "custom/提醒、警告音效.WAV",
        "notification": "custom/提醒、警告音效.WAV",
        "confirm": "custom/导航标签点击的声音.WAV",
        "complete": "custom/操作成功反馈音效.WAV",
        "startup": "custom/启动程序音效.WAV",
        "startup-success": "custom/任务完成音效.WAV",
        "shutdown": "custom/关闭comfyui.WAV",
        "shutdown-success": "custom/comfyui关闭成功的音效.WAV",
        "app-close": "custom/关闭启动器窗口的音效.WAV"
    };

    if (!window.audioManager) {
        console.error('❌ 音效管理器不可用');
        return false;
    }

    console.log('📋 当前音效映射:', window.audioManager.soundMap);
    console.log('🎯 应用配置工具的实际配置');
    console.log('📋 配置工具配置:', configToolData);

    // 应用配置工具的配置
    Object.assign(window.audioManager.soundMap, configToolData);

    console.log('✅ 配置工具配置已同步到启动器');
    console.log('📋 更新后的音效映射:', window.audioManager.soundMap);

    // 清空已加载的音效，强制重新加载
    window.audioManager.sounds = {};
    console.log('🔄 已清空音效缓存，将重新加载');

    // 保存到localStorage
    const configData = {
        timestamp: new Date().toISOString(),
        soundMap: configToolData,
        version: '1.0',
        source: 'manual-sync-from-config-tool'
    };

    localStorage.setItem('launcher_audio_config', JSON.stringify(configData));
    console.log('💾 配置已保存到启动器localStorage');

    return true;
}

// 导出到全局
window.customAudioTools = {
    apply: applyCustomAudioConfig,
    test: testCustomAudio,
    check: checkAudioFiles,
    reset: resetAudioConfig,
    sync: syncFromConfigTool,
    config: customAudioConfig
};

console.log('🎵 自定义音效工具已加载');
console.log('📖 使用方法:');
console.log('  customAudioTools.apply() - 应用自定义音效配置');
console.log('  customAudioTools.sync() - 手动同步配置工具的配置');
console.log('  customAudioTools.test() - 测试音效播放');
console.log('  customAudioTools.check() - 检查音效文件状态');
console.log('  customAudioTools.reset() - 重置音效配置');
console.log('');
console.log('🚀 快速开始: 运行 customAudioTools.sync() 然后 customAudioTools.test()');
console.log('💡 提示: sync() 会应用你在配置工具中的实际配置');
