@echo off
title AI-Vision China Mirrors Test
color 0A

echo.
echo ========================================
echo AI-Vision 国内镜像源测试
echo ========================================
echo.

echo 🇨🇳 现在默认使用国内镜像源:
echo.

echo 主要源 (优先级从高到低):
echo 1. 清华大学镜像 (pypi.tuna.tsinghua.edu.cn)
echo 2. 阿里云镜像 (mirrors.aliyun.com)  
echo 3. 豆瓣镜像 (pypi.douban.com)
echo 4. PyTorch官方源 (download.pytorch.org)
echo.

echo 🚀 预期改进:
echo - PyTorch下载速度提升 5-10倍
echo - 依赖包安装更快更稳定
echo - 自动重试多个镜像源
echo - 减少网络超时问题
echo.

echo 📊 下载速度对比:
echo - 官方源: 100-500 KB/s (国内)
echo - 清华镜像: 5-20 MB/s (国内)
echo - 阿里镜像: 3-15 MB/s (国内)
echo.

echo 🕒 预计安装时间 (使用国内源):
echo - PyTorch 2.7.1: 5-15分钟 (原来30-60分钟)
echo - 依赖包: 2-5分钟 (原来10-20分钟)
echo - 总计: 10-25分钟 (原来40-80分钟)
echo.

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit
)

echo ✅ 环境检查通过
echo.

if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
)

echo.
echo ========================================
echo 开始国内镜像源测试
echo ========================================
echo.

echo 🔧 配置变更:
echo - 默认使用清华大学PyPI镜像
echo - PyTorch包从清华源下载
echo - 自动fallback到其他国内镜像
echo - 增加trusted-host配置
echo.

echo 📝 注意事项:
echo - 首次使用镜像可能需要同步时间
echo - 如果某个镜像慢会自动切换
echo - 所有包都会优先使用国内源
echo - 保持网络连接稳定
echo.

set /p start="开始测试国内镜像安装? (y/N): "
if /i not "%start%"=="y" (
    echo 测试取消
    pause
    exit
)

echo.
echo 🚀 启动安装器 (国内镜像模式)...
echo 观察控制台输出，应该看到:
echo - "使用清华镜像源"
echo - 更快的下载速度
echo - 更少的超时错误
echo.

npm start

echo.
if %errorlevel% equ 0 (
    echo ========================================
    echo 🎉 国内镜像测试成功!
    echo ========================================
    echo.
    echo 安装速度应该明显提升
    echo PyTorch下载时间大幅缩短
    echo.
) else (
    echo ========================================
    echo ❌ 安装遇到问题
    echo ========================================
    echo.
    echo 可能原因:
    echo - 网络连接问题
    echo - 镜像源临时不可用
    echo - 包版本兼容性问题
    echo.
    echo 解决方案:
    echo - 检查网络连接
    echo - 稍后重试
    echo - 安装器会自动尝试多个源
    echo.
)

pause
