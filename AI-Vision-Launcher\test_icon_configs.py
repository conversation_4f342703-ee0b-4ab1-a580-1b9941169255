#!/usr/bin/env python3
"""
测试不同图标配置的脚本
"""

import os
import shutil

def backup_main_js():
    """备份main.js文件"""
    if os.path.exists("main.js"):
        shutil.copy2("main.js", "main.js.backup")
        print("✓ 已备份 main.js 到 main.js.backup")
        return True
    return False

def restore_main_js():
    """恢复main.js文件"""
    if os.path.exists("main.js.backup"):
        shutil.copy2("main.js.backup", "main.js")
        print("✓ 已从备份恢复 main.js")
        return True
    return False

def create_icon_config(config_name, icon_line):
    """创建特定的图标配置"""
    if not os.path.exists("main.js.backup"):
        print("❌ 未找到备份文件，请先运行备份")
        return False
    
    # 读取备份文件
    with open("main.js.backup", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并替换图标配置行
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if 'icon:' in line and ('assets' in line or 'nativeImage' in line):
            lines[i] = f"            {icon_line}"
            break
    
    # 写入新配置
    with open("main.js", 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print(f"✓ 已应用配置: {config_name}")
    print(f"  图标设置: {icon_line}")
    return True

def main():
    print("AI视界启动器 - 图标配置测试工具")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "assets/icon.png",
        "assets/icon.ico", 
        "assets/taskbar-icon.ico"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要的图标文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请先运行: python fix_taskbar_icon.py")
        return
    
    print("✓ 所有必要的图标文件都存在")
    
    # 备份当前配置
    if not backup_main_js():
        print("❌ 无法备份main.js文件")
        return
    
    print("\n可用的图标配置选项:")
    print("1. 高分辨率PNG (推荐) - 让Electron自动处理缩放")
    print("2. 优化的任务栏ICO - 专门针对任务栏优化的多尺寸ICO")
    print("3. 标准ICO文件 - 包含所有尺寸的标准ICO")
    print("4. nativeImage动态处理 - 代码中动态创建图标")
    print("5. 恢复原始配置")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择配置 (0-5): ").strip()
            
            if choice == '0':
                print("退出测试工具")
                break
            elif choice == '1':
                icon_line = 'icon: path.join(__dirname, "assets", "icon.png"), // 高分辨率PNG'
                create_icon_config("高分辨率PNG", icon_line)
            elif choice == '2':
                icon_line = 'icon: path.join(__dirname, "assets", "taskbar-icon.ico"), // 任务栏优化ICO'
                create_icon_config("任务栏优化ICO", icon_line)
            elif choice == '3':
                icon_line = 'icon: path.join(__dirname, "assets", "icon.ico"), // 标准ICO'
                create_icon_config("标准ICO", icon_line)
            elif choice == '4':
                # 这个需要更复杂的替换，暂时跳过
                print("nativeImage配置已在当前main.js中实现")
            elif choice == '5':
                restore_main_js()
            else:
                print("无效选择，请输入0-5")
                continue
            
            print(f"\n✅ 配置已更新！")
            print("请重新启动应用程序测试效果")
            print("如果图标仍然模糊，请尝试其他配置")
            
            test_again = input("\n是否继续测试其他配置? (y/n): ").strip().lower()
            if test_again != 'y':
                break
                
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            break
        except Exception as e:
            print(f"发生错误: {e}")
    
    print(f"\n测试完成！")
    print("如果找到最佳配置，可以删除 main.js.backup 文件")

if __name__ == "__main__":
    main()
