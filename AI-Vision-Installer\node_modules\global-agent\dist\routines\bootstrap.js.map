{"version": 3, "sources": ["../../src/routines/bootstrap.js"], "names": ["log", "<PERSON><PERSON>", "child", "namespace", "configurationInput", "global", "GLOBAL_AGENT", "warn"], "mappings": ";;;;;;;AAEA;;AACA;;;;AAOA,MAAMA,GAAG,GAAGC,gBAAOC,KAAP,CAAa;AACvBC,EAAAA,SAAS,EAAE;AADY,CAAb,CAAZ;;kBAIgBC,kB,IAAmE;AACjF,MAAIC,MAAM,CAACC,YAAX,EAAyB;AACvBN,IAAAA,GAAG,CAACO,IAAJ,CAAS,iFAAT;AAEA,WAAO,KAAP;AACD;;AAEDF,EAAAA,MAAM,CAACC,YAAP,GAAsB,uCAAuBF,kBAAvB,CAAtB;AAEA,SAAO,IAAP;AACD,C", "sourcesContent": ["// @flow\n\nimport Logger from '../Logger';\nimport {\n  createGlobalProxyAgent,\n} from '../factories';\nimport type {\n  ProxyAgentConfigurationInputType,\n} from '../types';\n\nconst log = Logger.child({\n  namespace: 'bootstrap',\n});\n\nexport default (configurationInput?: ProxyAgentConfigurationInputType): boolean => {\n  if (global.GLOBAL_AGENT) {\n    log.warn('found global.GLOBAL_AGENT; second attempt to bootstrap global-agent was ignored');\n\n    return false;\n  }\n\n  global.GLOBAL_AGENT = createGlobalProxyAgent(configurationInput);\n\n  return true;\n};\n"], "file": "bootstrap.js"}