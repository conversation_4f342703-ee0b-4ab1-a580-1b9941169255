@echo off
title AI Vision Launcher - Process Manager
echo ====================================
echo    AI Vision Launcher Process Manager
echo ====================================
echo.

:menu
echo 请选择操作:
echo 1. 查看当前Electron进程
echo 2. 启动AI视界启动器
echo 3. 停止所有Electron进程
echo 4. 检查端口占用情况
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto show_processes
if "%choice%"=="2" goto start_launcher
if "%choice%"=="3" goto stop_processes
if "%choice%"=="4" goto check_ports
if "%choice%"=="5" goto exit
goto menu

:show_processes
echo.
echo 当前Electron进程:
echo ================
tasklist /fi "imagename eq electron.exe" /fo table
echo.
echo 详细信息:
wmic process where "name='electron.exe'" get ProcessId,WorkingSetSize,CommandLine /format:table
echo.
pause
goto menu

:start_launcher
echo.
echo 启动AI视界启动器...
echo 检查是否已有进程运行...
tasklist /fi "imagename eq electron.exe" | find "electron.exe" >nul
if not errorlevel 1 (
    echo 警告: 已有Electron进程在运行
    echo 是否继续启动? (Y/N)
    set /p confirm=
    if /i not "%confirm%"=="Y" goto menu
)

echo 启动中...
start "" npm start
echo 启动命令已执行
echo.
pause
goto menu

:stop_processes
echo.
echo 停止所有Electron进程...
tasklist /fi "imagename eq electron.exe" | find "electron.exe" >nul
if errorlevel 1 (
    echo 没有找到Electron进程
) else (
    echo 正在停止进程...
    taskkill /f /im electron.exe
    echo 所有Electron进程已停止
)
echo.
pause
goto menu

:check_ports
echo.
echo 检查端口占用情况:
echo ==================
echo 端口 8399 (后端API):
netstat -an | findstr :8399
echo.
echo 端口 8188 (ComfyUI):
netstat -an | findstr :8188
echo.
pause
goto menu

:exit
echo 退出程序...
exit /b 0
