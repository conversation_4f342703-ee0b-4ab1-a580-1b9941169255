@echo off
title AI-Vision Installer Final Test
color 0A

echo.
echo ========================================
echo AI-Vision Installer Final Test
echo ========================================
echo.

echo Applied Fixes:
echo   - Disk space detection (multiple methods)
echo   - Configuration environment (safe operations)
echo   - Download system (redirect handling)
echo   - System compatibility (encoding fixes)
echo.

echo Environment Check:
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do echo   Node.js: %%i
for /f "tokens=*" %%i in ('npm --version') do echo   npm: %%i
echo.

echo Checking dependencies...
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Dependencies ready
echo.

echo Starting installer...
echo.
echo NOTICE: All known issues have been fixed
echo The installer should now run completely
echo.

echo Press any key to start...
pause >nul

npm start

if %errorlevel% equ 0 (
    echo.
    echo SUCCESS: Installer completed!
) else (
    echo.
    echo ERROR: Installer failed with code %errorlevel%
)

echo.
echo Test completed
echo.
pause
