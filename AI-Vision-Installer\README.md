# AI-Vision 智能安装器

一键安装 ComfyUI + AI-Vision 启动器的完整环境。

## 🎯 功能特性

- ✅ **自动配置 Python 3.12.9 便携环境**
- ✅ **安装 PyTorch 2.7.1 + CUDA 12.8**
- ✅ **智能镜像切换，加速下载**
- ✅ **完整的卸载功能**
- ✅ **用户友好的图形界面**
- ✅ **实时安装进度显示**

## 📋 系统要求

### 最低要求
- **操作系统**: Windows 10/11 (64位)
- **内存**: 8GB RAM
- **存储空间**: 50GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Windows 11 (64位)
- **内存**: 16GB+ RAM
- **GPU**: NVIDIA GPU (支持CUDA 12.8)
- **存储空间**: 60GB+ 可用空间 (SSD推荐)

## 🚀 快速开始

### 方法一: 下载预构建版本
1. 从 [Releases](../../releases) 页面下载最新版本
2. 运行 `AI-Vision-Installer-Setup-x.x.x.exe`
3. 按照安装向导完成安装

### 方法二: 从源码构建
1. 克隆仓库
```bash
git clone <repository-url>
cd AI-Vision-Installer
```

2. 安装依赖
```bash
npm install
```

3. 构建安装器
```bash
node build-installer.js
```

4. 运行生成的安装器
```bash
./dist/AI-Vision-Installer-Setup-x.x.x.exe
```

## 🔧 开发

### 项目结构
```
AI-Vision-Installer/
├── installer.html              # 安装器界面
├── installer-main.js           # 前端逻辑
├── installer-electron-main.js  # Electron主进程
├── installer-preload.js        # 预加载脚本
├── installer-config.js         # 配置文件
├── package.json               # 项目配置
├── build-installer.js         # 构建脚本
└── README.md                  # 说明文档
```

### 开发模式运行
```bash
npm start
```

### 构建安装器
```bash
# 构建Windows安装器
npm run build:win

# 构建便携版
npm run build:portable

# 使用构建脚本
node build-installer.js
```

## 📦 安装流程

安装器将按以下步骤执行：

1. **环境检测** (5%)
   - 检测操作系统和硬件
   - 检测GPU和CUDA支持
   - 验证网络连接

2. **下载Python** (10%)
   - 下载Python 3.12.9便携版
   - 验证文件完整性

3. **配置Python环境** (5%)
   - 解压Python包
   - 配置环境变量
   - 安装pip

4. **下载ComfyUI** (15%)
   - 从GitHub下载ComfyUI
   - 智能镜像切换

5. **配置ComfyUI** (5%)
   - 解压ComfyUI包
   - 创建启动脚本

6. **安装PyTorch** (30%)
   - 安装PyTorch 2.7.1
   - 安装TorchVision和TorchAudio
   - 验证CUDA支持

7. **安装依赖包** (20%)
   - 安装ComfyUI依赖
   - 安装后端服务依赖

8. **配置环境** (5%)
   - 复制启动器文件
   - 配置启动脚本
   - 创建快捷方式

9. **完成安装** (5%)
   - 清理临时文件
   - 创建卸载程序
   - 验证安装

## 🎨 用户界面

安装器采用现代化的霓虹风格界面，包含：

- **欢迎页面**: 显示功能特性
- **路径选择**: 允许用户选择安装位置
- **安装进度**: 实时显示安装状态和日志
- **完成页面**: 显示安装信息和后续操作

## ⚙️ 配置选项

### 安装路径
- 默认路径: `C:\AI-Vision`
- 支持自定义路径选择
- 自动检测磁盘空间

### 组件版本
- **Python**: 3.12.9 (便携式)
- **PyTorch**: 2.7.1+cu128
- **TorchVision**: 0.22.1+cu128
- **CUDA**: 12.8

### 镜像配置
- **Python**: 官方源 + 国内镜像
- **PyTorch**: 官方源 + 清华镜像
- **ComfyUI**: GitHub + 代理镜像

## 🛠️ 故障排除

### 常见问题

**Q: 安装过程中网络错误**
A: 安装器会自动切换到备用镜像，请确保网络连接稳定

**Q: 磁盘空间不足**
A: 请确保至少有50GB可用空间，推荐60GB以上

**Q: GPU不支持CUDA**
A: 安装器会自动检测并安装CPU版本的PyTorch

**Q: 安装失败**
A: 查看安装日志，常见原因包括网络问题、权限不足、磁盘空间不足

### 日志文件
安装日志保存在: `%TEMP%\ai-vision-installer.log`

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如果您遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查 [Issues](../../issues) 页面
3. 提交新的 Issue 描述问题

---

**注意**: 首次安装可能需要较长时间，请耐心等待并保持网络连接稳定。
