{"name": "is-ci", "version": "3.0.1", "description": "Detect if the current environment is a CI server", "bin": "bin.js", "main": "index.js", "author": "<PERSON> <<EMAIL>> (https://twitter.com/wa7son)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/watson/is-ci.git"}, "bugs": {"url": "https://github.com/watson/is-ci/issues"}, "homepage": "https://github.com/watson/is-ci", "keywords": ["ci", "continuous", "integration", "test", "detect"], "coordinates": [55.778272, 12.593116], "scripts": {"test": "standard && node test.js"}, "dependencies": {"ci-info": "^3.2.0"}, "devDependencies": {"clear-module": "^4.1.1", "standard": "^16.0.4"}}