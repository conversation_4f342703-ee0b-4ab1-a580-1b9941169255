# AI-Vision 智能安装器使用说明

## 📋 概述

AI-Vision 智能安装器是一个一键式安装工具，可以帮助用户快速安装 ComfyUI 和 AI-Vision 启动器的完整环境。安装器采用现代化的界面设计，提供了直观的安装流程和实时进度显示。

## 🚀 主要功能

- ✅ **自动配置 Python 3.12.9 便携环境**
- ✅ **安装 PyTorch 2.7.1 + CUDA 12.8**
- ✅ **智能镜像切换，加速下载**
- ✅ **完整的卸载功能**
- ✅ **用户友好的图形界面**

## 📦 项目结构

安装器项目包含以下主要文件：

```
AI-Vision-Installer/
├── installer.html              # 安装器界面
├── installer-main.js           # 前端逻辑
├── installer-electron-main.js  # Electron主进程
├── installer-preload.js        # 预加载脚本
├── installer-config.js         # 配置文件
├── package.json                # 项目配置
├── build-installer.js          # 构建脚本
├── start-installer.bat         # 启动脚本
├── test-installer.bat          # 测试脚本
├── preview-installer.html      # 预览页面
└── README.md                   # 说明文档
```

## 🔧 使用方法

### 1. 开发和测试

如果您想要测试或修改安装器，可以按照以下步骤操作：

1. **安装Node.js**：
   - 从 [Node.js官网](https://nodejs.org/) 下载并安装最新版本

2. **运行测试脚本**：
   - 双击 `test-installer.bat` 运行测试模式
   - 这将启动安装器但不会执行实际安装

3. **修改配置**：
   - 编辑 `installer-config.js` 文件可以修改安装参数
   - 可以调整Python版本、PyTorch版本、下载镜像等

4. **构建安装器**：
   - 运行 `node build-installer.js` 构建完整安装器
   - 生成的安装器将保存在 `dist` 目录中

### 2. 分发给用户

构建完成后，您可以通过以下方式分发安装器：

1. **安装包方式**：
   - 分发 `dist/AI-Vision-Installer-Setup-x.x.x.exe` 文件
   - 用户运行此文件即可启动安装向导

2. **便携版方式**：
   - 分发 `dist/AI-Vision-Installer-Portable-x.x.x.exe` 文件
   - 无需安装，直接运行即可使用

3. **源码方式**：
   - 分发整个 `AI-Vision-Installer` 目录
   - 用户运行 `start-installer.bat` 启动安装器

## 📋 安装流程说明

安装器将按照以下步骤执行安装：

1. **环境检测** (5%)
   - 检测操作系统和硬件
   - 检测GPU和CUDA支持
   - 验证网络连接

2. **下载Python** (10%)
   - 下载Python 3.12.9便携版
   - 验证文件完整性

3. **配置Python环境** (5%)
   - 解压Python包
   - 配置环境变量
   - 安装pip

4. **下载ComfyUI** (15%)
   - 从GitHub下载ComfyUI
   - 智能镜像切换

5. **配置ComfyUI** (5%)
   - 解压ComfyUI包
   - 创建启动脚本

6. **安装PyTorch** (30%)
   - 安装PyTorch 2.7.1
   - 安装TorchVision和TorchAudio
   - 验证CUDA支持

7. **安装依赖包** (20%)
   - 安装ComfyUI依赖
   - 安装后端服务依赖

8. **配置环境** (5%)
   - 复制启动器文件
   - 配置启动脚本
   - 创建快捷方式

9. **完成安装** (5%)
   - 清理临时文件
   - 创建卸载程序
   - 验证安装

## ⚙️ 配置选项

您可以通过修改 `installer-config.js` 文件来自定义安装器的行为：

### 安装路径
```javascript
installation: {
    defaultPath: 'C:\\AI-Vision',
    allowCustomPath: true,
    requiredSpace: 53687091200, // 50GB
}
```

### Python版本
```javascript
python: {
    version: '3.12.9',
    type: 'portable',
    filename: 'python-3.12.9-embed-amd64.zip',
}
```

### PyTorch配置
```javascript
pytorch: {
    cudaVersion: '12.8',
    torchVersion: '2.7.1',
    packages: [
        'torch==2.7.1+cu128',
        'torchvision==0.22.1+cu128',
        'torchaudio==2.7.1+cu128'
    ],
}
```

### 下载镜像
```javascript
mirrors: {
    python: {
        official: 'https://www.python.org/ftp/python/',
        china: 'https://npm.taobao.org/mirrors/python/',
    },
    pytorch: {
        official: 'https://download.pytorch.org/whl/cu128',
        china: 'https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/',
    },
}
```

## 🛠️ 故障排除

### 常见问题

1. **安装器无法启动**
   - 确保已安装Node.js
   - 检查是否有杀毒软件阻止运行
   - 尝试以管理员身份运行

2. **下载速度慢**
   - 安装器会自动切换到最快的镜像
   - 可以在配置文件中添加更多镜像源

3. **安装失败**
   - 检查磁盘空间是否充足
   - 确保网络连接稳定
   - 查看安装日志了解详细错误

4. **GPU不支持CUDA**
   - 安装器会自动检测并安装CPU版本的PyTorch
   - 可以在配置文件中修改PyTorch版本

## 📝 注意事项

1. **系统要求**
   - Windows 10/11 (64位)
   - 至少50GB可用磁盘空间
   - 8GB以上内存
   - 稳定的网络连接

2. **安装时间**
   - 完整安装可能需要30-60分钟，取决于网络速度和电脑性能
   - 下载PyTorch是最耗时的步骤

3. **安装位置**
   - 默认安装在C盘，可以自定义安装路径
   - 建议安装在SSD上以获得更好的性能

4. **卸载方式**
   - 可以通过控制面板的"程序和功能"卸载
   - 也可以使用安装目录中的卸载程序

## 🤝 获取帮助

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

1. 查看安装日志：`%TEMP%\ai-vision-installer.log`
2. 检查README文件中的故障排除部分
3. 提交Issue描述您的问题

---

祝您使用愉快！
