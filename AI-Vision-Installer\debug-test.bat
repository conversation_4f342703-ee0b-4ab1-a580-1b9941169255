@echo off
title AI-Vision 安装器调试
color 0E

echo.
echo ========================================
echo AI-Vision 安装器调试脚本
echo ========================================
echo.

:: 显示基本信息
echo 📋 系统信息:
echo   - 当前目录: %CD%
echo   - 用户名: %USERNAME%
echo   - 计算机名: %COMPUTERNAME%
echo   - 操作系统: %OS%
echo.

:: 检查文件是否存在
echo 📁 检查文件:
if exist "installer.html" (
    echo   ✅ installer.html
) else (
    echo   ❌ installer.html [缺失]
)

if exist "installer-main.js" (
    echo   ✅ installer-main.js
) else (
    echo   ❌ installer-main.js [缺失]
)

if exist "installer-electron-main.js" (
    echo   ✅ installer-electron-main.js
) else (
    echo   ❌ installer-electron-main.js [缺失]
)

if exist "package.json" (
    echo   ✅ package.json
) else (
    echo   ❌ package.json [缺失]
)

if exist "node_modules" (
    echo   ✅ node_modules [目录存在]
) else (
    echo   ❌ node_modules [目录不存在]
)

echo.

:: 检查Node.js
echo 🔍 检查Node.js:
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ Node.js 已安装
    node --version
    echo   路径: 
    where node
) else (
    echo   ❌ Node.js 未找到
    echo.
    echo   请安装Node.js:
    echo   1. 访问 https://nodejs.org/
    echo   2. 下载并安装最新版本
    echo   3. 重启命令提示符
    echo.
    goto :end
)

echo.

:: 检查npm
echo 🔍 检查npm:
where npm >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ npm 已安装
    npm --version
) else (
    echo   ❌ npm 未找到
    goto :end
)

echo.

:: 如果node_modules不存在，尝试安装依赖
if not exist "node_modules" (
    echo 📦 安装依赖包:
    echo   正在运行: npm install
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo   ❌ npm install 失败
        echo.
        echo   尝试清理缓存:
        npm cache clean --force
        echo   再次尝试安装:
        npm install
    ) else (
        echo   ✅ 依赖包安装成功
    )
    echo.
)

:: 检查package.json中的scripts
echo 📋 检查package.json scripts:
if exist "package.json" (
    findstr "start" package.json
    echo.
)

:: 尝试启动
echo 🚀 尝试启动安装器:
echo   运行命令: npm start
echo   如果出现错误，请查看上面的输出信息
echo.

echo 按任意键开始启动...
pause >nul

npm start

echo.
echo 启动完成，错误代码: %errorlevel%

:end
echo.
echo ========================================
echo 调试完成
echo ========================================
echo.
echo 如果仍有问题，请检查:
echo 1. Node.js是否正确安装
echo 2. 网络连接是否正常
echo 3. 是否有杀毒软件阻止运行
echo 4. 是否需要管理员权限
echo.
echo 按任意键退出...
pause >nul
