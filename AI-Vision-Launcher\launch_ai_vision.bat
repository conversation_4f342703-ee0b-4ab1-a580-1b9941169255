@echo off
  title AI Vision Launcher

  echo ====================================
  echo       AI Vision Launcher
  echo ====================================

  cd /d "%~dp0"

  echo Current directory: %CD%
  echo.

  python --version >nul 2>&1
  if errorlevel 1 (
      echo ERROR: Python not found
      echo Please install Python
      pause
      exit /b 1
  )

  echo Python found
  echo.

  if exist "backend\ai_vision_api.py" (
      echo Starting AI Vision API server...
      cd backend
      python ai_vision_api.py
  ) else (
      echo ERROR: Backend not found
      pause
  )

  pause