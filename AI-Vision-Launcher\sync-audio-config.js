/**
 * 音效配置同步脚本
 * 从配置工具复制配置到启动器
 */

// 从配置工具获取配置
function getConfigFromTool() {
    // 这是从配置工具控制台获取的实际配置
    return {
        "click": "custom/按钮科技音效.WAV",
        "click-primary": "custom/按钮科技音效.WAV",
        "hover": "custom/按钮科技音效.WAV",
        "switch": "custom/按钮科技音效.WAV",
        "input": "custom/按钮科技音效.WAV",
        "tab-switch": "custom/按钮科技音效.WAV",
        "success": "custom/任务完成音效.WAV",
        "warning": "custom/提醒、警告音效.WAV",
        "error": "custom/提醒、警告音效.WAV",
        "notification": "custom/提醒、警告音效.WAV",
        "confirm": "custom/导航标签点击的声音.WAV",
        "complete": "custom/操作成功反馈音效.WAV",
        "startup": "custom/启动程序音效.WAV",
        "startup-success": "custom/任务完成音效.WAV",
        "shutdown": "custom/关闭comfyui.WAV",
        "shutdown-success": "custom/comfyui关闭成功的音效.WAV",
        "app-close": "custom/关闭启动器窗口的音效.WAV",
        "loading": "custom/按钮科技音效.WAV"
    };
}

// 应用配置到启动器
function applyConfigToLauncher() {
    const config = getConfigFromTool();
    
    if (window.audioManager) {
        console.log('应用音效配置到启动器...');
        console.log('当前音效映射:', window.audioManager.soundMap);
        
        // 应用配置
        Object.assign(window.audioManager.soundMap, config);
        
        console.log('配置应用成功！');
        console.log('更新后的音效映射:', window.audioManager.soundMap);
        
        // 保存到localStorage供下次使用
        const configData = {
            timestamp: new Date().toISOString(),
            soundMap: config,
            version: '1.0',
            source: 'manual-sync'
        };
        
        localStorage.setItem('launcher_audio_config', JSON.stringify(configData));
        console.log('配置已保存到localStorage');
        
        return true;
    } else {
        console.error('音效管理器不可用');
        return false;
    }
}

// 测试音效播放
function testAudioEffects() {
    if (!window.audioManager) {
        console.error('音效管理器不可用');
        return;
    }
    
    const testSounds = ['click', 'success', 'startup', 'shutdown'];
    
    console.log('开始测试音效...');
    
    testSounds.forEach((soundName, index) => {
        setTimeout(() => {
            console.log(`播放测试音效: ${soundName}`);
            try {
                window.audioManager.play(soundName);
            } catch (error) {
                console.error(`播放 ${soundName} 失败:`, error);
            }
        }, index * 1000);
    });
}

// 检查配置状态
function checkConfigStatus() {
    console.log('=== 音效配置状态检查 ===');
    
    // 检查音效管理器
    if (window.audioManager) {
        console.log('✅ 音效管理器可用');
        console.log('当前音效映射:', window.audioManager.soundMap);
    } else {
        console.log('❌ 音效管理器不可用');
    }
    
    // 检查localStorage
    const configData = localStorage.getItem('launcher_audio_config');
    if (configData) {
        console.log('✅ localStorage中有配置数据');
        try {
            const config = JSON.parse(configData);
            console.log('配置内容:', config);
        } catch (error) {
            console.log('❌ 配置数据解析失败:', error);
        }
    } else {
        console.log('❌ localStorage中没有配置数据');
    }
    
    // 检查音效文件
    const testFiles = [
        'assets/sounds/custom/按钮科技音效.WAV',
        'assets/sounds/custom/任务完成音效.WAV',
        'assets/sounds/custom/关闭comfyui.WAV'
    ];
    
    console.log('检查音效文件...');
    testFiles.forEach(file => {
        const audio = new Audio(file);
        audio.addEventListener('canplaythrough', () => {
            console.log(`✅ 文件可用: ${file}`);
        });
        audio.addEventListener('error', (e) => {
            console.log(`❌ 文件不可用: ${file}`, e);
        });
    });
}

// 清空配置
function clearConfig() {
    localStorage.removeItem('launcher_audio_config');
    localStorage.removeItem('launcher_audio_config_backup');
    localStorage.removeItem('audio_config_for_launcher');
    console.log('所有音效配置已清空');
}

// 导出函数供控制台使用
window.syncAudioConfig = {
    apply: applyConfigToLauncher,
    test: testAudioEffects,
    check: checkConfigStatus,
    clear: clearConfig,
    getConfig: getConfigFromTool
};

console.log('音效配置同步脚本已加载');
console.log('使用方法:');
console.log('  syncAudioConfig.apply() - 应用配置');
console.log('  syncAudioConfig.test() - 测试音效');
console.log('  syncAudioConfig.check() - 检查状态');
console.log('  syncAudioConfig.clear() - 清空配置');
