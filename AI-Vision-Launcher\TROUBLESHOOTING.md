# AI视界启动器 - 故障排除指南

## 🔧 常见问题解决方案

### 1. 端口占用问题 ✅ 已修复

**问题**: `ERROR: [Errno 10048] error while attempting to bind on address`

**解决方案**: 
- ✅ 应用已支持自动端口检测
- 如果8400端口被占用，会自动使用8401、8402等
- 控制台会显示实际使用的端口

**手动解决** (如果仍有问题):
```bash
# Windows
netstat -ano | findstr :8400
taskkill /PID <进程ID> /F

# Linux/macOS  
lsof -i :8400
kill <进程ID>
```

### 2. Python依赖问题

**问题**: `ModuleNotFoundError: No module named 'fastapi'`

**解决方案**:
```bash
# 方式1: 使用ComfyUI虚拟环境
D:\AI\ComfyUI\venv\Scripts\activate
pip install fastapi uvicorn python-multipart

# 方式2: 全局安装
pip install fastapi uvicorn python-multipart

# 方式3: 使用requirements.txt
cd D:\AI\ComfyUI\AI-Vision-Launcher\backend
pip install -r requirements.txt
```

### 3. Node.js依赖问题

**问题**: `Cannot find module 'electron'`

**解决方案**:
```bash
cd D:\AI\ComfyUI\AI-Vision-Launcher
npm install
```

**如果npm install失败**:
```bash
# 清除缓存重试
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 4. Python路径问题

**问题**: 找不到Python执行文件

**解决方案**: 
应用会自动按以下顺序查找Python:
1. `D:\AI\ComfyUI\venv\Scripts\python.exe` (ComfyUI虚拟环境)
2. `python` (系统Python)
3. `python3` (系统Python3)

**手动指定Python路径**:
```bash
# 方式1: 设置环境变量
set PYTHON_PATH=D:\AI\ComfyUI\venv\Scripts\python.exe
npm start

# 方式2: 修改main.js中的findPythonExecutable方法
```

### 5. 编码问题 (中文乱码)

**问题**: 控制台显示乱码

**解决方案**: 已在代码中处理
- 设置了 `PYTHONIOENCODING=utf-8`
- Windows环境自动配置UTF-8输出

### 6. 插件扫描失败

**问题**: 插件列表为空或加载失败

**检查清单**:
- ✅ ComfyUI的custom_nodes目录是否存在
- ✅ 目录权限是否正确
- ✅ ComfyUI-Manager是否已安装

**解决方案**:
```bash
# 检查目录结构
D:\AI\ComfyUI\
├── custom_nodes\          # 必须存在
│   ├── comfyui-manager\   # ComfyUI-Manager目录
│   └── 其他插件目录...
└── AI-Vision-Launcher\    # AI视界启动器
```

### 7. 网络连接问题

**问题**: 无法连接到后端API

**解决方案**:
1. 检查防火墙是否阻止本地连接
2. 确认127.0.0.1/localhost解析正常
3. 查看控制台是否显示实际端口号

### 8. Electron窗口不显示

**问题**: 应用启动但窗口不显示

**解决方案**:
```bash
# 开发模式启动，查看详细日志
electron main.js --dev

# 检查是否最小化到系统托盘
# 双击系统托盘图标或右键选择"显示AI视界"
```

## 🚀 推荐启动流程

### Windows用户 (推荐)
```cmd
# 1. 使用批处理脚本
双击 quick_start.bat

# 2. 或者手动启动
cd D:\AI\ComfyUI\AI-Vision-Launcher
D:\AI\ComfyUI\venv\Scripts\activate
npm start
```

### 开发调试模式
```bash
cd D:\AI\ComfyUI\AI-Vision-Launcher
electron main.js --dev
```

## 📋 环境检查清单

运行前请确认:
- ✅ Node.js已安装 (>= 14.0.0)
- ✅ Python已安装 (>= 3.7)
- ✅ ComfyUI环境正常
- ✅ 网络连接正常
- ✅ 防火墙允许本地连接

## 🔍 日志收集

如果问题仍然存在，请收集以下信息:

1. **系统信息**:
   ```bash
   node --version
   python --version
   npm --version
   ```

2. **启动日志**: 控制台完整输出

3. **错误信息**: 具体的错误提示

4. **目录结构**: ComfyUI和AI-Vision-Launcher的目录布局

## 📞 获取支持

如果以上方案都无法解决问题:
1. 检查README.md中的系统要求
2. 查看GitHub Issues
3. 提交新的Issue并附上日志信息

---

**提示**: 大部分问题都是环境配置导致的，请仔细检查Python和Node.js环境配置。