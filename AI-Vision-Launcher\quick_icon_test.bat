@echo off
chcp 65001 >nul
echo AI视界启动器 - 快速图标测试工具
echo ================================
echo.
echo 当前已应用优化配置：
echo - 使用高分辨率PNG图标 (推荐)
echo - 动态图标处理，自动选择最佳格式
echo - 针对任务栏显示优化的多尺寸ICO文件
echo.
echo 如果任务栏图标仍然模糊，请尝试以下步骤：
echo.
echo 1. 重启应用程序 (关闭后重新运行 npm start)
echo 2. 检查Windows显示缩放设置
echo 3. 尝试其他图标配置
echo.
echo 可用的测试选项：
echo [1] 查看当前图标质量分析
echo [2] 重新生成优化图标
echo [3] 测试不同图标配置
echo [4] 启动应用程序
echo [0] 退出
echo.
set /p choice="请选择 (0-4): "

if "%choice%"=="1" (
    echo.
    echo 正在分析图标质量...
    python check_icon_quality.py
    pause
    goto :start
)

if "%choice%"=="2" (
    echo.
    echo 正在重新生成优化图标...
    python fix_taskbar_icon.py
    echo.
    echo 图标已重新生成，请重启应用程序测试效果
    pause
    goto :start
)

if "%choice%"=="3" (
    echo.
    echo 启动图标配置测试工具...
    python test_icon_configs.py
    pause
    goto :start
)

if "%choice%"=="4" (
    echo.
    echo 启动应用程序...
    npm start
    goto :end
)

if "%choice%"=="0" (
    goto :end
)

echo 无效选择，请重试
pause
goto :start

:start
cls
goto :main

:main
goto :start

:end
echo 测试工具已退出
pause
