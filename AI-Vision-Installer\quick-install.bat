@echo off
echo AI-Vision Real Installation
echo ===========================
echo.

echo WARNING: This is a REAL installation
echo - Will download 4-5GB of files
echo - Takes 30-60 minutes
echo - Requires stable internet
echo.

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Install from: https://nodejs.org/
    pause
    exit
)

echo Node.js: OK
echo.

set /p confirm="Continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Cancelled
    pause
    exit
)

echo.
echo Installing dependencies...
if not exist "node_modules" npm install

echo.
echo Starting real installation...
echo Keep this window open!
echo.

npm start

echo.
echo Installation finished
pause
