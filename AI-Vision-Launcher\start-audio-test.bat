@echo off
title Audio Test Server

echo Starting AI Vision Launcher Audio Test Server...
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python or use the conda environment
    echo.
    echo Trying with conda environment...
    call conda activate ./venv
    python start-audio-test-server.py
) else (
    echo Python found, starting server...
    python start-audio-test-server.py
)

echo.
pause
