@echo off
title AI Vision Launcher

echo ====================================
echo       AI Vision Launcher
echo ====================================

cd /d "%~dp0"

echo Checking environment...

node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found
    pause
    exit /b 1
)

if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo Starting AI Vision Launcher...
echo.

npm start

if errorlevel 1 (
    echo.
    echo [ERROR] Failed to start
    echo 1. Make sure Node.js is installed
    echo 2. Check if ports 8400-8401 are available
    echo 3. Try reinstalling dependencies
)

pause