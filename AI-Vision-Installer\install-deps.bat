@echo off
title 安装依赖包
echo.
echo ========================================
echo 安装AI-Vision安装器依赖包
echo ========================================
echo.

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js
    echo 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js已安装
node --version
echo.

:: 检查package.json
if not exist "package.json" (
    echo ❌ 错误: 未找到package.json文件
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo ✅ package.json文件存在
echo.

:: 清理旧的node_modules
if exist "node_modules" (
    echo 🧹 清理旧的node_modules目录...
    rmdir /s /q node_modules
)

if exist "package-lock.json" (
    echo 🧹 清理package-lock.json...
    del package-lock.json
)

echo.
echo 📦 开始安装依赖包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

:: 安装依赖
npm install

if %errorlevel% neq 0 (
    echo.
    echo ❌ 依赖包安装失败
    echo.
    echo 尝试解决方案:
    echo 1. 清理npm缓存: npm cache clean --force
    echo 2. 使用淘宝镜像: npm install --registry https://registry.npmmirror.com
    echo 3. 检查网络连接
    echo.
    
    echo 是否尝试使用淘宝镜像重新安装? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo.
        echo 使用淘宝镜像重新安装...
        npm install --registry https://registry.npmmirror.com
    )
) else (
    echo.
    echo ✅ 依赖包安装成功!
    echo.
    echo 现在可以运行以下命令启动安装器:
    echo   npm start
    echo.
    echo 或者直接运行: start-installer.bat
)

echo.
echo 按任意键退出...
pause >nul
