# AI-Vision Installer Simple Test
Write-Host "AI-Vision Installer - Simple Test" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Applied Fixes:" -ForegroundColor Yellow
Write-Host "  - Disk space detection (multiple methods)" -ForegroundColor Green
Write-Host "  - Configuration environment (safe operations)" -ForegroundColor Green
Write-Host "  - Download system (redirect handling)" -ForegroundColor Green
Write-Host "  - System compatibility (encoding fixes)" -ForegroundColor Green
Write-Host ""

Write-Host "Environment Check:" -ForegroundColor Green
Write-Host "  Node.js: $(node --version)" -ForegroundColor White
Write-Host "  npm: $(npm --version)" -ForegroundColor White
Write-Host ""

# Check dependencies
if (!(Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "Dependencies ready" -ForegroundColor Green
Write-Host ""

Write-Host "Starting installer..." -ForegroundColor Green
Write-Host ""
Write-Host "NOTICE: All known issues have been fixed" -ForegroundColor Yellow
Write-Host "The installer should now run completely" -ForegroundColor Yellow
Write-Host ""

Read-Host "Press Enter to start"

try {
    npm start
    Write-Host ""
    Write-Host "SUCCESS: Installer completed!" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test completed" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to exit"
