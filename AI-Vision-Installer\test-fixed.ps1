# AI-Vision Installer Test Script (Fixed Version)
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AI-Vision Installer Test (Fixed)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🔧 Applied Fixes:" -ForegroundColor Yellow
Write-Host "  ✅ Fixed disk space detection" -ForegroundColor Green
Write-Host "  ✅ Fixed download redirect handling" -ForegroundColor Green
Write-Host "  ✅ Fixed Windows command encoding" -ForegroundColor Green
Write-Host "  ✅ Updated ComfyUI download URL" -ForegroundColor Green
Write-Host ""

# Check environment
Write-Host "🔍 Environment Check:" -ForegroundColor Green
Write-Host "  Node.js: $(node --version)" -ForegroundColor White
Write-Host "  npm: $(npm --version)" -ForegroundColor White
Write-Host "  Current Directory: $PWD" -ForegroundColor White
Write-Host ""

# Check if dependencies are installed
if (!(Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "✅ Dependencies installed" -ForegroundColor Green
} else {
    Write-Host "✅ Dependencies already installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "🚀 Starting Fixed Installer..." -ForegroundColor Green
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "                NOTICE" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🧪 This is TEST MODE with fixes applied" -ForegroundColor Yellow
Write-Host "🔧 Fixed issues:" -ForegroundColor Yellow
Write-Host "   - Disk space detection for non-existent paths" -ForegroundColor White
Write-Host "   - HTTP redirect handling for downloads" -ForegroundColor White
Write-Host "   - Windows command encoding issues" -ForegroundColor White
Write-Host "   - ComfyUI download URL" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Press Enter to start the installer..." -ForegroundColor Yellow
Read-Host

try {
    npm start
    Write-Host ""
    Write-Host "✅ Installer test completed successfully" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "❌ Installer failed to start" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Check if all files exist" -ForegroundColor White
    Write-Host "2. Verify Node.js and npm versions" -ForegroundColor White
    Write-Host "3. Try running: npm install --force" -ForegroundColor White
    Write-Host "4. Check antivirus software" -ForegroundColor White
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Test Completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to exit"
