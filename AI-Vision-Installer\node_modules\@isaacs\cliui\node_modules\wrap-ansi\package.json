{"name": "wrap-ansi", "version": "8.1.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": "chalk/wrap-ansi", "funding": "https://github.com/chalk/wrap-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=12"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "devDependencies": {"ava": "^3.15.0", "chalk": "^4.1.2", "coveralls": "^3.1.1", "has-ansi": "^5.0.1", "nyc": "^15.1.0", "tsd": "^0.25.0", "xo": "^0.44.0"}}