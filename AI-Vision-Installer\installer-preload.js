/**
 * AI-Vision 安装器预加载脚本
 * 为渲染进程提供安全的API接口
 */

const { contextBridge, ipcRenderer } = require('electron');

// 暴露安全的API到渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
    // 目录选择
    selectDirectory: () => ipcRenderer.invoke('select-directory'),
    
    // 磁盘空间检查
    getDiskSpace: (path) => ipcRenderer.invoke('get-disk-space', path),
    
    // 安装相关
    startInstallation: (installPath) => ipcRenderer.invoke('start-installation', installPath),
    cancelInstallation: () => ipcRenderer.invoke('cancel-installation'),
    
    // 文件系统操作
    openFolder: (folderPath) => ipcRenderer.invoke('open-folder', folderPath),
    
    // 应用程序控制
    closeInstaller: () => ipcRenderer.invoke('close-installer'),
    launchAIVision: (installPath) => ipcRenderer.invoke('launch-ai-vision', installPath),
    
    // 事件监听
    onInstallationProgress: (callback) => {
        ipcRenderer.on('installation-progress', (event, progress) => {
            callback(progress);
        });
    },
    
    // 移除事件监听器
    removeAllListeners: (channel) => {
        ipcRenderer.removeAllListeners(channel);
    }
});

// 暴露配置到渲染进程
contextBridge.exposeInMainWorld('INSTALLER_CONFIG', require('./installer-config.js'));
