@echo off
title AI-Vision Python Environment Fix Test
color 0D

echo.
echo ========================================
echo AI-Vision Python Environment Fix Test
echo ========================================
echo.

echo Applied fixes:
echo - Added proper pip installation process
echo - Enhanced Python environment configuration
echo - Added path validation before pip commands
echo - Improved error handling and logging
echo.

echo This test will focus on Python environment setup
echo and pip installation which previously failed.
echo.

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit
)

echo Environment ready
echo.

if not exist "node_modules" (
    echo Installing dependencies...
    npm install
)

echo.
echo ========================================
echo STARTING PYTHON ENVIRONMENT TEST
echo ========================================
echo.

echo Key improvements:
echo 1. Automatic pip installation via get-pip.py
echo 2. Path validation before running pip commands
echo 3. Better error messages and logging
echo 4. Proper Python environment configuration
echo.

echo This should fix the pip.exe not found error
echo.

set /p start="Start test? (y/N): "
if /i not "%start%"=="y" (
    echo Test cancelled
    pause
    exit
)

npm start

echo.
echo Test completed
pause
