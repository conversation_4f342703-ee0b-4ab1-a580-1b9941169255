<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音效同步测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #1a1a2e;
            color: #ffffff;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 245, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #00f5ff;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }
        .btn {
            background: #00f5ff;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #ffffff;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 音效同步测试工具</h1>
        
        <div class="section">
            <h3>1. 检查本地存储</h3>
            <button class="btn" onclick="checkLocalStorage()">检查配置数据</button>
            <button class="btn" onclick="clearLocalStorage()">清空配置</button>
        </div>
        
        <div class="section">
            <h3>2. 模拟配置保存</h3>
            <button class="btn" onclick="saveTestConfig()">保存测试配置</button>
            <button class="btn" onclick="loadTestConfig()">加载测试配置</button>
        </div>
        
        <div class="section">
            <h3>3. 音效管理器测试</h3>
            <button class="btn" onclick="initAudioManager()">初始化音效管理器</button>
            <button class="btn" onclick="testAudioMapping()">测试音效映射</button>
        </div>
        
        <div class="section">
            <h3>4. 日志输出</h3>
            <div id="log" class="log">等待操作...</div>
        </div>
    </div>

    <script src="assets/sounds/audio-manager.js"></script>
    <script>
        let testAudioManager = null;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function checkLocalStorage() {
            log('=== 检查本地存储 ===');
            
            // 检查配置工具保存的数据
            const configData = localStorage.getItem('launcher_audio_config');
            log('launcher_audio_config: ' + (configData ? '存在' : '不存在'));
            if (configData) {
                try {
                    const config = JSON.parse(configData);
                    log('配置内容: ' + JSON.stringify(config, null, 2));
                } catch (e) {
                    log('配置解析失败: ' + e.message);
                }
            }
            
            // 检查其他相关数据
            const mappingData = localStorage.getItem('audioConfigTool_mapping');
            log('audioConfigTool_mapping: ' + (mappingData ? '存在' : '不存在'));
            
            const soundMapData = localStorage.getItem('audioConfigTool_soundMap');
            log('audioConfigTool_soundMap: ' + (soundMapData ? '存在' : '不存在'));
        }
        
        function clearLocalStorage() {
            localStorage.removeItem('launcher_audio_config');
            localStorage.removeItem('audioConfigTool_mapping');
            localStorage.removeItem('audioConfigTool_soundMap');
            log('已清空所有音效配置数据');
        }
        
        function saveTestConfig() {
            log('=== 保存测试配置 ===');
            
            const testConfig = {
                timestamp: new Date().toISOString(),
                soundMap: {
                    'click': 'custom/导航标签点击的声音.WAV',
                    'success': 'custom/操作成功反馈音效.WAV',
                    'startup': 'custom/启动comfyui的音效.WAV',
                    'shutdown': 'custom/关闭comfyui.WAV'
                },
                version: '1.0'
            };
            
            localStorage.setItem('launcher_audio_config', JSON.stringify(testConfig));
            log('测试配置已保存: ' + JSON.stringify(testConfig, null, 2));
        }
        
        function loadTestConfig() {
            log('=== 加载测试配置 ===');
            
            const configData = localStorage.getItem('launcher_audio_config');
            if (configData) {
                try {
                    const config = JSON.parse(configData);
                    log('加载的配置: ' + JSON.stringify(config, null, 2));
                } catch (e) {
                    log('配置加载失败: ' + e.message);
                }
            } else {
                log('没有找到配置数据');
            }
        }
        
        async function initAudioManager() {
            log('=== 初始化音效管理器 ===');
            
            try {
                if (window.TechAudioManager) {
                    testAudioManager = new TechAudioManager();
                    await testAudioManager.init();
                    log('音效管理器初始化成功');
                    log('当前音效映射: ' + JSON.stringify(testAudioManager.soundMap, null, 2));
                } else {
                    log('TechAudioManager 类不可用');
                }
            } catch (error) {
                log('音效管理器初始化失败: ' + error.message);
            }
        }
        
        function testAudioMapping() {
            log('=== 测试音效映射 ===');
            
            if (!testAudioManager) {
                log('请先初始化音效管理器');
                return;
            }
            
            // 测试几个关键音效
            const testSounds = ['click', 'success', 'startup', 'shutdown'];
            
            testSounds.forEach(soundName => {
                const mapping = testAudioManager.soundMap[soundName];
                log(`${soundName}: ${mapping || '未配置'}`);
                
                if (mapping) {
                    try {
                        testAudioManager.play(soundName);
                        log(`播放 ${soundName} 成功`);
                    } catch (error) {
                        log(`播放 ${soundName} 失败: ${error.message}`);
                    }
                }
            });
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', () => {
            log('音效同步测试工具已加载');
            checkLocalStorage();
        });
    </script>
</body>
</html>
