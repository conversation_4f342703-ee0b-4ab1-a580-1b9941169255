<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        button {
            background: #00f5ff;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .result {
            background: #2a2a3e;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #ff4757;
            color: #fff;
        }
        .success {
            background: #2ed573;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>插件API测试</h1>
        
        <button onclick="testInstalledPlugins()">测试已安装插件API</button>
        <button onclick="testAvailablePlugins()">测试可用插件API</button>
        <button onclick="testHealthCheck()">测试健康检查</button>
        
        <div id="results"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://127.0.0.1:8399';
        
        function addResult(title, content, type = 'result') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testHealthCheck() {
            try {
                console.log('Testing health check...');
                const response = await fetch(`${BACKEND_URL}/health`);
                const data = await response.json();
                addResult('健康检查', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('健康检查失败', error.message, 'error');
            }
        }
        
        async function testInstalledPlugins() {
            try {
                console.log('Testing installed plugins API...');
                const response = await fetch(`${BACKEND_URL}/nodes/installed`);
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Response data:', data);
                
                addResult(
                    `已安装插件 (${data.nodes?.length || 0}个)`, 
                    JSON.stringify(data, null, 2), 
                    'success'
                );
                
                // 显示插件列表
                if (data.nodes && data.nodes.length > 0) {
                    const pluginList = data.nodes.map(node => 
                        `- ${node.name} (${node.status}) - ${node.description || '无描述'}`
                    ).join('\n');
                    addResult('插件列表', pluginList, 'success');
                }
                
            } catch (error) {
                console.error('Error:', error);
                addResult('已安装插件API失败', error.message, 'error');
            }
        }
        
        async function testAvailablePlugins() {
            try {
                console.log('Testing available plugins API...');
                const response = await fetch(`${BACKEND_URL}/nodes/available`);
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Response data:', data);
                
                addResult(
                    `可用插件 (${data.nodes?.length || 0}个)`, 
                    JSON.stringify(data, null, 2), 
                    'success'
                );
                
            } catch (error) {
                console.error('Error:', error);
                addResult('可用插件API失败', error.message, 'error');
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            console.log('Page loaded, starting tests...');
            testHealthCheck();
        };
    </script>
</body>
</html>
