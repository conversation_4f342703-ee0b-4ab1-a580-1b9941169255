<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音效配置同步测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #1a1a2e;
            color: #ffffff;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(0, 245, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #00f5ff;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }
        .btn {
            background: #00f5ff;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #ffffff;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #0f0;
        }
        .status.error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #f00;
        }
        .status.info {
            background: rgba(0, 245, 255, 0.2);
            border: 1px solid #00f5ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 音效配置同步测试</h1>
        
        <div class="section">
            <h3>1. 配置同步测试</h3>
            <button class="btn" onclick="testSaveConfig()">保存测试配置</button>
            <button class="btn" onclick="testLoadConfig()">加载配置</button>
            <button class="btn" onclick="checkConfigStatus()">检查配置状态</button>
            <button class="btn" onclick="clearAllConfig()">清空所有配置</button>
        </div>
        
        <div class="section">
            <h3>2. localStorage测试</h3>
            <button class="btn" onclick="testLocalStorage()">测试localStorage</button>
            <button class="btn" onclick="testCrossDomain()">测试跨域访问</button>
            <button class="btn" onclick="simulateConfigTool()">模拟配置工具保存</button>
        </div>
        
        <div class="section">
            <h3>3. 音效管理器测试</h3>
            <button class="btn" onclick="initAudioManager()">初始化音效管理器</button>
            <button class="btn" onclick="testAudioSync()">测试音效同步</button>
            <button class="btn" onclick="playTestSound()">播放测试音效</button>
        </div>
        
        <div class="section">
            <h3>4. 状态显示</h3>
            <div id="status" class="status info">等待操作...</div>
        </div>
        
        <div class="section">
            <h3>5. 详细日志</h3>
            <div id="log" class="log">测试工具已加载，请选择操作...</div>
        </div>
    </div>

    <script src="audio-config-sync.js"></script>
    <script src="assets/sounds/audio-manager.js"></script>
    <script>
        let testAudioManager = null;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        async function testSaveConfig() {
            log('=== 测试保存配置 ===');
            setStatus('正在保存测试配置...', 'info');
            
            const testSoundMap = {
                'click': 'custom/导航标签点击的声音.WAV',
                'success': 'custom/操作成功反馈音效.WAV',
                'startup': 'custom/启动comfyui的音效.WAV',
                'shutdown': 'custom/关闭comfyui.WAV'
            };
            
            try {
                if (window.audioConfigSync) {
                    const success = await window.audioConfigSync.saveConfig(testSoundMap);
                    if (success) {
                        log('配置保存成功');
                        setStatus('配置保存成功', 'success');
                    } else {
                        log('配置保存失败');
                        setStatus('配置保存失败', 'error');
                    }
                } else {
                    log('audioConfigSync不可用');
                    setStatus('audioConfigSync不可用', 'error');
                }
            } catch (error) {
                log('保存配置时发生错误: ' + error.message);
                setStatus('保存配置失败: ' + error.message, 'error');
            }
        }
        
        async function testLoadConfig() {
            log('=== 测试加载配置 ===');
            setStatus('正在加载配置...', 'info');
            
            try {
                if (window.audioConfigSync) {
                    const config = await window.audioConfigSync.loadConfig();
                    if (config) {
                        log('配置加载成功: ' + JSON.stringify(config, null, 2));
                        setStatus('配置加载成功', 'success');
                    } else {
                        log('没有找到配置');
                        setStatus('没有找到配置', 'error');
                    }
                } else {
                    log('audioConfigSync不可用');
                    setStatus('audioConfigSync不可用', 'error');
                }
            } catch (error) {
                log('加载配置时发生错误: ' + error.message);
                setStatus('加载配置失败: ' + error.message, 'error');
            }
        }
        
        function checkConfigStatus() {
            log('=== 检查配置状态 ===');
            
            if (window.audioConfigSync) {
                const status = window.audioConfigSync.getConfigStatus();
                log('配置状态: ' + JSON.stringify(status, null, 2));
                
                const hasAnyConfig = Object.values(status).some(v => v);
                if (hasAnyConfig) {
                    setStatus('找到配置数据', 'success');
                } else {
                    setStatus('没有找到任何配置', 'error');
                }
            } else {
                log('audioConfigSync不可用');
                setStatus('audioConfigSync不可用', 'error');
            }
        }
        
        function clearAllConfig() {
            log('=== 清空所有配置 ===');
            
            if (window.audioConfigSync) {
                window.audioConfigSync.clearAllConfig();
                log('所有配置已清空');
                setStatus('所有配置已清空', 'info');
            } else {
                log('audioConfigSync不可用');
                setStatus('audioConfigSync不可用', 'error');
            }
        }
        
        function testLocalStorage() {
            log('=== 测试localStorage ===');
            
            // 测试写入
            const testData = {
                test: true,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('test_config', JSON.stringify(testData));
            log('测试数据已写入localStorage');
            
            // 测试读取
            const readData = localStorage.getItem('test_config');
            if (readData) {
                log('测试数据读取成功: ' + readData);
                setStatus('localStorage工作正常', 'success');
            } else {
                log('测试数据读取失败');
                setStatus('localStorage不工作', 'error');
            }
            
            // 清理
            localStorage.removeItem('test_config');
        }
        
        function testCrossDomain() {
            log('=== 测试跨域访问 ===');
            
            // 检查当前域名和端口
            log('当前URL: ' + window.location.href);
            log('当前域名: ' + window.location.hostname);
            log('当前端口: ' + window.location.port);
            
            // 检查localStorage中的所有音效相关数据
            const keys = Object.keys(localStorage);
            const audioKeys = keys.filter(key => 
                key.includes('audio') || 
                key.includes('launcher') || 
                key.includes('config')
            );
            
            log('找到的音效相关键: ' + JSON.stringify(audioKeys));
            
            audioKeys.forEach(key => {
                const value = localStorage.getItem(key);
                log(`${key}: ${value ? '有数据' : '无数据'}`);
            });
        }
        
        function simulateConfigTool() {
            log('=== 模拟配置工具保存 ===');
            
            // 模拟配置工具的保存逻辑
            const configData = {
                timestamp: new Date().toISOString(),
                soundMap: {
                    'click': 'custom/导航标签点击的声音.WAV',
                    'success': 'custom/操作成功反馈音效.WAV',
                    'startup': 'custom/启动comfyui的音效.WAV',
                    'shutdown': 'custom/关闭comfyui.WAV'
                },
                version: '1.0',
                source: 'test-tool'
            };
            
            // 保存到多个位置
            localStorage.setItem('launcher_audio_config', JSON.stringify(configData));
            localStorage.setItem('audio_config_for_launcher', JSON.stringify(configData));
            localStorage.setItem('launcher_audio_config_backup', JSON.stringify(configData));
            
            log('模拟配置已保存到多个位置');
            setStatus('模拟配置保存完成', 'success');
        }
        
        async function initAudioManager() {
            log('=== 初始化音效管理器 ===');
            
            try {
                if (window.TechAudioManager) {
                    testAudioManager = new TechAudioManager();
                    await testAudioManager.init();
                    log('音效管理器初始化成功');
                    log('当前音效映射: ' + JSON.stringify(testAudioManager.soundMap, null, 2));
                    setStatus('音效管理器初始化成功', 'success');
                } else {
                    log('TechAudioManager不可用');
                    setStatus('TechAudioManager不可用', 'error');
                }
            } catch (error) {
                log('音效管理器初始化失败: ' + error.message);
                setStatus('音效管理器初始化失败', 'error');
            }
        }
        
        async function testAudioSync() {
            log('=== 测试音效同步 ===');
            
            if (!testAudioManager) {
                log('请先初始化音效管理器');
                setStatus('请先初始化音效管理器', 'error');
                return;
            }
            
            try {
                await testAudioManager.loadConfigToolSettings();
                log('音效同步测试完成');
                log('更新后的音效映射: ' + JSON.stringify(testAudioManager.soundMap, null, 2));
                setStatus('音效同步测试完成', 'success');
            } catch (error) {
                log('音效同步测试失败: ' + error.message);
                setStatus('音效同步测试失败', 'error');
            }
        }
        
        function playTestSound() {
            log('=== 播放测试音效 ===');
            
            if (!testAudioManager) {
                log('请先初始化音效管理器');
                setStatus('请先初始化音效管理器', 'error');
                return;
            }
            
            try {
                testAudioManager.play('click');
                log('播放点击音效');
                setStatus('播放测试音效', 'info');
            } catch (error) {
                log('播放音效失败: ' + error.message);
                setStatus('播放音效失败', 'error');
            }
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', () => {
            log('音效配置同步测试工具已加载');
            checkConfigStatus();
        });
    </script>
</body>
</html>
