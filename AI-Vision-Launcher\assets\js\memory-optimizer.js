/**
 * 内存管理和性能优化模块
 * 防止内存泄漏，优化DOM操作和事件处理
 */

class MemoryOptimizer {
    constructor() {
        this.eventListeners = new Map(); // 跟踪事件监听器
        this.timers = new Set(); // 跟踪定时器
        this.observers = new Set(); // 跟踪观察者
        this.cleanupCallbacks = new Set(); // 清理回调
        
        // 自动清理机制
        this.setupAutoCleanup();
    }

    /**
     * 安全添加事件监听器（自动跟踪以便清理）
     */
    addEventListener(element, event, handler, options = {}) {
        if (!element || !event || !handler) return;

        const key = `${element.constructor.name}_${event}_${Date.now()}`;
        const wrappedHandler = (...args) => {
            try {
                return handler(...args);
            } catch (error) {
                console.error(`Event handler error for ${event}:`, error);
            }
        };

        element.addEventListener(event, wrappedHandler, options);
        
        this.eventListeners.set(key, {
            element,
            event,
            handler: wrappedHandler,
            options
        });

        return key; // 返回key以便手动清理
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(key) {
        const listener = this.eventListeners.get(key);
        if (listener) {
            listener.element.removeEventListener(
                listener.event, 
                listener.handler, 
                listener.options
            );
            this.eventListeners.delete(key);
        }
    }

    /**
     * 安全设置定时器
     */
    setTimeout(callback, delay) {
        const id = setTimeout(() => {
            this.timers.delete(id);
            try {
                callback();
            } catch (error) {
                console.error('Timer callback error:', error);
            }
        }, delay);
        
        this.timers.add(id);
        return id;
    }

    /**
     * 安全设置间隔定时器
     */
    setInterval(callback, delay) {
        const id = setInterval(() => {
            try {
                callback();
            } catch (error) {
                console.error('Interval callback error:', error);
            }
        }, delay);
        
        this.timers.add(id);
        return id;
    }

    /**
     * 清除定时器
     */
    clearTimer(id) {
        clearTimeout(id);
        clearInterval(id);
        this.timers.delete(id);
    }

    /**
     * 创建DOM元素的优化版本
     */
    createElement(tagName, options = {}) {
        const element = document.createElement(tagName);
        
        // 应用样式
        if (options.style) {
            if (typeof options.style === 'string') {
                element.style.cssText = options.style;
            } else {
                Object.assign(element.style, options.style);
            }
        }

        // 设置属性
        if (options.attributes) {
            Object.entries(options.attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });
        }

        // 设置文本内容
        if (options.textContent) {
            element.textContent = options.textContent;
        }

        // 设置HTML内容
        if (options.innerHTML) {
            element.innerHTML = options.innerHTML;
        }

        return element;
    }

    /**
     * 批量DOM操作优化
     */
    batchDOMUpdate(container, updateFn) {
        const fragment = document.createDocumentFragment();
        const originalParent = container.parentNode;
        
        // 临时移除容器以避免重排
        if (originalParent) {
            originalParent.removeChild(container);
        }

        try {
            updateFn(container, fragment);
            if (fragment.hasChildNodes()) {
                container.appendChild(fragment);
            }
        } catch (error) {
            console.error('Batch DOM update error:', error);
        } finally {
            // 重新插入容器
            if (originalParent) {
                originalParent.appendChild(container);
            }
        }
    }

    /**
     * 虚拟滚动实现（用于大列表）
     */
    createVirtualList(container, items, renderItem, itemHeight = 50) {
        const viewport = this.createElement('div', {
            style: {
                height: '100%',
                overflow: 'auto'
            }
        });

        const content = this.createElement('div', {
            style: {
                position: 'relative',
                height: `${items.length * itemHeight}px`
            }
        });

        viewport.appendChild(content);
        container.appendChild(viewport);

        let visibleStart = 0;
        let visibleEnd = 0;
        const visibleElements = new Map();

        const updateVisible = () => {
            const scrollTop = viewport.scrollTop;
            const viewportHeight = viewport.clientHeight;
            
            const newStart = Math.floor(scrollTop / itemHeight);
            const newEnd = Math.min(items.length, newStart + Math.ceil(viewportHeight / itemHeight) + 1);

            // 移除不可见元素
            for (let i = visibleStart; i < newStart; i++) {
                const element = visibleElements.get(i);
                if (element) {
                    content.removeChild(element);
                    visibleElements.delete(i);
                }
            }

            for (let i = newEnd; i < visibleEnd; i++) {
                const element = visibleElements.get(i);
                if (element) {
                    content.removeChild(element);
                    visibleElements.delete(i);
                }
            }

            // 添加新可见元素
            for (let i = newStart; i < newEnd; i++) {
                if (!visibleElements.has(i) && items[i]) {
                    const element = renderItem(items[i], i);
                    element.style.position = 'absolute';
                    element.style.top = `${i * itemHeight}px`;
                    element.style.height = `${itemHeight}px`;
                    content.appendChild(element);
                    visibleElements.set(i, element);
                }
            }

            visibleStart = newStart;
            visibleEnd = newEnd;
        };

        this.addEventListener(viewport, 'scroll', updateVisible);
        updateVisible(); // 初始渲染

        return {
            viewport,
            update: (newItems) => {
                items = newItems;
                content.style.height = `${items.length * itemHeight}px`;
                visibleElements.clear();
                content.innerHTML = '';
                updateVisible();
            }
        };
    }

    /**
     * 设置自动清理机制
     */
    setupAutoCleanup() {
        // 页面隐藏时清理
        this.addEventListener(document, 'visibilitychange', () => {
            if (document.hidden) {
                this.performMaintenance();
            }
        });

        // 定期清理（每5分钟）
        this.setInterval(() => {
            this.performMaintenance();
        }, 5 * 60 * 1000);

        // 页面卸载时完全清理
        this.addEventListener(window, 'beforeunload', () => {
            this.cleanup();
        });
    }

    /**
     * 执行维护任务
     */
    performMaintenance() {
        // 强制垃圾回收（如果可用）
        if (window.gc) {
            window.gc();
        }

        // 清理死元素的事件监听器
        for (const [key, listener] of this.eventListeners) {
            if (!document.contains(listener.element)) {
                this.removeEventListener(key);
            }
        }

        console.log(`内存维护完成 - 事件监听器: ${this.eventListeners.size}, 定时器: ${this.timers.size}`);
    }

    /**
     * 完全清理所有资源
     */
    cleanup() {
        // 清理所有事件监听器
        for (const key of this.eventListeners.keys()) {
            this.removeEventListener(key);
        }

        // 清理所有定时器
        for (const id of this.timers) {
            this.clearTimer(id);
        }

        // 清理观察者
        for (const observer of this.observers) {
            observer.disconnect();
        }
        this.observers.clear();

        // 执行清理回调
        for (const callback of this.cleanupCallbacks) {
            try {
                callback();
            } catch (error) {
                console.error('Cleanup callback error:', error);
            }
        }
        this.cleanupCallbacks.clear();

        console.log('内存优化器已完全清理');
    }

    /**
     * 添加清理回调
     */
    onCleanup(callback) {
        this.cleanupCallbacks.add(callback);
    }
}

// 创建全局实例
window.memoryOptimizer = new MemoryOptimizer();

// 导出优化工具函数
window.optimizedAddEventListener = (element, event, handler, options) => 
    window.memoryOptimizer.addEventListener(element, event, handler, options);

window.optimizedSetTimeout = (callback, delay) => 
    window.memoryOptimizer.setTimeout(callback, delay);

window.optimizedSetInterval = (callback, delay) => 
    window.memoryOptimizer.setInterval(callback, delay);

window.createOptimizedElement = (tagName, options) => 
    window.memoryOptimizer.createElement(tagName, options);

console.log('内存优化器已初始化');