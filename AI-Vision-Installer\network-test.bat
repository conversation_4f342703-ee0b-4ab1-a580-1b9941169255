@echo off
title Network Connection Test
color 0B

echo.
echo ========================================
echo Network Connection Test for PyTorch
echo ========================================
echo.

echo Testing connections to PyTorch download sources...
echo.

echo [1/4] Testing Official PyTorch...
ping -n 1 download.pytorch.org >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ download.pytorch.org - OK
) else (
    echo   ❌ download.pytorch.org - Failed
)

echo.
echo [2/4] Testing Tsinghua Mirror...
ping -n 1 pypi.tuna.tsinghua.edu.cn >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ pypi.tuna.tsinghua.edu.cn - OK
) else (
    echo   ❌ pypi.tuna.tsinghua.edu.cn - Failed
)

echo.
echo [3/4] Testing Default PyPI...
ping -n 1 pypi.org >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ pypi.org - OK
) else (
    echo   ❌ pypi.org - Failed
)

echo.
echo [4/4] Testing General Internet...
ping -n 1 www.google.com >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ www.google.com - OK
) else (
    echo   ❌ www.google.com - Failed
)

echo.
echo ========================================
echo Network Speed Recommendations
echo ========================================
echo.

echo For PyTorch download (3.5GB):
echo - Minimum: 10 Mbps (30+ minutes)
echo - Recommended: 50+ Mbps (10-15 minutes)
echo - Optimal: 100+ Mbps (5-10 minutes)
echo.

echo If downloads are slow:
echo 1. Try during off-peak hours (late night/early morning)
echo 2. Close other applications using internet
echo 3. Use wired connection instead of WiFi
echo 4. Consider mobile hotspot if home internet is slow
echo 5. The installer will automatically try multiple sources
echo.

echo Current time: %time%
echo Recommended download times: 11PM-6AM for best speeds
echo.

pause
