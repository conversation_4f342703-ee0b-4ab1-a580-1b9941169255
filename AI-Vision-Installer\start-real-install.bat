@echo off
title AI-Vision Real Installation
color 0E

echo.
echo ========================================
echo AI-Vision Real Installation
echo ========================================
echo.

echo WARNING: This will perform a real installation
echo - Downloads actual files (4-5GB)
echo - Requires stable internet connection
echo - Takes 30-60 minutes to complete
echo.

echo System Requirements:
echo - Windows 10/11 (64-bit)
echo - 8GB+ RAM
echo - 50GB+ free disk space
echo - NVIDIA GPU (recommended)
echo - Stable internet connection
echo.

echo Your System:
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js first: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do echo - Node.js: %%i
for /f "tokens=*" %%i in ('npm --version') do echo - npm: %%i

wmic path win32_VideoController get name | findstr /i "NVIDIA" >nul
if %errorlevel% equ 0 (
    echo - GPU: NVIDIA detected
) else (
    echo - GPU: No NVIDIA GPU (will use CPU mode)
)

echo.
echo Installation Process:
echo 1. Download Python 3.12.9 (2-5 min)
echo 2. Download ComfyUI (3-8 min)
echo 3. Install PyTorch 2.7.1 (10-20 min)
echo 4. Install dependencies (5-15 min)
echo 5. Configure environment (2-5 min)
echo.

echo Download Sources:
echo - Python: Official + China mirrors
echo - PyTorch: Official + Tsinghua mirrors
echo - ComfyUI: GitHub + proxy mirrors
echo (Installer will auto-select fastest source)
echo.

set /p confirm="Continue with real installation? (y/N): "
if /i not "%confirm%"=="y" (
    echo Installation cancelled
    pause
    exit /b 0
)

echo.
echo Checking dependencies...
if not exist "node_modules" (
    echo Installing npm dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Dependencies ready
echo.

echo ========================================
echo STARTING REAL INSTALLATION
echo ========================================
echo.

echo IMPORTANT NOTES:
echo 1. Keep internet connection stable
echo 2. Do not close installer window
echo 3. Allow antivirus exceptions if prompted
echo 4. Installation will auto-switch mirrors if slow
echo 5. Desktop shortcut will be created when done
echo.

echo Press any key to begin installation...
pause >nul

echo Starting installer...
npm start

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo INSTALLATION COMPLETED SUCCESSFULLY!
    echo ========================================
    echo.
    echo Next Steps:
    echo 1. Find AI-Vision shortcut on desktop
    echo 2. Launch AI-Vision Launcher
    echo 3. Test ComfyUI startup
    echo 4. Explore plugin management
    echo.
    echo Installation location: Check path you selected in installer
    echo.
) else (
    echo.
    echo ========================================
    echo INSTALLATION FAILED
    echo ========================================
    echo.
    echo Possible causes:
    echo - Network connection interrupted
    echo - Insufficient disk space
    echo - Antivirus interference
    echo - Permission issues
    echo.
    echo Solutions:
    echo 1. Check internet connection
    echo 2. Free up disk space
    echo 3. Disable antivirus temporarily
    echo 4. Run as administrator
    echo 5. Try again
    echo.
)

echo.
pause
