<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Vision Launcher - 音效配置工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0f, #1a1a2e, #16213e);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(26, 26, 46, 0.9);
            border: 2px solid #00f5ff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            margin-bottom: 10px;
        }

        .header p {
            color: #a0a0a0;
            font-size: 1.1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .section {
            background: rgba(0, 245, 255, 0.05);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 10px;
            padding: 25px;
        }

        .section-title {
            font-size: 1.4rem;
            color: #00f5ff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-area {
            border: 2px dashed #00f5ff;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            background: rgba(0, 245, 255, 0.1);
            border-color: #ffffff;
        }

        .upload-area.dragover {
            background: rgba(0, 245, 255, 0.2);
            border-color: #ffffff;
        }

        .upload-icon {
            font-size: 3rem;
            color: #00f5ff;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #a0a0a0;
            font-size: 0.9rem;
        }

        .file-input {
            display: none;
        }

        .audio-mapping {
            margin-top: 20px;
        }

        .mapping-item {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .mapping-label {
            min-width: 120px;
            color: #00f5ff;
            font-weight: 600;
        }

        .mapping-select {
            flex: 1;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #00f5ff;
            border-radius: 5px;
            padding: 8px 12px;
            color: #ffffff;
            font-size: 0.9rem;
        }

        .mapping-select option {
            background: #1a1a2e;
            color: #ffffff;
        }

        .play-btn {
            background: linear-gradient(45deg, #00f5ff, #0080ff);
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            color: #ffffff;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .play-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 245, 255, 0.4);
        }

        .uploaded-files {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            margin-bottom: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .file-icon {
            color: #00f5ff;
            font-size: 1.2rem;
        }

        .file-name {
            flex: 1;
            font-size: 0.9rem;
        }

        .file-actions {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            background: rgba(0, 245, 255, 0.2);
            border: 1px solid #00f5ff;
            border-radius: 3px;
            padding: 5px 10px;
            color: #00f5ff;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(0, 245, 255, 0.4);
        }

        .action-btn.delete {
            border-color: #ff4757;
            color: #ff4757;
            background: rgba(255, 71, 87, 0.2);
        }

        .action-btn.delete:hover {
            background: rgba(255, 71, 87, 0.4);
        }

        .control-panel {
            grid-column: 1 / -1;
            margin-top: 20px;
        }

        .control-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(45deg, #00f5ff, #0080ff);
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            color: #ffffff;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 245, 255, 0.4);
        }

        .control-btn.secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }

        .control-btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .control-btn.danger {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
        }

        .status-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            display: none;
        }

        .status-message.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }

        .status-message.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }

        .status-message.info {
            background: rgba(0, 245, 255, 0.2);
            border: 1px solid #00f5ff;
            color: #00f5ff;
        }

        .scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #00f5ff rgba(0, 0, 0, 0.3);
        }

        .scrollbar::-webkit-scrollbar {
            width: 8px;
        }

        .scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }

        .scrollbar::-webkit-scrollbar-thumb {
            background: #00f5ff;
            border-radius: 4px;
        }

        .scrollbar::-webkit-scrollbar-thumb:hover {
            background: #ffffff;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .control-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .control-btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 音效配置工具</h1>
            <p>上传自定义音效文件并配置AI Vision Launcher的音效系统</p>
        </div>

        <div class="main-content">
            <!-- 文件上传区域 -->
            <div class="section">
                <div class="section-title">
                    <span>📁</span>
                    <span>音效文件管理</span>
                </div>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">🎵</div>
                    <div class="upload-text">点击或拖拽音效文件到此处</div>
                    <div class="upload-hint">支持 WAV, MP3, OGG 格式，最大 10MB</div>
                </div>
                
                <input type="file" id="fileInput" class="file-input" multiple accept=".wav,.mp3,.ogg">
                
                <div class="uploaded-files scrollbar" id="uploadedFiles">
                    <!-- 上传的文件列表将在这里显示 -->
                </div>
            </div>

            <!-- 音效映射配置 -->
            <div class="section">
                <div class="section-title">
                    <span>🔧</span>
                    <span>音效映射配置</span>
                </div>
                
                <div class="audio-mapping" id="audioMapping">
                    <!-- 音效映射配置将在这里显示 -->
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="section control-panel">
            <div class="section-title">
                <span>⚙️</span>
                <span>配置控制</span>
            </div>
            
            <div class="control-buttons">
                <button class="control-btn" id="previewBtn">🎧 预览配置</button>
                <button class="control-btn success" id="saveBtn">💾 保存配置</button>
                <button class="control-btn secondary" id="loadBtn">📂 加载配置</button>
                <button class="control-btn secondary" id="resetBtn">🔄 重置配置</button>
                <button class="control-btn danger" id="clearBtn">🗑️清空文件</button>
                <button class="control-btn secondary" onclick="debugConfig()">🔍 调试配置</button>
            </div>
            
            <div class="status-message" id="statusMessage"></div>
        </div>
    </div>

    <script src="assets/sounds/audio-manager.js"></script>
    <script src="audio-config-sync.js"></script>
    <script src="audio-config-tool.js"></script>
</body>
</html>
