#!/usr/bin/env python3
"""
专门测试镜像加速功能
"""
import requests
import time

def test_mirror_acceleration():
    """测试镜像加速功能"""
    
    print("🚀 测试镜像加速功能...")
    print("=" * 50)
    
    # 测试GitHub连接速度
    test_url = "https://github.com/pythongosssss/ComfyUI-Custom-Scripts"
    
    print(f"🔍 测试GitHub连接: {test_url}")
    
    try:
        start_time = time.time()
        response = requests.head(test_url, timeout=5)
        duration = time.time() - start_time
        
        print(f"⏱️  连接耗时: {duration:.2f}秒")
        print(f"📊 响应状态: {response.status_code}")
        
        if duration > 3:
            print("🔄 连接较慢，会触发镜像加速")
        else:
            print("✅ 连接正常，可能不会触发镜像加速")
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
    
    print("\n🧪 测试镜像源:")
    mirrors = [
        "https://ghproxy.com/",
        "https://mirror.ghproxy.com/",
        "https://github.moeyy.xyz/"
    ]
    
    for mirror in mirrors:
        mirror_url = mirror + test_url
        print(f"\n🔍 测试镜像: {mirror_url}")
        
        try:
            start_time = time.time()
            response = requests.head(mirror_url, timeout=5)
            duration = time.time() - start_time
            
            print(f"⏱️  响应时间: {duration:.2f}秒")
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 镜像可用")
            else:
                print(f"❌ 镜像不可用")
                
        except Exception as e:
            print(f"❌ 镜像测试失败: {e}")
    
    print("\n📋 结论:")
    print("- 如果GitHub连接 > 3秒，会自动尝试镜像加速")
    print("- 系统会自动选择最快的可用镜像")
    print("- 查看launcher_output.log可以看到实时的加速选择过程")

if __name__ == "__main__":
    test_mirror_acceleration()