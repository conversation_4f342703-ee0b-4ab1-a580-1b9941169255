{"name": "ai-vision-launcher", "version": "1.0.0", "description": "AI视界启动器 - ComfyUI的现代化桌面启动器", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "npm run build", "pack": "electron-builder --dir", "make-exe": "electron-builder --win --publish=never"}, "keywords": ["comfyui", "ai-vision", "launcher", "ai", "desktop", "modern"], "author": "AI Vision Team", "license": "MIT", "devDependencies": {"electron": "^26.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"axios": "^1.5.0", "fs-extra": "^11.1.1"}, "build": {"appId": "com.aivision.launcher", "productName": "AI视界启动器", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "installerHeaderIcon": "assets/icon.ico", "shortcutName": "AI视界启动器"}}}