#!/usr/bin/env python3
"""
测试Git加速功能脚本
"""
import requests
import json
import time
import threading
import subprocess
import os

def test_git_acceleration():
    """测试Git加速功能"""
    
    # 测试用的插件仓库
    test_repos = [
        {
            "name": "ComfyUI-Manager",
            "url": "https://github.com/ltdrdata/ComfyUI-Manager",
            "size": "大型仓库"
        },
        {
            "name": "ComfyUI-Custom-Scripts", 
            "url": "https://github.com/pythongosssss/ComfyUI-Custom-Scripts",
            "size": "中型仓库"
        },
        {
            "name": "ComfyUI-WD14-Tagger",
            "url": "https://github.com/pythongosssss/ComfyUI-WD14-Tagger", 
            "size": "小型仓库"
        }
    ]
    
    print("🚀 开始测试Git加速功能...")
    print("=" * 60)
    
    for repo in test_repos:
        print(f"\n📦 测试仓库: {repo['name']} ({repo['size']})")
        print(f"🔗 URL: {repo['url']}")
        
        # 确保目录不存在
        repo_name = repo['url'].split('/')[-1]
        target_dir = f"/mnt/d/AI/ComfyUI-AI-Vision/custom_nodes/{repo_name}"
        
        if os.path.exists(target_dir):
            print(f"🗑️  删除已存在的目录: {target_dir}")
            subprocess.run(['rm', '-rf', target_dir], check=True)
        
        # 准备安装请求
        payload = {
            "node_id": f"test-{repo['name'].lower()}",
            "repo_url": repo['url'],
            "install_type": "git-clone"
        }
        
        print(f"⏱️  开始安装...")
        start_time = time.time()
        
        try:
            # 发送安装请求
            response = requests.post(
                "http://127.0.0.1:8404/nodes/install",
                headers={"Content-Type": "application/json"},
                json=payload,
                timeout=300  # 5分钟超时
            )
            
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    print(f"✅ 安装成功!")
                    print(f"📊 总耗时: {duration:.2f}秒")
                    print(f"📍 安装路径: {result.get('install_path', 'N/A')}")
                    print(f"📝 依赖安装: {result.get('pip_log', 'N/A')}")
                else:
                    print(f"❌ 安装失败: {result.get('message', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("⏰ 安装超时")
        except Exception as e:
            print(f"❌ 安装异常: {e}")
        
        print("-" * 40)
    
    print("\n🎯 测试完成!")
    print("\n📋 如何查看Git加速效果:")
    print("1. 查看启动器日志: tail -f launcher_output.log")
    print("2. 寻找以下关键信息:")
    print("   - 🔍 测试GitHub连接")
    print("   - 🔄 GitHub访问较慢，尝试使用镜像加速")
    print("   - ✅ 使用xxx镜像加速")
    print("   - 🚀 镜像加速生效")
    print("   - Git克隆耗时统计")

if __name__ == "__main__":
    test_git_acceleration()