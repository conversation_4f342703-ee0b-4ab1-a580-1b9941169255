/**
 * 标签页切换音效测试脚本
 * 在启动器控制台中运行此脚本来测试标签页音效修复效果
 */

console.log('🎵 测试标签页切换音效修复...');

// 测试按钮类型识别
const testButtonTypeRecognition = () => {
    console.log('=== 按钮类型识别测试 ===');
    
    // 获取实际的标签页按钮
    const topTabs = document.querySelectorAll('.tab-btn');
    console.log(`找到顶部标签页按钮: ${topTabs.length} 个`);
    
    topTabs.forEach((tab, index) => {
        const buttonType = getButtonType(tab);
        const text = tab.textContent.trim();
        console.log(`${index + 1}. "${text}" -> ${buttonType} ${buttonType === 'tab-switch' ? '✅' : '❌'}`);
    });
    
    // 测试其他类型的标签页按钮
    const pluginTabs = document.querySelectorAll('.plugin-tab-btn');
    const versionTabs = document.querySelectorAll('.version-tab-btn');
    const envTabs = document.querySelectorAll('.environment-tab-btn');
    
    console.log(`插件管理标签: ${pluginTabs.length} 个`);
    console.log(`版本管理标签: ${versionTabs.length} 个`);
    console.log(`环境配置标签: ${envTabs.length} 个`);
    
    [...pluginTabs, ...versionTabs, ...envTabs].forEach((tab, index) => {
        if (tab) {
            const buttonType = getButtonType(tab);
            const text = tab.textContent.trim();
            console.log(`子标签 ${index + 1}. "${text}" -> ${buttonType} ${buttonType === 'tab-switch' ? '✅' : '❌'}`);
        }
    });
};

// 测试音效播放
const testTabSwitchAudio = () => {
    console.log('=== 标签页音效播放测试 ===');
    
    // 测试 tab-switch 音效
    console.log('🔊 播放 tab-switch 音效...');
    if (window.audioManager) {
        window.audioManager.play('tab-switch');
        console.log('✅ tab-switch 音效播放命令已发送');
    } else {
        console.error('❌ 音效管理器未初始化');
    }
    
    // 检查音效映射
    const tabSwitchSound = window.audioManager?.soundMap?.['tab-switch'];
    console.log(`tab-switch 映射到: ${tabSwitchSound}`);
    
    // 检查音效文件是否加载
    const loadedSound = window.audioManager?.sounds?.['tab-switch'];
    console.log(`tab-switch 音效已加载: ${loadedSound ? '✅' : '❌'}`);
    if (loadedSound && loadedSound.audio) {
        console.log(`音效文件路径: ${decodeURIComponent(loadedSound.audio.src)}`);
    }
};

// 模拟标签页点击测试
const simulateTabClick = () => {
    console.log('=== 模拟标签页点击测试 ===');
    
    const topTabs = document.querySelectorAll('.tab-btn');
    if (topTabs.length > 1) {
        const firstTab = topTabs[0];
        const secondTab = topTabs[1];
        
        console.log(`当前活跃标签: ${document.querySelector('.tab-btn.active')?.textContent}`);
        console.log(`准备点击: ${secondTab.textContent}`);
        
        // 模拟点击
        secondTab.click();
        
        setTimeout(() => {
            console.log(`点击后活跃标签: ${document.querySelector('.tab-btn.active')?.textContent}`);
            console.log('如果听到了导航标签音效，说明修复成功！');
        }, 100);
    } else {
        console.log('❌ 找不到足够的标签页进行测试');
    }
};

// 检查音效配置
const checkAudioConfig = () => {
    console.log('=== 音效配置检查 ===');
    
    const expectedTabSwitchSound = 'custom/导航标签点击的声音.WAV';
    const actualTabSwitchSound = window.audioManager?.soundMap?.['tab-switch'];
    
    console.log(`期望的 tab-switch 音效: ${expectedTabSwitchSound}`);
    console.log(`实际的 tab-switch 音效: ${actualTabSwitchSound}`);
    console.log(`配置正确: ${actualTabSwitchSound === expectedTabSwitchSound ? '✅' : '❌'}`);
    
    // 检查与 click 音效的区别
    const clickSound = window.audioManager?.soundMap?.['click'];
    console.log(`click 音效: ${clickSound}`);
    console.log(`tab-switch 与 click 不同: ${actualTabSwitchSound !== clickSound ? '✅' : '❌'}`);
};

// 主测试函数
const runTabSwitchTest = () => {
    console.log('🎯 标签页切换音效修复测试');
    console.log('=' * 50);
    
    // 1. 检查音效配置
    checkAudioConfig();
    console.log('');
    
    // 2. 测试按钮类型识别
    testButtonTypeRecognition();
    console.log('');
    
    // 3. 测试音效播放
    testTabSwitchAudio();
    console.log('');
    
    // 4. 模拟标签页点击
    console.log('⏰ 3秒后将模拟标签页点击...');
    setTimeout(() => {
        simulateTabClick();
    }, 3000);
    
    console.log('');
    console.log('🎉 测试完成！');
    console.log('💡 请手动点击顶部标签页，确认是否播放了导航标签音效');
};

// 导出测试函数
window.testTabSwitchAudio = runTabSwitchTest;

console.log('📖 使用方法：');
console.log('   运行 testTabSwitchAudio() 开始测试');
console.log('   或者直接点击顶部标签页测试音效');
