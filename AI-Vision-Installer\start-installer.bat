@echo off
title AI-Vision 智能安装器
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    AI-Vision 智能安装器                      ║
echo  ║                                                              ║
echo  ║  一键安装 ComfyUI + AI-Vision 启动器完整环境                 ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

:: 检查是否在正确的目录
if not exist "installer.html" (
    echo ❌ 错误: 请在安装器目录中运行此脚本
    echo 当前目录: %CD%
    echo.
    pause
    exit /b 1
)

:: 检查Node.js
echo 🔍 检查运行环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js
    echo.
    echo 请先安装Node.js:
    echo 1. 访问 https://nodejs.org/
    echo 2. 下载并安装最新版本的Node.js
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

:: 检查依赖
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖包安装失败
        echo.
        echo 请检查网络连接并重试
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
    echo.
)

:: 启动安装器
echo 🚀 启动AI-Vision智能安装器...
echo.
echo 💡 提示:
echo   - 安装器将在新窗口中打开
echo   - 请按照界面提示完成安装
echo   - 安装过程中请保持网络连接稳定
echo.

:: 启动Electron应用
npm start

:: 检查启动结果
if %errorlevel% neq 0 (
    echo.
    echo ❌ 安装器启动失败
    echo.
    echo 可能的解决方案:
    echo 1. 重新运行此脚本
    echo 2. 检查是否有杀毒软件阻止运行
    echo 3. 以管理员身份运行此脚本
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 安装器已关闭
echo 感谢使用AI-Vision智能安装器！
echo.
pause
