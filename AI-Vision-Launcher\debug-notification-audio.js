/**
 * 通知音效调试脚本
 * 用于调试版本切换和插件操作成功后没有音效的问题
 */

console.log('🔍 开始调试通知音效问题...');

// 检查音效设置
const checkAudioSettings = () => {
    console.log('=== 音效设置检查 ===');
    
    if (!window.audioManager) {
        console.error('❌ 音效管理器不可用');
        return false;
    }
    
    const settings = window.audioManager.settings;
    console.log('音效设置:', settings);
    
    console.log(`总开关 (enabled): ${settings.enabled ? '✅' : '❌'}`);
    console.log(`反馈音效 (feedbackSounds): ${settings.feedbackSounds ? '✅' : '❌'}`);
    console.log(`系统音效 (systemSounds): ${settings.systemSounds ? '✅' : '❌'}`);
    console.log(`点击音效 (clickSounds): ${settings.clickSounds ? '✅' : '❌'}`);
    console.log(`悬停音效 (hoverSounds): ${settings.hoverSounds ? '✅' : '❌'}`);
    
    return settings.enabled && settings.feedbackSounds;
};

// 检查成功音效配置和加载状态
const checkSuccessAudioStatus = () => {
    console.log('=== 成功音效状态检查 ===');
    
    const successSounds = ['success', 'plugin-success', 'version-success', 'install-success'];
    
    successSounds.forEach(soundName => {
        const mapping = window.audioManager?.soundMap?.[soundName];
        const loaded = window.audioManager?.sounds?.[soundName];
        
        console.log(`${soundName}:`);
        console.log(`  映射: ${mapping || '❌ 未配置'}`);
        console.log(`  加载: ${loaded ? '✅' : '❌'}`);
        
        if (loaded && loaded.audio) {
            console.log(`  路径: ${decodeURIComponent(loaded.audio.src)}`);
        }
    });
};

// 拦截 showNotification 方法来调试
const interceptShowNotification = () => {
    console.log('=== 拦截 showNotification 方法 ===');
    
    if (!window.launcherInstance || !window.launcherInstance.showNotification) {
        console.error('❌ launcherInstance 或 showNotification 不可用');
        return;
    }
    
    // 保存原始方法
    const originalShowNotification = window.launcherInstance.showNotification;
    
    // 创建拦截方法
    window.launcherInstance.showNotification = function(message, type = 'info', context = null) {
        console.log('🔔 showNotification 被调用:');
        console.log(`  消息: "${message}"`);
        console.log(`  类型: "${type}"`);
        console.log(`  上下文: "${context}"`);
        
        // 检查音效播放逻辑
        if (type === 'success' && window.audioManager) {
            console.log('🎵 应该播放成功音效...');
            
            let expectedSound = 'success';
            if (context === 'plugin' || message.includes('插件') || message.includes('plugin')) {
                expectedSound = 'plugin-success';
            } else if (context === 'version' || message.includes('版本') || message.includes('version')) {
                expectedSound = 'version-success';
            } else if (context === 'install' || message.includes('安装') || message.includes('install')) {
                expectedSound = 'install-success';
            } else if (context === 'update' || message.includes('更新') || message.includes('update')) {
                expectedSound = 'update-success';
            }
            
            console.log(`  期望播放音效: ${expectedSound}`);
            
            // 检查音效是否可用
            const soundMapping = window.audioManager.soundMap[expectedSound];
            const soundLoaded = window.audioManager.sounds[expectedSound];
            
            console.log(`  音效映射: ${soundMapping}`);
            console.log(`  音效已加载: ${soundLoaded ? '✅' : '❌'}`);
            
            // 检查音效设置
            const settings = window.audioManager.settings;
            console.log(`  音效总开关: ${settings.enabled ? '✅' : '❌'}`);
            console.log(`  反馈音效开关: ${settings.feedbackSounds ? '✅' : '❌'}`);
            
            if (!settings.enabled) {
                console.warn('⚠️ 音效总开关已关闭');
            } else if (!settings.feedbackSounds) {
                console.warn('⚠️ 反馈音效开关已关闭');
            } else if (!soundLoaded) {
                console.warn('⚠️ 音效文件未加载');
            } else {
                console.log('✅ 音效应该能正常播放');
            }
        }
        
        // 调用原始方法
        return originalShowNotification.call(this, message, type, context);
    };
    
    console.log('✅ showNotification 方法已被拦截，现在可以调试音效播放');
};

// 拦截 audioManager.play 方法来调试
const interceptAudioManagerPlay = () => {
    console.log('=== 拦截 audioManager.play 方法 ===');
    
    if (!window.audioManager || !window.audioManager.play) {
        console.error('❌ audioManager 或 play 方法不可用');
        return;
    }
    
    // 保存原始方法
    const originalPlay = window.audioManager.play;
    
    // 创建拦截方法
    window.audioManager.play = function(soundName, volume = null) {
        console.log(`🔊 audioManager.play 被调用: "${soundName}"`);
        
        // 检查音效设置
        if (!this.settings.enabled) {
            console.log('❌ 音效被跳过: 总开关已关闭');
            return;
        }
        
        // 检查特定音效类型是否启用
        if (soundName.includes('hover') && !this.settings.hoverSounds) {
            console.log('❌ 音效被跳过: 悬停音效已关闭');
            return;
        }
        if (['success', 'error', 'warning', 'notification', 'plugin-success', 'version-success', 'install-success', 'update-success'].includes(soundName) && !this.settings.feedbackSounds) {
            console.log('❌ 音效被跳过: 反馈音效已关闭');
            return;
        }
        if (['startup', 'shutdown', 'complete'].includes(soundName) && !this.settings.systemSounds) {
            console.log('❌ 音效被跳过: 系统音效已关闭');
            return;
        }
        if (['click', 'switch', 'input', 'tab-switch'].includes(soundName) && !this.settings.clickSounds) {
            console.log('❌ 音效被跳过: 点击音效已关闭');
            return;
        }
        
        const sound = this.sounds[soundName];
        if (!sound) {
            console.log(`⚠️ 音效未加载: ${soundName}，尝试加载...`);
            this.loadSound(soundName);
            return;
        }
        
        console.log(`✅ 播放音效: ${soundName}`);
        
        // 调用原始方法
        return originalPlay.call(this, soundName, volume);
    };
    
    console.log('✅ audioManager.play 方法已被拦截');
};

// 测试通知音效
const testNotificationAudio = () => {
    console.log('=== 测试通知音效 ===');
    
    if (!window.launcherInstance || !window.launcherInstance.showNotification) {
        console.error('❌ launcherInstance 不可用');
        return;
    }
    
    const testCases = [
        { message: '版本切换成功测试', type: 'success', context: 'version' },
        { message: '插件操作成功测试', type: 'success', context: 'plugin' },
        { message: '安装成功测试', type: 'success', context: 'install' }
    ];
    
    testCases.forEach((test, index) => {
        setTimeout(() => {
            console.log(`📢 测试通知 ${index + 1}:`);
            window.launcherInstance.showNotification(test.message, test.type, test.context);
        }, index * 2000);
    });
};

// 主调试函数
const runNotificationAudioDebug = () => {
    console.log('🎯 通知音效调试');
    console.log('=' * 50);
    
    // 1. 检查音效设置
    const settingsOK = checkAudioSettings();
    console.log('');
    
    // 2. 检查成功音效状态
    checkSuccessAudioStatus();
    console.log('');
    
    // 3. 拦截方法进行调试
    interceptShowNotification();
    interceptAudioManagerPlay();
    console.log('');
    
    // 4. 测试通知音效
    if (settingsOK) {
        console.log('⏰ 3秒后开始测试通知音效...');
        setTimeout(() => {
            testNotificationAudio();
        }, 3000);
    } else {
        console.log('❌ 音效设置有问题，跳过测试');
    }
    
    console.log('');
    console.log('🎉 调试设置完成！');
    console.log('💡 现在可以在版本管理或插件管理页面进行实际操作，查看调试信息');
};

// 恢复原始方法
const restoreOriginalMethods = () => {
    console.log('🔄 恢复原始方法...');
    // 这里可以添加恢复逻辑，但通常重新加载页面更简单
    console.log('💡 建议刷新页面来恢复原始方法');
};

// 导出函数
window.runNotificationAudioDebug = runNotificationAudioDebug;
window.restoreOriginalMethods = restoreOriginalMethods;

console.log('📖 使用方法：');
console.log('   运行 runNotificationAudioDebug() 开始调试');
console.log('   然后在版本管理或插件管理页面进行操作，观察调试信息');
