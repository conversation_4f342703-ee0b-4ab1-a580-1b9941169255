<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Vision 智能安装器 - 预览</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .preview-header {
            background: linear-gradient(45deg, #0088ff, #00e5ff);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .preview-content {
            padding: 20px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f9f9f9;
        }
        
        .feature-card h3 {
            color: #0088ff;
            margin-top: 0;
        }
        
        .btn-group {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #0088ff, #00e5ff);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,136,255,0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .system-requirements {
            background: #e7f3ff;
            border-left: 4px solid #0088ff;
            padding: 15px;
            margin: 20px 0;
        }
        
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Consolas', monospace;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1>🚀 AI-Vision 智能安装器</h1>
            <p>一键安装 ComfyUI + AI-Vision 启动器完整环境</p>
        </div>
        
        <div class="preview-content">
            <h2>📋 功能特性</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🐍 Python 环境</h3>
                    <p>自动配置 Python 3.12.9 便携环境，无需手动安装Python，完全独立运行。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔥 PyTorch 支持</h3>
                    <p>安装最新的 PyTorch 2.7.1 + CUDA 12.8，充分利用GPU加速能力。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🌐 智能下载</h3>
                    <p>智能镜像切换技术，自动选择最快的下载源，国内用户友好。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🎨 用户界面</h3>
                    <p>现代化霓虹风格界面，实时进度显示，用户体验友好。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🛠️ 完整功能</h3>
                    <p>包含完整的卸载程序，支持桌面快捷方式和开始菜单。</p>
                </div>
                
                <div class="feature-card">
                    <h3>⚡ 一键启动</h3>
                    <p>安装完成后可直接启动AI-Vision启动器，无需额外配置。</p>
                </div>
            </div>
            
            <div class="system-requirements">
                <h3>💻 系统要求</h3>
                <ul>
                    <li><strong>操作系统</strong>: Windows 10/11 (64位)</li>
                    <li><strong>内存</strong>: 8GB RAM (推荐16GB+)</li>
                    <li><strong>存储空间</strong>: 50GB 可用空间</li>
                    <li><strong>GPU</strong>: NVIDIA GPU (支持CUDA 12.8) 可选</li>
                    <li><strong>网络</strong>: 稳定的互联网连接</li>
                </ul>
            </div>
            
            <div class="warning">
                <h3>⚠️ 重要提示</h3>
                <p>这是安装器的预览页面。要使用完整功能，请运行桌面版安装器。</p>
                <p>首次安装可能需要较长时间，请保持网络连接稳定。</p>
            </div>
            
            <h2>🚀 快速开始</h2>
            <p>有两种方式运行安装器：</p>
            
            <h3>方法一：直接运行 (推荐)</h3>
            <div class="code-block">
双击运行: start-installer.bat
            </div>
            
            <h3>方法二：命令行运行</h3>
            <div class="code-block">
# 安装依赖
npm install

# 启动安装器
npm start
            </div>
            
            <h2>📦 安装流程</h2>
            <ol>
                <li><strong>环境检测</strong> - 检测系统环境和硬件配置</li>
                <li><strong>下载Python</strong> - 下载Python 3.12.9便携版</li>
                <li><strong>配置Python</strong> - 配置Python环境和pip</li>
                <li><strong>下载ComfyUI</strong> - 从GitHub下载ComfyUI</li>
                <li><strong>配置ComfyUI</strong> - 解压和配置ComfyUI</li>
                <li><strong>安装PyTorch</strong> - 安装PyTorch和相关包</li>
                <li><strong>安装依赖</strong> - 安装所有必需的依赖包</li>
                <li><strong>配置环境</strong> - 配置启动器和后端服务</li>
                <li><strong>完成安装</strong> - 创建快捷方式和清理临时文件</li>
            </ol>
            
            <div class="btn-group">
                <button class="btn btn-primary" onclick="openInstaller()">
                    🚀 启动安装器
                </button>
                <button class="btn btn-secondary" onclick="showTestMode()">
                    🧪 测试模式
                </button>
                <a href="README.md" class="btn btn-secondary">
                    📖 查看文档
                </a>
            </div>
            
            <div id="test-info" style="display: none; margin-top: 20px; padding: 15px; background: #d4edda; border-radius: 4px;">
                <h4>🧪 测试模式说明</h4>
                <p>测试模式将模拟安装过程，不会下载实际文件。适合用于：</p>
                <ul>
                    <li>预览安装器界面</li>
                    <li>测试安装流程</li>
                    <li>检查系统兼容性</li>
                </ul>
                <p><strong>运行测试：</strong> 双击 <code>test-installer.bat</code></p>
            </div>
        </div>
    </div>
    
    <script>
        function openInstaller() {
            if (confirm('这将启动AI-Vision安装器。\n\n请确保：\n1. 已安装Node.js\n2. 网络连接稳定\n3. 有足够的磁盘空间\n\n是否继续？')) {
                alert('请运行 start-installer.bat 文件来启动安装器');
            }
        }
        
        function showTestMode() {
            const testInfo = document.getElementById('test-info');
            if (testInfo.style.display === 'none') {
                testInfo.style.display = 'block';
            } else {
                testInfo.style.display = 'none';
            }
        }
        
        // 显示系统信息
        function showSystemInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine
            };
            
            console.log('系统信息:', info);
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', () => {
            showSystemInfo();
            
            // 检查是否在Electron环境中
            if (window.electronAPI) {
                console.log('检测到Electron环境');
            } else {
                console.log('浏览器预览模式');
            }
        });
    </script>
</body>
</html>
