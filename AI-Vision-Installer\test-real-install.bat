@echo off
title AI-Vision REAL Installation Test
color 0C

echo.
echo ========================================
echo AI-Vision REAL Installation Test
echo ========================================
echo.

echo IMPORTANT: This will perform REAL installation!
echo.

echo What this will do:
echo - Download Python 3.12.9 (160MB)
echo - Download ComfyUI (500MB)  
echo - Install PyTorch 2.7.1 + CUDA 12.8 (3.5GB)
echo - Install all dependencies (500MB)
echo - Configure complete environment
echo.

echo Total download: ~4-5GB
echo Time required: 30-60 minutes
echo.

echo Your system:
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit
)

for /f "tokens=*" %%i in ('node --version') do echo - Node.js: %%i
for /f "tokens=*" %%i in ('npm --version') do echo - npm: %%i

wmic path win32_VideoController get name | findstr /i "NVIDIA" >nul
if %errorlevel% equ 0 (
    echo - GPU: NVIDIA detected (CUDA support)
) else (
    echo - GPU: No NVIDIA GPU (CPU mode)
)

echo.
echo REAL installation changes:
echo - Removed all simulation delays
echo - Added actual pip install commands
echo - Real file downloads and extractions
echo - Actual PyTorch and dependency installation
echo.

set /p confirm="Start REAL installation? (type YES): "
if not "%confirm%"=="YES" (
    echo Installation cancelled
    echo Type exactly "YES" to confirm real installation
    pause
    exit
)

echo.
echo Preparing for real installation...

if not exist "node_modules" (
    echo Installing npm dependencies...
    npm install
)

echo.
echo ========================================
echo STARTING REAL INSTALLATION NOW
echo ========================================
echo.

echo This will:
echo 1. Actually download files from internet
echo 2. Install real Python packages with pip
echo 3. Create working AI-Vision environment
echo 4. Take significant time and bandwidth
echo.

echo Press Ctrl+C to abort, or any key to continue...
pause >nul

npm start

echo.
if %errorlevel% equ 0 (
    echo ========================================
    echo REAL INSTALLATION COMPLETED!
    echo ========================================
    echo.
    echo You should now have:
    echo - Working Python 3.12.9 environment
    echo - Installed PyTorch 2.7.1 with CUDA
    echo - Complete ComfyUI installation
    echo - All required dependencies
    echo - AI-Vision launcher ready to use
    echo.
) else (
    echo ========================================
    echo INSTALLATION FAILED
    echo ========================================
    echo.
    echo Check the error messages above
    echo Common issues:
    echo - Network connection problems
    echo - Insufficient disk space
    echo - Antivirus interference
    echo.
)

pause
