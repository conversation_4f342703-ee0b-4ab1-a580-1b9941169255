{"name": "ai-vision-installer", "version": "1.0.0", "description": "AI-Vision 智能安装器 - 一键安装 ComfyUI + AI-Vision 启动器", "main": "installer-electron-main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:portable": "electron-builder --win portable"}, "author": "Your Name", "license": "MIT", "dependencies": {"adm-zip": "^0.5.10", "electron-log": "^5.0.1", "axios": "^1.6.0", "node-machine-id": "^1.1.12"}, "devDependencies": {"electron": "^28.1.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.aivision.installer", "productName": "AI-Vision Installer", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "../AI-Vision-Launcher/assets/shortcut-icon-max.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "AI-Vision Installer", "artifactName": "AI-Vision-Installer-Setup-${version}.${ext}", "uninstallDisplayName": "AI-Vision Installer", "deleteAppDataOnUninstall": true}, "portable": {"artifactName": "AI-Vision-Installer-Portable-${version}.${ext}"}}}