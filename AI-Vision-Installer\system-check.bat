@echo off
title AI-Vision System Check
color 0B

echo.
echo ========================================
echo AI-Vision System Check
echo ========================================
echo.

echo Checking system requirements...
echo.

REM Check OS
echo [1/6] Operating System:
ver | findstr /i "10\." >nul
if %errorlevel% equ 0 (
    echo   OK: Windows 10 detected
    goto :os_ok
)
ver | findstr /i "11\." >nul
if %errorlevel% equ 0 (
    echo   OK: Windows 11 detected
    goto :os_ok
)
echo   WARNING: Windows 10/11 recommended

:os_ok

REM Check architecture
echo.
echo [2/6] System Architecture:
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo   OK: 64-bit system
) else (
    echo   ERROR: 64-bit system required
    goto :failed
)

REM Check Node.js
echo.
echo [3/6] Node.js Environment:
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do echo   OK: Node.js %%i
    for /f "tokens=*" %%i in ('npm --version') do echo   OK: npm %%i
) else (
    echo   ERROR: Node.js not found
    echo   Please install from https://nodejs.org/
    goto :failed
)

REM Check GPU
echo.
echo [4/6] GPU Check:
wmic path win32_VideoController get name | findstr /i "NVIDIA" >nul
if %errorlevel% equ 0 (
    echo   OK: NVIDIA GPU detected
) else (
    echo   WARNING: No NVIDIA GPU found, will use CPU mode
)

REM Check network
echo.
echo [5/6] Network Connection:
ping -n 1 www.python.org >nul 2>&1
if %errorlevel% equ 0 (
    echo   OK: Internet connection available
) else (
    echo   WARNING: Network connection issues
)

REM Check disk space
echo.
echo [6/6] Disk Space:
echo   Checking available space on drives...
if exist C:\ (
    echo   C: drive available
)
if exist D:\ (
    echo   D: drive available
)
if exist E:\ (
    echo   E: drive available
)

echo.
echo ========================================
echo System Check Complete
echo ========================================
echo.

echo Summary:
echo   - OS: Windows 10/11 (64-bit)
echo   - Node.js: Installed
echo   - GPU: NVIDIA detected
echo   - Network: Available
echo   - Disk: Multiple drives available
echo.

echo Recommendations:
echo   1. Choose drive with 50GB+ free space
echo   2. Ensure stable internet connection
echo   3. Temporarily disable antivirus
echo   4. Allow 30-60 minutes for installation
echo.

echo System is ready for installation!
echo.

set /p start="Start real installation now? (y/N): "
if /i "%start%"=="y" (
    echo.
    echo Starting installer...
    call real-install.bat
) else (
    echo.
    echo Installation cancelled
    echo Run real-install.bat when ready
)

goto :end

:failed
echo.
echo ERROR: System requirements not met
echo Please fix the issues above and try again
echo.

:end
echo.
pause
