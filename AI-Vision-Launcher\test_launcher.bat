@echo off
title AI Vision Launcher Test
echo ====================================
echo       AI Vision Launcher Test
echo ====================================
echo.

cd /d "%~dp0"
echo Current directory: %CD%
echo.

echo Checking Node.js...
node --version
if errorlevel 1 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)

echo Checking Python...
python --version
if errorlevel 1 (
    echo Trying python3...
    python3 --version
    if errorlevel 1 (
        echo ERROR: Python not found
        pause
        exit /b 1
    )
)

echo.
echo Checking if port 8399 is available...
netstat -an | findstr :8399
if not errorlevel 1 (
    echo WARNING: Port 8399 is already in use
    echo This might cause conflicts
)

echo.
echo Starting AI Vision Launcher...
echo Press Ctrl+C to stop
echo.

npm start

pause
