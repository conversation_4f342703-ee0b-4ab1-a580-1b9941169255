# 插件切换按钮加载状态优化

## 🎯 **问题描述**

已安装插件标签页面中的"切换"按钮在点击后没有显示加载状态，用户不知道系统是否正在处理请求，体验不够友好。

## 🔧 **优化方案**

### 1. **按钮点击时显示"切换中"状态**

#### ✅ **DOM按钮创建**：
```javascript
{ text: '切换', icon: 'fas fa-code-branch', color: '#6c757d', textColor: 'white', action: () => {
    // 设置按钮为加载状态
    launcherInstance.setPluginButtonLoading(safeName, 'showPluginVersions', true, {
        loadingText: '切换中',
        loadingIcon: 'fas fa-spinner fa-spin'
    });
    
    // 调用版本切换函数
    if (window.showPluginVersions) {
        window.showPluginVersions(safeName);
    }
}}
```

#### ✅ **HTML模板按钮**：
```javascript
onclick="
    // 设置按钮为加载状态
    launcherInstance.setPluginButtonLoading('${safeName}', 'showPluginVersions', true, {
        loadingText: '切换中',
        loadingIcon: 'fas fa-spinner fa-spin'
    });
    // 调用版本切换函数
    showPluginVersions('${safeName}');
"
data-plugin-name="${safeName}" 
data-action-type="showPluginVersions"
```

### 2. **版本对话框显示后恢复按钮状态**

#### ✅ **成功显示对话框**：
```javascript
// 获取模态框并显示
const modal = document.getElementById('version-switch-modal');
if (modal) {
    modal.classList.add('show');
    modal.style.display = 'flex';
    
    // 恢复切换按钮状态
    this.setPluginButtonLoading(pluginName, 'showPluginVersions', false);
}
```

#### ✅ **显示失败时**：
```javascript
if (!modal) {
    logger.error('Version switch modal not found!');
    // 恢复按钮状态
    this.setPluginButtonLoading(pluginName, 'showPluginVersions', false);
    return;
}
```

### 3. **关闭对话框时恢复按钮状态**

#### ✅ **关闭模态框函数**：
```javascript
function closeVersionSwitchModal() {
    // ... 关闭逻辑 ...
    
    if (launcherInstance) {
        // 恢复切换按钮状态
        if (launcherInstance.currentVersionSwitchPlugin) {
            launcherInstance.setPluginButtonLoading(
                launcherInstance.currentVersionSwitchPlugin, 
                'showPluginVersions', 
                false
            );
        }
        launcherInstance.currentVersionSwitchPlugin = null;
    }
}
```

## 🎨 **用户体验改进**

### ✅ **优化前**：
- 点击"切换"按钮后没有任何反馈
- 用户不知道系统是否在处理
- 可能会重复点击按钮

### ✅ **优化后**：
- 点击后立即显示"切换中"状态
- 按钮显示旋转的加载图标
- 版本对话框显示后恢复正常状态
- 关闭对话框时确保状态恢复

## 🔧 **技术实现**

### 📱 **按钮状态管理**：
- 使用现有的 `setPluginButtonLoading()` 方法
- 通过 `data-plugin-name` 和 `data-action-type` 属性定位按钮
- 支持自定义加载文本和图标

### 🎯 **状态恢复机制**：
- **成功场景**：版本对话框显示后恢复
- **失败场景**：对话框加载失败时恢复
- **取消场景**：用户关闭对话框时恢复
- **异常场景**：任何异常情况都会恢复状态

### 📊 **日志集成**：
- 使用新的日志管理系统记录操作
- 便于调试和问题排查
- 支持不同日志级别

## 🚀 **使用效果**

### 对于用户：
1. 点击"切换"按钮
2. 按钮立即显示"切换中"状态和旋转图标
3. 版本选择对话框打开后，按钮恢复正常
4. 关闭对话框时确保按钮状态正确

### 对于开发者：
- 统一的按钮状态管理机制
- 完善的错误处理和状态恢复
- 易于扩展到其他按钮类型

## 🔍 **测试场景**

1. **正常流程**：点击切换 → 显示加载 → 对话框打开 → 状态恢复
2. **对话框失败**：点击切换 → 显示加载 → 加载失败 → 状态恢复
3. **用户取消**：点击切换 → 显示加载 → 用户关闭对话框 → 状态恢复
4. **快速点击**：防止用户在加载期间重复点击

## 📋 **相关功能**

这次优化与以下功能保持一致：
- ✅ **安装按钮**：显示"安装中"状态
- ✅ **卸载按钮**：显示"卸载中"状态  
- ✅ **更新按钮**：显示"更新中"状态
- ✅ **启用/禁用按钮**：显示"启用中/禁用中"状态
- ✅ **切换按钮**：现在显示"切换中"状态

---

**总结**：插件切换按钮现在会在点击时显示"切换中"状态，提供了更好的用户反馈和体验。所有状态变化都有完善的恢复机制，确保界面状态的一致性。
