# AI视界启动器 (AI Vision Launcher)

现代化的ComfyUI桌面启动器，采用Electron架构，提供完整的插件管理功能。

## 🌟 特性

- **🖥️ 原生桌面应用** - 基于Electron，跨平台支持
- **🎨 现代化UI** - 科技感设计，竖版布局优化
- **🔌 真实插件管理** - 实时扫描、安装、卸载、启用/禁用插件
- **📦 ComfyUI-Manager集成** - 自动获取可安装插件列表
- **🔄 实时同步** - 与ComfyUI的custom_nodes目录完全同步
- **🚀 独立运行** - 不影响原始ComfyUI启动器

## 🏗️ 项目结构

```
AI-Vision-Launcher/
├── main.js                    # Electron主进程
├── preload.js                 # 安全预加载脚本
├── ai_vision_launcher.html    # AI视界界面
├── package.json              # Node.js项目配置
├── backend/                  # Python后端API
│   ├── ai_vision_api.py      # FastAPI服务
│   └── requirements.txt      # Python依赖
├── test_backend.sh           # 后端测试脚本
└── assets/                   # 应用资源
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Node.js依赖
npm install

# 安装Python依赖（可选，测试用）
pip install fastapi uvicorn python-multipart
```

### 2. 启动应用

```bash
# 方式1：使用npm脚本
npm start

# 方式2：直接运行
electron main.js

# 方式3：开发模式（带调试工具）
electron main.js --dev
```

### 3. 测试后端API

```bash
# 运行后端测试
./test_backend.sh
```

## 🔧 技术架构

### 前端 (Electron)
- **主进程**: main.js - 窗口管理、API通信
- **渲染进程**: ai_vision_launcher.html - 用户界面
- **预加载**: preload.js - 安全的API桥接

### 后端 (FastAPI)
- **插件扫描**: 实时扫描custom_nodes目录
- **插件管理**: 安装/卸载/启用/禁用操作
- **数据集成**: ComfyUI-Manager插件库集成
- **API端口**: 8400 (避免与原始启动器冲突)

## 📡 API端点

- `GET /health` - 健康检查
- `GET /api/plugins/installed` - 获取已安装插件
- `GET /api/plugins/available` - 获取可安装插件
- `POST /api/plugins/toggle` - 启用/禁用插件
- `POST /api/plugins/install` - 安装插件
- `POST /api/plugins/uninstall` - 卸载插件

## 🔌 插件管理功能

### 已安装插件
- ✅ 实时扫描custom_nodes目录
- ✅ 显示插件详细信息（大小、作者、描述等）
- ✅ 启用/禁用切换（通过目录重命名）
- ✅ 安全卸载（自动备份）

### 可安装插件
- ✅ 集成ComfyUI-Manager插件库
- ✅ 搜索和分类筛选
- ✅ Git克隆安装
- ✅ 自动依赖安装

## 🎨 界面设计

- **竖版布局**: 600x900窗口，适配现代显示器
- **科技风格**: 深色主题，霓虹蓝色调
- **响应式**: 自适应窗口大小调整
- **系统托盘**: 最小化到系统托盘

## 🔒 安全特性

- **进程隔离**: 主进程与渲染进程分离
- **上下文隔离**: 禁用Node.js集成
- **API桥接**: 通过preload脚本安全通信
- **备份机制**: 卸载前自动创建备份

## 🛠️ 开发说明

### 调试模式
```bash
electron main.js --dev
```
- 自动打开开发者工具
- 热重载支持
- 详细日志输出

### 快捷键
- `Ctrl+Shift+I` - 开发者工具

### 目录说明
- ComfyUI根目录是AI-Vision-Launcher的上级目录
- 插件安装到 `../custom_nodes/` 目录
- 备份保存在 `../custom_nodes/.backups/` 目录

## 📋 系统要求

- **Node.js**: >= 14.0.0
- **Python**: >= 3.7 (用于后端API)
- **操作系统**: Windows/macOS/Linux
- **ComfyUI**: 需要已安装的ComfyUI环境

## 🐛 故障排除

### 后端服务无法启动
```bash
# 检查Python依赖
python3 -c "import fastapi, uvicorn"

# 检查端口占用
netstat -an | grep 8400
```

### 插件扫描失败
- 确保ComfyUI的custom_nodes目录存在
- 检查目录权限

### Electron应用无法启动
```bash
# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

## 📝 更新日志

### v1.0.0
- ✅ 完整的Electron桌面应用
- ✅ 真实的插件管理API
- ✅ ComfyUI-Manager集成
- ✅ 现代化UI设计
- ✅ 系统托盘支持

## 📞 支持

如有问题或建议，请提交Issue或联系开发团队。

---

**科技赋能，视界无限** - AI视界启动器团队