@echo off
echo AI-Vision Fast Install Test (China Mirrors)
echo ============================================
echo.

echo Using China mirrors for faster downloads:
echo - Tsinghua University PyPI mirror
echo - Alibaba Cloud mirror
echo - Auto-fallback to multiple sources
echo.

echo Expected: 5-10x faster PyTorch download
echo.

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit
)

echo Node.js: OK
echo.

if not exist "node_modules" npm install

echo Starting fast installation...
echo.

npm start

echo.
echo Installation completed
pause
