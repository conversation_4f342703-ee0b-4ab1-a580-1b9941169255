/**
 * AI-Vision 安装器 Electron 主进程
 */

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { spawn, exec } = require('child_process');
const https = require('https');
const http = require('http');
const AdmZip = require('adm-zip');

// 导入配置
const INSTALLER_CONFIG = require('./installer-config.js');

let mainWindow;
let installationProcess = null;

// 创建主窗口
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1000,
        height: 700,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'installer-preload.js')
        },
        icon: path.join(__dirname, '../AI-Vision-Launcher/assets/shortcut-icon-max.ico'),
        title: 'AI-Vision 智能安装器',
        show: false,
        frame: true,
        resizable: true,
        maximizable: true,
        minimizable: true,
        closable: true,
        autoHideMenuBar: true
    });

    // 加载安装器页面
    mainWindow.loadFile('installer.html');

    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // 开发模式下打开开发者工具
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    });

    // 处理窗口关闭
    mainWindow.on('closed', () => {
        mainWindow = null;
        
        // 如果有正在进行的安装，询问是否取消
        if (installationProcess) {
            const choice = dialog.showMessageBoxSync(null, {
                type: 'question',
                buttons: ['取消安装', '继续安装'],
                defaultId: 1,
                title: '确认',
                message: '安装正在进行中，确定要退出吗？',
                detail: '退出将取消当前安装进程。'
            });
            
            if (choice === 0) {
                // 取消安装
                if (installationProcess && !installationProcess.killed) {
                    installationProcess.kill();
                }
                app.quit();
            } else {
                // 继续安装，重新创建窗口
                createWindow();
            }
        }
    });
}

// 应用程序事件
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC 处理器

// 选择目录
ipcMain.handle('select-directory', async () => {
    const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openDirectory', 'createDirectory'],
        title: '选择安装目录',
        defaultPath: INSTALLER_CONFIG.installation.defaultPath
    });
    
    return result;
});

// 获取磁盘空间
ipcMain.handle('get-disk-space', async (event, dirPath) => {
    try {
        // 如果路径不存在，获取父目录或驱动器根目录的空间
        let targetPath = dirPath;

        // 检查路径是否存在
        if (!fs.existsSync(dirPath)) {
            // 获取驱动器根目录
            const parsed = path.parse(dirPath);
            targetPath = parsed.root || 'C:\\';
        } else {
            const stats = fs.statSync(dirPath);
            if (!stats.isDirectory()) {
                targetPath = path.dirname(dirPath);
            }
        }
        
        // 在Windows上使用wmic命令获取磁盘空间
        if (process.platform === 'win32') {
            return new Promise((resolve, reject) => {
                const drive = path.parse(targetPath).root;
                const command = `chcp 65001 >nul && wmic logicaldisk where caption="${drive}" get size,freespace /value`;
                exec(command, { encoding: 'utf8' }, (error, stdout) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    
                    const lines = stdout.split('\n');
                    let freeSpace = 0;
                    let totalSpace = 0;
                    
                    lines.forEach(line => {
                        if (line.startsWith('FreeSpace=')) {
                            freeSpace = parseInt(line.split('=')[1]);
                        }
                        if (line.startsWith('Size=')) {
                            totalSpace = parseInt(line.split('=')[1]);
                        }
                    });
                    
                    resolve({
                        available: freeSpace,
                        total: totalSpace,
                        used: totalSpace - freeSpace
                    });
                });
            });
        } else {
            // Linux/Mac 使用 statvfs
            const { execSync } = require('child_process');
            const output = execSync(`df "${dirPath}"`, { encoding: 'utf8' });
            const lines = output.split('\n');
            const data = lines[1].split(/\s+/);
            
            return {
                available: parseInt(data[3]) * 1024, // df 返回的是KB
                total: parseInt(data[1]) * 1024,
                used: parseInt(data[2]) * 1024
            };
        }
    } catch (error) {
        console.error('获取磁盘空间失败:', error);
        // 返回默认值
        return {
            available: 100 * 1024 * 1024 * 1024, // 100GB
            total: 500 * 1024 * 1024 * 1024,     // 500GB
            used: 400 * 1024 * 1024 * 1024       // 400GB
        };
    }
});

// 开始安装
ipcMain.handle('start-installation', async (event, installPath) => {
    try {
        console.log('开始安装到:', installPath);
        
        // 创建安装目录
        if (!fs.existsSync(installPath)) {
            fs.mkdirSync(installPath, { recursive: true });
        }
        
        // 创建安装器实例
        const installer = new AIVisionInstaller(installPath);
        
        // 开始安装流程
        await installer.install((progress) => {
            // 发送进度更新到渲染进程
            mainWindow.webContents.send('installation-progress', progress);
        });
        
        return { success: true };
    } catch (error) {
        console.error('安装失败:', error);
        return { success: false, error: error.message };
    }
});

// 取消安装
ipcMain.handle('cancel-installation', async () => {
    if (installationProcess && !installationProcess.killed) {
        installationProcess.kill();
        installationProcess = null;
    }
    return { success: true };
});

// 打开文件夹
ipcMain.handle('open-folder', async (event, folderPath) => {
    try {
        await shell.openPath(folderPath);
        return { success: true };
    } catch (error) {
        console.error('打开文件夹失败:', error);
        return { success: false, error: error.message };
    }
});

// 关闭安装器
ipcMain.handle('close-installer', async () => {
    app.quit();
});

// 启动AI-Vision启动器
ipcMain.handle('launch-ai-vision', async (event, installPath) => {
    try {
        const launcherPath = path.join(installPath, 'launcher', 'AI-Vision-Launcher.exe');
        
        if (fs.existsSync(launcherPath)) {
            spawn(launcherPath, [], { detached: true, stdio: 'ignore' });
            return { success: true };
        } else {
            throw new Error('启动器文件不存在');
        }
    } catch (error) {
        console.error('启动AI-Vision失败:', error);
        return { success: false, error: error.message };
    }
});

// AI-Vision 安装器类
class AIVisionInstaller {
    constructor(installPath) {
        this.installPath = installPath;
        this.config = INSTALLER_CONFIG;
        this.pythonPath = path.join(installPath, 'python');
        this.comfyuiPath = path.join(installPath, 'ComfyUI');
        this.launcherPath = path.join(installPath, 'launcher');
        this.backendPath = path.join(installPath, 'backend');
    }
    
    async install(progressCallback) {
        const steps = this.config.installSteps.filter(step => step.weight > 0);
        let totalWeight = steps.reduce((sum, step) => sum + step.weight, 0);
        let completedWeight = 0;
        
        for (const step of steps) {
            try {
                console.log(`执行安装步骤: ${step.name}`);
                
                // 发送步骤开始事件
                progressCallback({
                    step: step.id,
                    status: 'active',
                    message: `正在${step.name}...`,
                    progress: Math.floor((completedWeight / totalWeight) * 100)
                });
                
                // 执行步骤
                await this.executeStep(step.id, progressCallback);
                
                // 更新完成权重
                completedWeight += step.weight;
                
                // 发送步骤完成事件
                progressCallback({
                    step: step.id,
                    status: 'completed',
                    message: `${step.name}完成`,
                    progress: Math.floor((completedWeight / totalWeight) * 100)
                });
                
            } catch (error) {
                // 发送步骤错误事件
                progressCallback({
                    step: step.id,
                    status: 'error',
                    message: `${step.name}失败: ${error.message}`,
                    progress: Math.floor((completedWeight / totalWeight) * 100)
                });
                
                throw error;
            }
        }
        
        // 安装完成
        progressCallback({
            step: 'complete',
            status: 'completed',
            message: '安装完成！',
            progress: 100
        });
    }
    
    async executeStep(stepId, progressCallback) {
        switch (stepId) {
            case 'environment':
                await this.detectEnvironment(progressCallback);
                break;
            case 'download-python':
                await this.downloadPython(progressCallback);
                break;
            case 'extract-python':
                await this.setupPython(progressCallback);
                break;
            case 'download-comfyui':
                await this.downloadComfyUI(progressCallback);
                break;
            case 'extract-comfyui':
                await this.setupComfyUI(progressCallback);
                break;
            case 'install-pytorch':
                await this.installPyTorch(progressCallback);
                break;
            case 'install-dependencies':
                await this.installDependencies(progressCallback);
                break;
            case 'configure':
                await this.configureEnvironment(progressCallback);
                break;
            case 'finalize':
                await this.finalizeInstallation(progressCallback);
                break;
            default:
                throw new Error(`未知的安装步骤: ${stepId}`);
        }
    }
    
    async detectEnvironment(progressCallback) {
        // 检测操作系统
        const osInfo = {
            platform: os.platform(),
            arch: os.arch(),
            release: os.release(),
            totalMemory: os.totalmem(),
            freeMemory: os.freemem()
        };
        
        console.log('系统信息:', osInfo);
        
        // 检测GPU (Windows)
        if (process.platform === 'win32') {
            try {
                const { execSync } = require('child_process');
                const gpuInfo = execSync('wmic path win32_VideoController get name', { encoding: 'utf8' });
                console.log('GPU信息:', gpuInfo);
            } catch (error) {
                console.log('GPU检测失败:', error.message);
            }
        }
        
        // 模拟检测时间
        await this.delay(2000);
    }
    
    async downloadPython(progressCallback) {
        const pythonConfig = this.config.python;
        const downloadUrl = pythonConfig.downloadUrl;
        const filename = pythonConfig.filename;
        const targetPath = path.join(this.installPath, 'temp', filename);
        
        // 创建临时目录
        const tempDir = path.dirname(targetPath);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        
        // 下载文件
        await this.downloadFile(downloadUrl, targetPath, (progress) => {
            progressCallback({
                step: 'download-python',
                status: 'active',
                message: `下载Python: ${progress}%`,
                progress: progress
            });
        });
    }
    
    async setupPython(progressCallback) {
        const pythonConfig = this.config.python;
        const zipPath = path.join(this.installPath, 'temp', pythonConfig.filename);
        
        // 解压Python
        const zip = new AdmZip(zipPath);
        zip.extractAllTo(this.pythonPath, true);
        
        // 配置Python环境
        await this.configurePythonEnvironment();
        
        // 模拟配置时间
        await this.delay(3000);
    }
    
    async configurePythonEnvironment() {
        // 创建pth文件
        const pthContent = `
python312.zip
.
Lib
Lib/site-packages
Scripts
        `.trim();
        
        const pthFile = path.join(this.pythonPath, 'python312._pth');
        fs.writeFileSync(pthFile, pthContent);
        
        // 创建必要的目录
        const directories = [
            path.join(this.pythonPath, 'Scripts'),
            path.join(this.pythonPath, 'Lib'),
            path.join(this.pythonPath, 'Lib', 'site-packages')
        ];
        
        directories.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }
    
    async downloadComfyUI(progressCallback) {
        const comfyuiConfig = this.config.comfyui;
        const downloadUrl = comfyuiConfig.downloadUrl || `${comfyuiConfig.repository}/archive/${comfyuiConfig.branch}.zip`;
        const targetPath = path.join(this.installPath, 'temp', comfyuiConfig.filename);
        
        // 下载ComfyUI
        await this.downloadFile(downloadUrl, targetPath, (progress) => {
            progressCallback({
                step: 'download-comfyui',
                status: 'active',
                message: `下载ComfyUI: ${progress}%`,
                progress: progress
            });
        });
    }
    
    async setupComfyUI(progressCallback) {
        const comfyuiConfig = this.config.comfyui;
        const zipPath = path.join(this.installPath, 'temp', comfyuiConfig.filename);
        
        // 解压ComfyUI
        const zip = new AdmZip(zipPath);
        const tempExtractPath = path.join(this.installPath, 'temp', 'comfyui-extract');
        zip.extractAllTo(tempExtractPath, true);
        
        // 移动到最终位置
        const extractedDir = path.join(tempExtractPath, 'ComfyUI-master');
        if (fs.existsSync(extractedDir)) {
            fs.renameSync(extractedDir, this.comfyuiPath);
        }
        
        await this.delay(2000);
    }
    
    async installPyTorch(progressCallback) {
        // 这里应该实际安装PyTorch
        // 为了演示，我们模拟安装过程
        const packages = this.config.pytorch.packages;
        
        for (let i = 0; i < packages.length; i++) {
            const progress = Math.floor((i / packages.length) * 100);
            progressCallback({
                step: 'install-pytorch',
                status: 'active',
                message: `安装 ${packages[i]}: ${progress}%`,
                progress: progress
            });
            
            await this.delay(3000); // 模拟安装时间
        }
    }
    
    async installDependencies(progressCallback) {
        // 安装依赖包
        const dependencies = [...this.config.dependencies.core, ...this.config.dependencies.backend];
        
        for (let i = 0; i < dependencies.length; i++) {
            const progress = Math.floor((i / dependencies.length) * 100);
            progressCallback({
                step: 'install-dependencies',
                status: 'active',
                message: `安装 ${dependencies[i]}: ${progress}%`,
                progress: progress
            });
            
            await this.delay(1000); // 模拟安装时间
        }
    }
    
    async configureEnvironment(progressCallback) {
        // 复制启动器文件
        await this.copyLauncherFiles();
        
        // 复制后端文件
        await this.copyBackendFiles();
        
        // 创建启动脚本
        await this.createStartupScripts();
        
        await this.delay(2000);
    }
    
    async copyLauncherFiles() {
        const sourcePath = path.join(__dirname, '../AI-Vision-Launcher');
        
        if (fs.existsSync(sourcePath)) {
            this.copyDirectory(sourcePath, this.launcherPath);
        }
    }
    
    async copyBackendFiles() {
        const sourcePath = path.join(__dirname, '../AI-Vision-Launcher/backend');
        
        if (fs.existsSync(sourcePath)) {
            this.copyDirectory(sourcePath, this.backendPath);
        }
    }
    
    copyDirectory(source, destination) {
        if (!fs.existsSync(destination)) {
            fs.mkdirSync(destination, { recursive: true });
        }
        
        const items = fs.readdirSync(source);
        
        items.forEach(item => {
            const sourcePath = path.join(source, item);
            const destPath = path.join(destination, item);
            
            if (fs.statSync(sourcePath).isDirectory()) {
                this.copyDirectory(sourcePath, destPath);
            } else {
                fs.copyFileSync(sourcePath, destPath);
            }
        });
    }
    
    async createStartupScripts() {
        // 创建ComfyUI启动脚本
        const comfyuiScript = `@echo off
cd /d "${this.comfyuiPath}"
"${path.join(this.pythonPath, 'python.exe')}" main.py --listen 127.0.0.1 --port 8188
pause`;
        
        fs.writeFileSync(path.join(this.comfyuiPath, 'start-comfyui.bat'), comfyuiScript);
        
        // 创建后端启动脚本
        const backendScript = `@echo off
cd /d "${this.backendPath}"
"${path.join(this.pythonPath, 'python.exe')}" start_fixed_cors.py
pause`;
        
        fs.writeFileSync(path.join(this.backendPath, 'start-backend.bat'), backendScript);
    }
    
    async finalizeInstallation(progressCallback) {
        // 创建桌面快捷方式
        await this.createDesktopShortcut();
        
        // 清理临时文件
        await this.cleanupTempFiles();
        
        await this.delay(1000);
    }
    
    async createDesktopShortcut() {
        // 在Windows上创建快捷方式
        if (process.platform === 'win32') {
            const desktopPath = path.join(os.homedir(), 'Desktop');
            const shortcutPath = path.join(desktopPath, 'AI-Vision Launcher.lnk');
            const targetPath = path.join(this.launcherPath, 'AI-Vision-Launcher.exe');
            
            // 这里需要使用Windows API或第三方库来创建快捷方式
            // 为了简化，我们跳过实际创建
            console.log('创建桌面快捷方式:', shortcutPath, '->', targetPath);
        }
    }
    
    async cleanupTempFiles() {
        const tempDir = path.join(this.installPath, 'temp');
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    }
    
    async downloadFile(url, targetPath, progressCallback) {
        return new Promise((resolve, reject) => {
            const file = fs.createWriteStream(targetPath);
            const protocol = url.startsWith('https:') ? https : http;

            const request = protocol.get(url, (response) => {
                // 处理重定向
                if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
                    file.close();
                    if (fs.existsSync(targetPath)) {
                        fs.unlinkSync(targetPath);
                    }
                    // 递归处理重定向
                    this.downloadFile(response.headers.location, targetPath, progressCallback)
                        .then(resolve)
                        .catch(reject);
                    return;
                }

                if (response.statusCode !== 200) {
                    file.close();
                    if (fs.existsSync(targetPath)) {
                        fs.unlinkSync(targetPath);
                    }
                    reject(new Error(`下载失败: ${response.statusCode}`));
                    return;
                }
                
                const totalSize = parseInt(response.headers['content-length'], 10);
                let downloadedSize = 0;
                
                response.on('data', (chunk) => {
                    downloadedSize += chunk.length;
                    const progress = Math.floor((downloadedSize / totalSize) * 100);
                    progressCallback(progress);
                });
                
                response.pipe(file);
                
                file.on('finish', () => {
                    file.close();
                    resolve();
                });
                
                file.on('error', (error) => {
                    // 安全删除部分下载的文件
                    if (fs.existsSync(targetPath)) {
                        fs.unlink(targetPath, () => {});
                    }
                    reject(error);
                });
            });
            
            request.on('error', (error) => {
                reject(error);
            });
        });
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
