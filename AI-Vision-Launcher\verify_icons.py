#!/usr/bin/env python3
"""
验证图标质量和完整性
"""

from PIL import Image
import os

def verify_icon_quality():
    """验证所有图标的质量和完整性"""
    print("AI视界启动器 - 图标质量验证")
    print("=" * 50)
    
    # 检查的图标文件
    icon_files = [
        ("icon.png", "主图标", (512, 512)),
        ("icon.ico", "Windows ICO图标", None),  # ICO文件包含多个尺寸
        ("tray-icon.png", "系统托盘图标", (32, 32)),
        ("taskbar-icon.png", "任务栏图标", (48, 48)),
        ("desktop-icon.png", "桌面快捷方式图标", (256, 256)),
        ("icon-16.png", "16x16小图标", (16, 16)),
        ("icon-24.png", "24x24小图标", (24, 24)),
        ("icon-32.png", "32x32标准图标", (32, 32)),
        ("icon-48.png", "48x48任务栏图标", (48, 48)),
        ("icon-64.png", "64x64中等图标", (64, 64)),
        ("icon-96.png", "96x96大图标", (96, 96)),
        ("icon-128.png", "128x128大图标", (128, 128)),
        ("icon-256.png", "256x256高清图标", (256, 256)),
    ]
    
    print("检查图标文件:")
    all_good = True
    
    for filename, description, expected_size in icon_files:
        icon_path = os.path.join("assets", filename)
        
        if not os.path.exists(icon_path):
            print(f"  ❌ {description}: 文件不存在 - {icon_path}")
            all_good = False
            continue
        
        try:
            if filename.endswith('.ico'):
                # ICO文件特殊处理
                file_size = os.path.getsize(icon_path)
                print(f"  ✓ {description}: 存在 ({file_size} bytes)")
            else:
                # PNG文件检查
                with Image.open(icon_path) as img:
                    actual_size = img.size
                    mode = img.mode
                    
                    if expected_size and actual_size != expected_size:
                        print(f"  ⚠️  {description}: 尺寸不匹配 - 期望{expected_size}, 实际{actual_size}")
                        all_good = False
                    else:
                        print(f"  ✓ {description}: {actual_size}, {mode}模式")
                        
                    # 检查透明度支持
                    if mode not in ['RGBA', 'LA']:
                        print(f"    ⚠️  警告: {description} 不支持透明度 ({mode}模式)")
                        
        except Exception as e:
            print(f"  ❌ {description}: 读取失败 - {e}")
            all_good = False
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("✅ 所有图标检查通过！")
        print("\n图标使用说明:")
        print("• 主窗口图标: icon.png (512x512)")
        print("• 系统托盘图标: tray-icon.png (32x32)")
        print("• 任务栏图标: taskbar-icon.png (48x48)")
        print("• 桌面快捷方式: icon.ico (多尺寸)")
        print("• 所有图标都基于512x512高分辨率源生成")
        
        print("\n显示质量优化:")
        print("• 小尺寸图标(≤48px)已进行锐化处理")
        print("• 使用LANCZOS高质量重采样算法")
        print("• 支持RGBA透明度")
        print("• 针对不同用途优化尺寸")
        
    else:
        print("❌ 发现图标问题，请重新生成图标")
        print("运行命令: python create_icon.py")
        print("然后运行: python optimize_icons.py")
    
    return all_good

def check_electron_integration():
    """检查Electron集成配置"""
    print("\n检查Electron集成:")
    
    # 检查main.js中的图标配置
    main_js_path = "main.js"
    if os.path.exists(main_js_path):
        with open(main_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查窗口图标配置
        if 'icon.png' in content:
            print("  ✓ 主窗口使用高分辨率PNG图标")
        elif 'icon.ico' in content:
            print("  ⚠️  主窗口使用ICO图标 (建议使用PNG)")
        else:
            print("  ❌ 未找到主窗口图标配置")
            
        # 检查托盘图标配置
        if 'tray-icon.png' in content or 'icon.png' in content:
            print("  ✓ 系统托盘图标配置正确")
        else:
            print("  ❌ 系统托盘图标配置缺失")
            
        # 检查快捷方式图标配置
        if 'icon.ico' in content:
            print("  ✓ 桌面快捷方式使用ICO图标")
        else:
            print("  ⚠️  桌面快捷方式图标配置可能缺失")
    else:
        print("  ❌ 找不到main.js文件")

def main():
    """主函数"""
    success = verify_icon_quality()
    check_electron_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 图标系统验证完成！应用应该显示高质量图标。")
    else:
        print("⚠️  发现问题，请按提示修复后重新验证。")

if __name__ == "__main__":
    main()
