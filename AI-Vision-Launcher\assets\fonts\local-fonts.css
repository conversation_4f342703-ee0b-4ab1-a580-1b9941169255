/* 本地字体定义 - 替代Google Fonts CDN */
@font-face {
    font-family: 'JetBrains Mono';
    src: local('JetBrains Mono'), local('Consolas'), local('Monaco'), local('monospace');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Rajdhani';
    src: local('<PERSON><PERSON><PERSON>'), local('Arial'), local('sans-serif');
    font-weight: 300 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Orbitron';
    src: local('Orbitron'), local('Impact'), local('Arial Black'), local('sans-serif');
    font-weight: 400 900;
    font-style: normal;
    font-display: swap;
}

/* 字体 fallback 优化 */
.font-primary {
    font-family: 'JetBrains Mono', 'Consolas', 'Monaco', 'Courier New', monospace;
}

.font-display {
    font-family: 'Orbitron', 'Impact', 'Arial Black', sans-serif;
}

.font-ui {
    font-family: 'Raj<PERSON><PERSON>', 'Arial', 'Microsoft YaHei', sans-serif;
}