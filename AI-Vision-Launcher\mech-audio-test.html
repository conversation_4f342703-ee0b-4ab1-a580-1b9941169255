<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机甲音效测试</title>
    <style>
        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #0a0a0f, #1a1a2e);
            color: #00f5ff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: rgba(26, 26, 46, 0.8);
            border: 2px solid #00f5ff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-shadow: 0 0 10px #00f5ff;
            color: #ffffff;
        }

        .audio-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 245, 255, 0.05);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 10px;
        }

        .section-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #00f5ff;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .mech-btn {
            background: linear-gradient(45deg, #1a1a2e, #16213e);
            border: 2px solid #00f5ff;
            color: #ffffff;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .mech-btn:hover {
            background: linear-gradient(45deg, #16213e, #1a1a2e);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
            transform: translateY(-2px);
        }

        .mech-btn:active {
            transform: translateY(0);
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
        }

        .mech-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .mech-btn:hover::before {
            left: 100%;
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        label {
            color: #00f5ff;
            font-weight: 600;
        }

        input[type="range"] {
            width: 150px;
            height: 5px;
            background: #1a1a2e;
            border-radius: 5px;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: #00f5ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }

        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #00f5ff;
        }

        .volume-display {
            color: #ffffff;
            font-weight: bold;
            min-width: 40px;
        }

        .description {
            color: #a0a0a0;
            font-size: 0.9rem;
            margin-top: 10px;
            line-height: 1.4;
        }

        .emoji {
            font-size: 1.2rem;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 自定义音效系统测试</h1>
        
        <div class="controls">
            <div class="control-group">
                <label for="volume">音量:</label>
                <input type="range" id="volume" min="0" max="100" value="60">
                <span class="volume-display" id="volumeDisplay">60%</span>
            </div>
            <div class="control-group">
                <input type="checkbox" id="enabled" checked>
                <label for="enabled">启用音效</label>
            </div>
        </div>

        <div class="audio-section">
            <div class="section-title">🔧 统一导航音效</div>
            <div class="description">所有按钮都使用统一的导航点击音效（除了ComfyUI启动/关闭）</div>
            <div class="button-grid">
                <button class="mech-btn" onclick="playSound('click')">
                    <span class="emoji">🔘</span>导航标签点击
                </button>
                <button class="mech-btn" onclick="playSound('tab-switch')">
                    <span class="emoji">📑</span>页面标签切换
                </button>
                <button class="mech-btn" onclick="playSound('hover')">
                    <span class="emoji">📡</span>悬停音效
                </button>
                <button class="mech-btn" onclick="playSound('switch')">
                    <span class="emoji">🔄</span>功能按钮
                </button>
            </div>
        </div>

        <div class="audio-section">
            <div class="section-title">🚨 反馈音效</div>
            <div class="description">成功操作使用专用反馈音效，其他使用导航点击音效</div>
            <div class="button-grid">
                <button class="mech-btn" onclick="playSound('success')">
                    <span class="emoji">✅</span>操作成功反馈
                </button>
                <button class="mech-btn" onclick="playSound('plugin-success')">
                    <span class="emoji">🔌</span>插件操作成功
                </button>
                <button class="mech-btn" onclick="playSound('version-success')">
                    <span class="emoji">📋</span>版本切换成功
                </button>
                <button class="mech-btn" onclick="playSound('warning')">
                    <span class="emoji">⚠️</span>警告提示
                </button>
                <button class="mech-btn" onclick="playSound('error')">
                    <span class="emoji">🚨</span>错误提示
                </button>
                <button class="mech-btn" onclick="playSound('notification')">
                    <span class="emoji">📢</span>系统通知
                </button>
            </div>
        </div>

        <div class="audio-section">
            <div class="section-title">🚀 系统音效</div>
            <div class="description">ComfyUI和启动器的系统级操作音效</div>
            <div class="button-grid">
                <button class="mech-btn" onclick="playSound('startup')">
                    <span class="emoji">🚀</span>启动ComfyUI
                </button>
                <button class="mech-btn" onclick="playSound('startup-success')">
                    <span class="emoji">✅</span>启动成功
                </button>
                <button class="mech-btn" onclick="playSound('shutdown')">
                    <span class="emoji">🔌</span>关闭ComfyUI
                </button>
                <button class="mech-btn" onclick="playSound('shutdown-success')">
                    <span class="emoji">✅</span>关闭成功
                </button>
                <button class="mech-btn" onclick="playSound('app-close')">
                    <span class="emoji">❌</span>关闭启动器
                </button>
            </div>
        </div>
    </div>

    <script src="assets/sounds/audio-manager.js"></script>
    <script>
        let audioManager;

        // 初始化音效管理器
        document.addEventListener('DOMContentLoaded', async () => {
            if (window.TechAudioManager) {
                audioManager = new TechAudioManager();
                await audioManager.init();
                console.log('机甲音效系统初始化完成');
                
                // 设置控件事件
                setupControls();
            }
        });

        function setupControls() {
            const volumeSlider = document.getElementById('volume');
            const volumeDisplay = document.getElementById('volumeDisplay');
            const enabledCheckbox = document.getElementById('enabled');

            volumeSlider.addEventListener('input', (e) => {
                const volume = e.target.value / 100;
                audioManager.setVolume(volume);
                volumeDisplay.textContent = e.target.value + '%';
            });

            enabledCheckbox.addEventListener('change', (e) => {
                audioManager.setEnabled(e.target.checked);
            });
        }

        function playSound(soundName) {
            if (audioManager) {
                audioManager.play(soundName);
                console.log(`播放机甲音效: ${soundName}`);
            }
        }
    </script>
</body>
</html>
