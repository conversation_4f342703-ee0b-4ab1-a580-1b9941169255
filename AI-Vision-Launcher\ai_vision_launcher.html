<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI视界启动器</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Black+Ops+One&family=Bungee&family=Rajdhani:wght@300;400;500;600;700&family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- 音效管理器 -->
    <script src="audio-config-sync.js"></script>
    <script src="sync-audio-config.js"></script>
    <script src="apply-audio-config.js"></script>
    <script src="assets/sounds/audio-manager.js"></script>
    <style>
        :root {
            --primary-bg: #0a0a0f;
            --secondary-bg: #1a1a2e;
            --accent-cyan: #00f5ff;
            --accent-blue: #0066ff;
            --neon-glow: #00ffff;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --border-glow: #00f5ff;
            --card-bg: rgba(26, 26, 46, 0.8);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', 'Consolas', monospace;
            background: var(--primary-bg);
            color: var(--text-primary);
            overflow: hidden;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 防止水平滚动条和内嵌滚动条 */
        * {
            box-sizing: border-box;
        }

        .page-content, .plugin-management-container, .plugin-content {
            overflow-x: hidden !important;
        }

        /* 强制防止插件页面出现内嵌滚动条 */
        #available-plugin-list, #installed-plugin-list {
            overflow: visible !important;
            max-height: none !important;
            height: auto !important;
        }

        /* 确保插件管理页面容器不会产生滚动条 */
        .plugin-management-page, .installed-plugins, .available-plugins {
            overflow: visible !important;
        }

        /* 插件列表容器 - 防止内嵌滚动条 */
        .plugin-list {
            overflow: visible !important;
            max-height: none !important;
            height: auto !important;
        }

        /* 主容器 - 竖版比例 */
        .launcher-container {
            width: 600px;
            height: 900px;
            background: var(--card-bg);
            border: 2px solid var(--border-glow);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(20px);
            box-shadow: 
                0 0 30px rgba(0, 245, 255, 0.3),
                0 0 60px rgba(0, 245, 255, 0.1),
                inset 0 0 30px rgba(0, 245, 255, 0.05);
        }

        /* 背景容器 */
        .background-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            border-radius: 18px;
            overflow: hidden;
        }

        .background-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            filter: brightness(1.0) contrast(1.1);
            transition: all 0.5s ease;
        }

        .background-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            filter: brightness(1.0) contrast(1.1);
            transition: all 0.5s ease;
            display: none;
        }

        .background-image.dimmed {
            filter: brightness(0.15) contrast(1.2);
            opacity: 0.6;
        }

        .background-video.dimmed {
            filter: brightness(0.15) contrast(1.2);
            opacity: 0.6;
        }

        .background-overlay {
            display: none;
        }

        /* 内容层 */
        .content-layer {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 25px;
        }

        /* 顶部标题区域 */
        .header-section {
            text-align: center;
            margin-bottom: 25px;
        }

        .app-title {
            font-family: 'Rajdhani', sans-serif;
            font-size: 2.8rem;
            font-weight: 700;
            color: var(--neon-glow);
            text-shadow: 
                0 0 10px #00BFFF,
                0 0 20px #0080FF,
                0 0 30px #004FFF,
                0 0 40px #002FFF,
                1px 1px 3px rgba(0, 0, 0, 0.8);
            letter-spacing: 3px;
            margin-bottom: 8px;
        }

        .app-subtitle {
            font-family: 'Exo 2', sans-serif;
            font-size: 1rem;
            color: var(--neon-glow);
            font-weight: 300;
            letter-spacing: 2px;
            text-shadow: 
                0 0 6px #00BFFF,
                0 0 12px #0080FF,
                0 0 18px #004FFF,
                1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        /* 功能导航标签 */
        .navigation-tabs {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
            gap: 8px;
        }

        .tab-btn {
            flex: 1;
            padding: 10px 8px;
            background: transparent;
            border: 1px solid transparent;
            border-radius: 8px;
            color: var(--neon-glow);
            font-family: 'Exo 2', sans-serif;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-shadow: 
                0 0 8px var(--neon-glow),
                0 0 16px var(--neon-glow),
                2px 2px 6px rgba(0, 0, 0, 0.9);
        }

        .tab-btn:hover {
            color: #FFFFFF;
            text-shadow: 
                0 0 2px #6A5ACD,
                0 0 4px #6A5ACD,
                0 0 8px #6A5ACD,
                0 0 12px #6A5ACD,
                1px 1px 3px rgba(0, 0, 0, 1),
                -1px -1px 3px rgba(0, 0, 0, 1),
                1px -1px 3px rgba(0, 0, 0, 1),
                -1px 1px 3px rgba(0, 0, 0, 1);
        }

        .tab-btn.active {
            color: #FFFFFF;
            text-shadow: 
                0 0 3px #6A5ACD,
                0 0 6px #6A5ACD,
                0 0 12px #6A5ACD,
                0 0 18px #6A5ACD,
                1px 1px 4px rgba(0, 0, 0, 1),
                -1px -1px 4px rgba(0, 0, 0, 1),
                1px -1px 4px rgba(0, 0, 0, 1),
                -1px 1px 4px rgba(0, 0, 0, 1);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
            position: relative;
            min-height: 0;
            width: 100%;
            height: 100%;
        }

        .main-home {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 30px;
            position: relative;
            z-index: 50;
        }

        /* 当插件管理页面激活时，隐藏主页 */
        .main-home.hidden {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            z-index: 1 !important;
        }

        /* 图片上传区域 */
        .upload-section {
            position: absolute;
            top: 25px;
            right: 25px;
            z-index: 20;
        }

        .upload-btn {
            background: transparent;
            border: 1px solid transparent;
            border-radius: 8px;
            padding: 8px 12px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
            text-shadow: 
                0 0 8px rgba(255, 255, 255, 0.6),
                0 0 16px rgba(255, 255, 255, 0.4),
                2px 2px 6px rgba(0, 0, 0, 0.9);
            margin-right: 8px;
        }

        .upload-btn:hover {
            color: var(--neon-glow);
            text-shadow: 
                0 0 12px var(--neon-glow),
                0 0 24px var(--neon-glow),
                2px 2px 8px rgba(0, 0, 0, 0.9);
        }



        /* 配置信息卡片 */
        .info-card {
            background: rgba(0, 245, 255, 0.08);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(15px);
        }

        .info-card h3 {
            color: var(--neon-glow);
            font-size: 1rem;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
        }

        .info-label {
            color: var(--text-secondary);
        }

        .info-value {
            color: var(--neon-glow);
            font-weight: 500;
        }

        /* 功能内容区域 - 优化性能 */
        .tab-content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 10, 15, 0.95);
            padding: 30px;
            overflow-y: auto;
            display: none;
            z-index: 100;
            border-radius: 18px;
            /* 移除backdrop-filter以提高性能 */
        }

        .tab-content.active {
            display: block !important;
            z-index: 200 !important;
        }

        /* 插件管理页面使用通用样式，不需要特殊覆盖 */

        /* 简化的插件管理布局 */
        .plugin-management-simple {
            width: 100% !important;
            height: 100% !important;
            color: white !important;
        }

        .plugin-header-simple {
            display: flex !important;
            align-items: center !important;
            margin-bottom: 25px !important;
            padding-bottom: 15px !important;
            border-bottom: 1px solid #333 !important;
        }

        .plugin-header-simple h2 {
            color: #00f5ff !important;
            margin: 0 !important;
            font-size: 24px !important;
        }

        .plugin-header-simple i {
            color: #00f5ff !important;
            font-size: 24px !important;
            margin-right: 15px !important;
        }

        .plugin-count {
            margin-left: auto !important;
            color: #ccc !important;
            font-size: 14px !important;
        }

        .plugin-content-simple {
            background: rgba(0, 245, 255, 0.1) !important;
            border: 1px solid #00f5ff !important;
            border-radius: 8px !important;
            padding: 20px !important;
            margin-bottom: 20px !important;
        }

        .plugin-content-simple h3 {
            color: #00f5ff !important;
            margin: 0 0 15px 0 !important;
            font-size: 18px !important;
        }

        .plugin-items-container {
            /* 移除固定高度限制，使用自然高度 */
            overflow: visible !important;
        }

        .plugin-item-simple {
            background: rgba(255,255,255,0.05) !important;
            padding: 15px !important;
            margin: 10px 0 !important;
            border-radius: 8px !important;
            border-left: 3px solid #00f5ff !important;
        }

        .plugin-item-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            flex-wrap: wrap !important;
        }

        .plugin-info-simple {
            flex: 1 !important;
            min-width: 300px !important;
        }

        .plugin-name-simple {
            color: #00f5ff !important;
            font-size: 16px !important;
            font-weight: bold !important;
            margin-bottom: 8px !important;
        }

        .plugin-status {
            margin-left: 15px !important;
            font-size: 14px !important;
        }

        .plugin-status.enabled {
            color: #28a745 !important;
        }

        .plugin-status.disabled {
            color: #dc3545 !important;
        }

        .plugin-details {
            color: #ccc !important;
            font-size: 12px !important;
            margin-bottom: 5px !important;
        }

        .plugin-description {
            color: #999 !important;
            font-size: 11px !important;
        }

        .plugin-actions-simple {
            display: flex !important;
            gap: 8px !important;
            flex-wrap: wrap !important;
            margin-top: 10px !important;
        }

        .plugin-btn-simple {
            padding: 6px 12px !important;
            border: none !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 12px !important;
            color: white !important;
        }

        .plugin-btn-simple.home { background: #007bff !important; }
        .plugin-btn-simple.update { background: #28a745 !important; }
        .plugin-btn-simple.switch { background: #6c757d !important; }
        .plugin-btn-simple.toggle { background: #ffc107 !important; color: black !important; }
        .plugin-btn-simple.uninstall { background: #dc3545 !important; }

        .plugin-footer {
            text-align: center !important;
            color: #666 !important;
            padding: 20px !important;
            border-top: 1px solid #333 !important;
            font-size: 12px !important;
        }


        /* 底部启动按钮区域 */
        .launch-section {
            margin-top: auto;
            padding-top: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .launch-btn {
            width: 90%;
            min-width: 400px;
            padding: 18px 30px;
            font-size: 1.3rem;
            font-weight: bold;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-blue));
            border: 2px solid var(--neon-glow);
            border-radius: 12px;
            color: #000;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-family: inherit;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            box-shadow: 
                0 0 20px rgba(0, 245, 255, 0.4),
                0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 0 25px var(--neon-glow),
                0 0 50px var(--neon-glow),
                0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .launch-btn:active {
            transform: translateY(0);
        }

        .launch-btn.running {
            background: linear-gradient(45deg, #ff4757, #ff3742);
            border-color: #ff4757;
        }

        /* 浏览器按钮 */
        .browser-btn {
            width: 90%;
            min-width: 400px;
            padding: 12px 25px;
            font-size: 1.1rem;
            font-weight: 600;
            background: linear-gradient(45deg, rgba(0, 245, 255, 0.1), rgba(0, 102, 255, 0.1));
            border: 2px solid rgba(0, 245, 255, 0.6);
            border-radius: 10px;
            color: var(--accent-cyan);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-family: inherit;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
            box-shadow:
                0 0 15px rgba(0, 245, 255, 0.2),
                0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .browser-btn:hover {
            background: linear-gradient(45deg, rgba(0, 245, 255, 0.2), rgba(0, 102, 255, 0.2));
            border-color: var(--neon-glow);
            transform: translateY(-1px);
            box-shadow:
                0 0 20px rgba(0, 245, 255, 0.4),
                0 3px 12px rgba(0, 0, 0, 0.3);
        }

        .browser-btn:active {
            transform: translateY(0);
        }

        /* 显卡信息 */
        .gpu-info {
            position: relative;
            margin-top: 15px;
            width: 90%;
            min-width: 400px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(0, 245, 255, 0.5);
            border-radius: 10px;
            backdrop-filter: blur(15px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .gpu-details {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .gpu-icon-container {
            position: relative;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gpu-icon {
            font-size: 1.5rem;
            color: var(--neon-glow);
            z-index: 2;
        }

        .rotating-ring {
            position: absolute;
            top: -2px;
            left: -2px;
            width: 39px;
            height: 39px;
            border: 2px solid transparent;
            border-top: 2px solid var(--neon-glow);
            border-right: 2px solid var(--neon-glow);
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
            box-shadow: 0 0 8px var(--neon-glow);
            pointer-events: none; /* 确保不影响点击事件 */
        }

        .gpu-info.working::before {
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            background: radial-gradient(circle,
                #ffffff 0%,
                #ffffff 25%,
                var(--neon-glow) 50%,
                rgba(0, 245, 255, 0.8) 75%,
                rgba(0, 245, 255, 0.4) 90%,
                transparent 100%
            );
            border-radius: 50%;
            opacity: 1;
            animation: snake-head-trace 3s linear infinite;
            pointer-events: none;
            z-index: 5;
            box-shadow:
                0 0 10px #ffffff,
                0 0 20px var(--neon-glow);
        }

        .gpu-info.working::after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(255, 255, 255, 0.7) 20%,
                var(--neon-glow) 40%,
                rgba(0, 245, 255, 0.7) 65%,
                rgba(0, 245, 255, 0.4) 85%,
                transparent 100%
            );
            border-radius: 50%;
            opacity: 1;
            animation: snake-body1-trace 3s linear infinite;
            pointer-events: none;
            z-index: 4;
            box-shadow: 0 0 8px var(--neon-glow);
        }

        /* 蛇身体段 - 适中的尺寸递减，保持亮度 */
        .gpu-info.working .gpu-chip::before {
            content: '';
            position: absolute;
            width: 7px;
            height: 7px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.8) 0%,
                rgba(255, 255, 255, 0.6) 25%,
                var(--neon-glow) 45%,
                rgba(0, 245, 255, 0.6) 70%,
                rgba(0, 245, 255, 0.3) 90%,
                transparent 100%
            );
            border-radius: 50%;
            opacity: 1;
            animation: snake-body2-trace 3s linear infinite;
            pointer-events: none;
            z-index: 3;
            box-shadow: 0 0 7px rgba(0, 245, 255, 0.7);
        }

        .gpu-info.working .gpu-chip::after {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.7) 0%,
                rgba(255, 255, 255, 0.5) 30%,
                var(--neon-glow) 50%,
                rgba(0, 245, 255, 0.5) 75%,
                rgba(0, 245, 255, 0.3) 90%,
                transparent 100%
            );
            border-radius: 50%;
            opacity: 1;
            animation: snake-body3-trace 3s linear infinite;
            pointer-events: none;
            z-index: 2;
            box-shadow: 0 0 6px rgba(0, 245, 255, 0.6);
        }

        .gpu-info.working .gpu-name::before {
            content: '';
            position: absolute;
            width: 5px;
            height: 5px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.6) 0%,
                rgba(255, 255, 255, 0.4) 35%,
                var(--neon-glow) 55%,
                rgba(0, 245, 255, 0.4) 80%,
                transparent 100%
            );
            border-radius: 50%;
            opacity: 1;
            animation: snake-body4-trace 3s linear infinite;
            pointer-events: none;
            z-index: 1;
            box-shadow: 0 0 5px rgba(0, 245, 255, 0.5);
        }

        .gpu-info.working .gpu-name::after {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.5) 0%,
                rgba(255, 255, 255, 0.3) 40%,
                var(--neon-glow) 60%,
                rgba(0, 245, 255, 0.3) 85%,
                transparent 100%
            );
            border-radius: 50%;
            opacity: 1;
            animation: snake-tail-trace 3s linear infinite;
            pointer-events: none;
            z-index: 1;
            box-shadow: 0 0 4px rgba(0, 245, 255, 0.4);
        }



        @keyframes snake-head-trace {
            /* 蛇头 - 领先移动 (10px) */
            0% {
                top: -5px;
                left: -5px;
            }
            25% {
                top: -5px;
                left: calc(100% - 5px);
            }
            50% {
                top: calc(100% - 5px);
                left: calc(100% - 5px);
            }
            75% {
                top: calc(100% - 5px);
                left: -5px;
            }
            100% {
                top: -5px;
                left: -5px;
            }
        }

        @keyframes snake-body1-trace {
            /* 蛇身1 - 延迟3% (8px) */
            0% {
                top: -4px;
                left: -12px;
            }
            3% {
                top: -4px;
                left: -4px;
            }
            28% {
                top: -4px;
                left: calc(100% - 4px);
            }
            53% {
                top: calc(100% - 4px);
                left: calc(100% - 4px);
            }
            78% {
                top: calc(100% - 4px);
                left: -4px;
            }
            100% {
                top: -4px;
                left: -12px;
            }
        }

        @keyframes snake-body2-trace {
            /* 蛇身2 - 延迟6% (7px) */
            0% {
                top: -3.5px;
                left: -20px;
            }
            6% {
                top: -3.5px;
                left: -3.5px;
            }
            31% {
                top: -3.5px;
                left: calc(100% - 3.5px);
            }
            56% {
                top: calc(100% - 3.5px);
                left: calc(100% - 3.5px);
            }
            81% {
                top: calc(100% - 3.5px);
                left: -3.5px;
            }
            100% {
                top: -3.5px;
                left: -20px;
            }
        }

        @keyframes snake-body3-trace {
            /* 蛇身3 - 延迟9% (6px) */
            0% {
                top: -3px;
                left: -28px;
            }
            9% {
                top: -3px;
                left: -3px;
            }
            34% {
                top: -3px;
                left: calc(100% - 3px);
            }
            59% {
                top: calc(100% - 3px);
                left: calc(100% - 3px);
            }
            84% {
                top: calc(100% - 3px);
                left: -3px;
            }
            100% {
                top: -3px;
                left: -28px;
            }
        }

        @keyframes snake-body4-trace {
            /* 蛇身4 - 延迟12% (5px) */
            0% {
                top: -2.5px;
                left: -36px;
            }
            12% {
                top: -2.5px;
                left: -2.5px;
            }
            37% {
                top: -2.5px;
                left: calc(100% - 2.5px);
            }
            62% {
                top: calc(100% - 2.5px);
                left: calc(100% - 2.5px);
            }
            87% {
                top: calc(100% - 2.5px);
                left: -2.5px;
            }
            100% {
                top: -2.5px;
                left: -36px;
            }
        }

        @keyframes snake-tail-trace {
            /* 蛇尾 - 延迟15% (4px) */
            0% {
                top: -2px;
                left: -44px;
            }
            15% {
                top: -2px;
                left: -2px;
            }
            40% {
                top: -2px;
                left: calc(100% - 2px);
            }
            65% {
                top: calc(100% - 2px);
                left: calc(100% - 2px);
            }
            90% {
                top: calc(100% - 2px);
                left: -2px;
            }
            100% {
                top: -2px;
                left: -44px;
            }
        }







        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 8px var(--neon-glow);
                border-top-color: var(--neon-glow);
                border-right-color: var(--neon-glow);
            }
            50% {
                box-shadow: 0 0 15px var(--neon-glow), 0 0 20px var(--neon-glow);
                border-top-color: #ffffff;
                border-right-color: #ffffff;
            }
        }

        .gpu-text {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .gpu-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--neon-glow);
            text-shadow: 0 0 5px var(--neon-glow);
        }

        .gpu-usage {
            font-size: 0.75rem;
            color: var(--text-primary);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .gpu-status {
            font-size: 0.8rem;
            color: var(--text-primary);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        /* 扫描线效果 */
        .launcher-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                0deg,
                transparent,
                transparent 2px,
                rgba(0, 245, 255, 0.02) 2px,
                rgba(0, 245, 255, 0.02) 4px
            );
            pointer-events: none;
            z-index: 5;
            border-radius: 18px;
        }

        /* 专家模式按钮 */
        .expert-mode-btn {
            background: rgba(255, 102, 0, 0.2);
            border: 1px solid rgba(255, 102, 0, 0.5);
            color: #ff6600;
            font-size: 0.75rem;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .expert-mode-btn:hover {
            background: rgba(255, 102, 0, 0.3);
            box-shadow: 0 0 10px rgba(255, 102, 0, 0.3);
        }

        /* 启动设置页面样式 */
        .launch-settings-container {
            padding: 20px;
            max-width: 100%;
            color: var(--text-primary);
        }

        .launch-settings-container h3 {
            color: var(--neon-glow);
            font-size: 1.1rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            text-shadow: 0 0 10px var(--neon-glow);
        }

        /* 预设配置卡片 */
        .preset-section {
            margin-bottom: 25px;
        }

        .preset-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 12px;
            margin-top: 15px;
        }

        .preset-card {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .preset-card:hover {
            border-color: var(--neon-glow);
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            transform: translateY(-2px);
        }

        .preset-card.active {
            border-color: var(--neon-glow);
            background: rgba(0, 245, 255, 0.1);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.4);
        }

        .preset-card h4 {
            color: var(--neon-glow);
            font-size: 0.9rem;
            margin-bottom: 6px;
            text-shadow: 0 0 5px var(--neon-glow);
        }

        .preset-card p {
            color: var(--text-secondary);
            font-size: 0.7rem;
            line-height: 1.3;
            font-family: 'JetBrains Mono', monospace;
        }

        /* 设置区域 */
        .memory-section, .attention-section, .network-section, .proxy-section {
            margin-bottom: 25px;
            background: rgba(26, 26, 46, 0.4);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        /* 启动设置页面输入框样式 */
        .launch-settings-container input[type="text"],
        .launch-settings-container input[type="number"],
        .launch-settings-container select {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 6px;
            padding: 8px 12px;
            color: var(--text-primary);
            font-size: 0.9rem;
            font-family: 'JetBrains Mono', monospace;
            transition: all 0.3s ease;
            width: 100%;
        }

        .launch-settings-container input[type="text"]:focus,
        .launch-settings-container input[type="number"]:focus,
        .launch-settings-container select:focus {
            outline: none;
            border-color: var(--neon-glow);
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
            background: rgba(26, 26, 46, 0.8);
        }

        .launch-settings-container select option {
            background: rgba(26, 26, 46, 0.95);
            color: var(--text-primary);
            padding: 8px;
        }

        /* 水平布局的设置项 */
        .setting-item.horizontal {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 15px;
            padding: 8px 0;
        }

        .setting-item.horizontal label {
            flex-shrink: 0;
            min-width: 140px;
            margin-bottom: 0;
            padding-top: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        /* 显存优化版块的特殊调整 - 标签位置与复选框对齐 */
        .memory-section .setting-item.horizontal label {
            margin-left: 24px; /* 与复选框标签对齐 */
        }

        .setting-item.horizontal input,
        .setting-item.horizontal select {
            flex: 1;
            max-width: 280px;
            min-width: 200px;
        }

        .setting-item.horizontal .input-with-hint {
            flex: 1;
            max-width: 280px;
            min-width: 200px;
        }

        .setting-item.horizontal .input-with-hint input,
        .setting-item.horizontal .input-with-hint select {
            width: 100%;
            margin-bottom: 4px;
        }

        .setting-item.horizontal .input-with-hint .hint {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin: 0;
            line-height: 1.3;
            opacity: 0.8;
        }

        /* 代理设置的特殊样式 */
        .proxy-inputs .setting-item.horizontal {
            gap: 15px; /* 减少间距，防止输入框跑出边界 */
        }

        .proxy-inputs .setting-item.horizontal label {
            margin-left: 24px; /* 与"启用代理"复选框标签对齐 */
            min-width: 100px; /* 减少标签宽度，为输入框留出空间 */
        }

        .proxy-inputs .setting-item.horizontal input {
            max-width: 280px; /* 减少最大宽度，防止跑出边界 */
            min-width: 200px;
        }

        /* 单选按钮组 */
        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .radio-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .radio-item:hover {
            background: rgba(0, 245, 255, 0.05);
        }

        .radio-item input[type="radio"] {
            margin-top: 3px;
            accent-color: var(--neon-glow);
        }

        .radio-item span {
            color: var(--text-primary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .radio-item small {
            color: var(--text-secondary);
            font-size: 0.75rem;
            display: block;
            margin-top: 2px;
        }

        /* 复选框组 */
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .checkbox-item:hover {
            background: rgba(0, 245, 255, 0.05);
        }

        .checkbox-item input[type="checkbox"] {
            margin-top: 3px;
            accent-color: var(--neon-glow);
        }

        .checkbox-item span {
            color: var(--text-primary);
            font-size: 0.85rem;
            font-weight: 500;
        }

        .checkbox-item small {
            color: var(--text-secondary);
            font-size: 0.7rem;
            display: block;
            margin-top: 2px;
        }

        /* 输入框组 */
        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .input-item {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .input-item label {
            color: var(--text-primary);
            font-size: 0.85rem;
            font-weight: 500;
        }

        .input-item input {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 6px;
            padding: 8px 12px;
            color: var(--text-primary);
            font-size: 0.85rem;
            font-family: 'JetBrains Mono', monospace;
            transition: all 0.3s ease;
        }

        .input-item input:focus {
            outline: none;
            border-color: var(--neon-glow);
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
        }

        .input-item small {
            color: var(--text-secondary);
            font-size: 0.7rem;
        }

        /* 命令预览 */
        .command-preview {
            margin-top: 20px;
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            backdrop-filter: blur(5px);
        }

        .command-preview h4 {
            color: var(--neon-glow);
            font-size: 0.9rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .command-text {
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 6px;
            padding: 12px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.75rem;
            color: var(--neon-glow);
            word-break: break-all;
            line-height: 1.4;
            text-shadow: 0 0 5px var(--neon-glow);
        }

        /* 专家模式 */
        .expert-settings {
            margin-top: 20px;
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.5s ease;
        }

        .expert-settings.active {
            opacity: 1;
            max-height: 500px;
        }

        /* 启动按钮在设置页面 */
        .settings-launch-btn {
            width: 100%;
            margin-top: 25px;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-blue));
            border: 2px solid var(--neon-glow);
            border-radius: 12px;
            padding: 15px 30px;
            color: #000;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-shadow: none;
        }

        .settings-launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 25px rgba(0, 245, 255, 0.6);
        }

        .settings-launch-btn i {
            margin-right: 8px;
        }

        /* 操作按钮组 */
        .actions-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(0, 245, 255, 0.2);
        }

        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            backdrop-filter: blur(10px);
        }

        .btn-primary {
            background: rgba(0, 102, 255, 0.8);
            color: white;
            border: 1px solid rgba(0, 102, 255, 0.6);
        }

        .btn-primary:hover {
            background: rgba(0, 102, 255, 1);
            box-shadow: 0 0 15px rgba(0, 102, 255, 0.5);
            transform: translateY(-2px);
        }

        .btn-success {
            background: rgba(0, 255, 102, 0.8);
            color: #000;
            border: 1px solid rgba(0, 255, 102, 0.6);
        }

        .btn-success:hover {
            background: rgba(0, 255, 102, 1);
            box-shadow: 0 0 15px rgba(0, 255, 102, 0.5);
            transform: translateY(-2px);
        }

        .btn-warning {
            background: rgba(255, 153, 0, 0.8);
            color: white;
            border: 1px solid rgba(255, 153, 0, 0.6);
        }

        .btn-warning:hover {
            background: rgba(255, 153, 0, 1);
            box-shadow: 0 0 15px rgba(255, 153, 0, 0.5);
            transform: translateY(-2px);
        }

        /* 信息卡片样式 */
        .info-card {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }

        .info-card h3 {
            color: var(--neon-glow);
            font-size: 1.1rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            text-shadow: 0 0 10px var(--neon-glow);
        }

        .info-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 245, 255, 0.1);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .info-value {
            color: var(--neon-glow);
            font-weight: 600;
            font-size: 0.9rem;
            text-shadow: 0 0 5px var(--neon-glow);
        }

        /* 版本管理页面样式 */
        .version-management-container {
            padding: 20px;
            max-width: 100%;
            color: var(--text-primary);
        }

        /* 版本管理头部 */
        .version-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0, 245, 255, 0.2);
        }



        .version-header h2 {
            color: var(--neon-glow);
            font-size: 1.3rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            text-shadow: 0 0 10px var(--neon-glow);
        }

        /* 版本类型切换标签 */
        .version-tabs {
            display: flex;
            gap: 0;
            margin-bottom: 25px;
            background: rgba(26, 26, 46, 0.4);
            border-radius: 10px;
            padding: 4px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 245, 255, 0.2);
        }

        .version-tab-btn {
            flex: 1;
            padding: 12px 20px;
            background: transparent;
            border: none;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .version-tab-btn:hover {
            color: var(--text-primary);
            background: rgba(0, 245, 255, 0.1);
        }

        .version-tab-btn.active {
            color: var(--neon-glow);
            background: rgba(0, 245, 255, 0.15);
            text-shadow: 0 0 10px var(--neon-glow);
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
        }

        /* 版本列表容器 */
        .version-list-container {
            position: relative;
            min-height: 400px;
        }

        /* 加载状态 */
        .version-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 245, 255, 0.3);
            border-top: 3px solid var(--neon-glow);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* 版本列表 */
        .version-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        /* 版本条目 */
        .version-item {
            background: rgba(26, 26, 46, 0.4);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 10px;
            padding: 16px 20px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .version-item:hover {
            border-color: rgba(0, 245, 255, 0.4);
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.2);
        }

        .version-item.current {
            border-color: var(--neon-glow);
            background: rgba(0, 245, 255, 0.03);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
            animation: currentVersionPulse 3s ease-in-out infinite;
        }

        @keyframes currentVersionPulse {
            0%, 100% { 
                box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
            }
            50% { 
                box-shadow: 0 0 25px rgba(0, 245, 255, 0.5);
            }
        }

        /* 版本信息 */
        .version-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .version-main {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .version-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(0, 245, 255, 0.3);
            transition: all 0.3s ease;
        }

        .version-item.current .version-indicator {
            background: var(--neon-glow);
            box-shadow: 0 0 10px var(--neon-glow);
        }

        .version-item.latest {
            border-color: rgba(0, 245, 255, 0.4);
            background: rgba(0, 245, 255, 0.05);
        }

        .version-item.latest .version-indicator {
            background: rgba(0, 245, 255, 0.6);
            box-shadow: 0 0 8px rgba(0, 245, 255, 0.4);
        }

        .version-id {
            color: var(--neon-glow);
            font-weight: 600;
            font-size: 1rem;
            text-shadow: 0 0 5px var(--neon-glow);
            font-family: 'JetBrains Mono', monospace;
        }

        .version-date {
            color: var(--text-secondary);
            font-size: 0.85rem;
            margin-left: auto;
        }

        .version-commit {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-left: 24px;
            font-style: italic;
            opacity: 0.8;
        }

        /* 版本切换按钮 */
        .version-switch-btn {
            background: rgba(0, 102, 255, 0.8);
            border: 1px solid rgba(0, 102, 255, 0.6);
            border-radius: 6px;
            padding: 8px 16px;
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .version-switch-btn:hover {
            background: rgba(0, 102, 255, 1);
            box-shadow: 0 0 15px rgba(0, 102, 255, 0.5);
            transform: translateY(-1px);
        }

        .version-switch-btn.current {
            background: rgba(0, 255, 102, 0.1);
            border-color: rgba(0, 255, 102, 0.6);
            cursor: default;
            color: #00ff66;
        }

        .version-switch-btn.current:hover {
            background: rgba(0, 255, 102, 0.1);
            transform: none;
            box-shadow: none;
        }

        .version-switch-btn.newer {
            background: #ff6b35;
            border-color: #ff6b35;
            animation: pulse 2s infinite;
        }

        .version-switch-btn.newer:hover {
            background: #ff5722;
            border-color: #ff5722;
        }

        .version-item.newer {
            border-left: 3px solid #ff6b35;
            background: rgba(255, 107, 53, 0.05);
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 53, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 53, 0); }
        }

        /* 模态对话框 */
        .version-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: modalFadeIn 0.3s ease;
        }

        .version-modal.show {
            display: flex;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            background: rgba(26, 26, 46, 0.95);
            border: 1px solid var(--neon-glow);
            border-radius: 12px;
            padding: 25px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            backdrop-filter: blur(20px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.3);
        }

        .modal-content h3 {
            color: var(--neon-glow);
            margin-bottom: 15px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-shadow: 0 0 10px var(--neon-glow);
        }

        .modal-content p {
            color: var(--text-primary);
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .modal-content #target-version,
        .modal-content #progress-version {
            color: var(--neon-glow);
            font-weight: 600;
            text-shadow: 0 0 5px var(--neon-glow);
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--neon-glow), var(--accent-blue));
            border-radius: 4px;
            width: 0%;
            transition: width 0.5s ease;
            box-shadow: 0 0 10px var(--neon-glow);
        }

        .modal-content small {
            color: var(--text-secondary);
            font-size: 0.75rem;
        }

        /* 插件管理页面样式 */
        .plugin-management-container {
            padding: 20px;
            max-width: 100%;
            color: var(--text-primary);
            /* 移除固定高度限制，与版本管理页面保持一致 */
        }

        .plugin-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .plugin-header h2 {
            color: var(--neon-glow);
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
            text-shadow: 0 0 10px var(--neon-glow);
            margin: 0;
        }

        .plugin-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .plugin-tab-btn {
            padding: 10px 20px;
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 8px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .plugin-tab-btn:hover {
            border-color: var(--neon-glow);
            background: rgba(0, 245, 255, 0.1);
        }

        .plugin-tab-btn.active {
            border-color: var(--neon-glow);
            background: rgba(0, 245, 255, 0.15);
            color: var(--neon-glow);
            text-shadow: 0 0 5px var(--neon-glow);
        }

        .plugin-content {
            /* 移除overflow-y: auto，使用页面级别的滚动条 */
        }

        /* 已安装插件样式 */
        .installed-plugins {
            display: none;
        }

        .installed-plugins.active {
            display: block;
        }

        /* 可安装插件样式 */
        .available-plugins {
            display: none;
        }

        .available-plugins.active {
            display: block !important;
        }

        /* 插件工具栏样式 - 简约设计 */
        .plugin-toolbar {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(26, 26, 46, 0.4);
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.15);
            backdrop-filter: blur(15px);
        }

        .search-container {
            flex: 2;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 20px;
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 25px;
            color: var(--text-primary);
            font-size: 15px;
            font-family: 'JetBrains Mono', monospace;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--neon-glow);
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.2);
            background: rgba(0, 0, 0, 0.3);
        }

        .search-input::placeholder {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            right: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 16px;
        }

        .filter-container {
            flex: 1;
            min-width: 180px;
        }

        .category-select {
            width: 100%;
            padding: 12px 20px;
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 25px;
            color: var(--text-primary);
            font-size: 14px;
            font-family: 'JetBrains Mono', monospace;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-select:focus {
            outline: none;
            border-color: var(--neon-glow);
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.2);
            background: rgba(0, 0, 0, 0.3);
        }

        .category-select option {
            background: rgba(26, 26, 46, 0.95);
            color: var(--text-primary);
            padding: 8px;
        }

        /* 插件列表布局 - 紧凑表格式，优化滚动性能 */
        .plugin-list {
            display: block;
            padding: 0;
            margin: 0;
            /* 优化滚动性能 */
            contain: layout style paint;
            transform: translate3d(0, 0, 0);
        }

        /* 可用插件项 - 两行布局设计，优化滚动性能 */
        .available-plugin-item {
            background: rgba(26, 26, 46, 0.2);
            border: 1px solid rgba(0, 245, 255, 0.1);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            /* 启用硬件加速，优化滚动性能 */
            transform: translate3d(0, 0, 0);
            will-change: transform, background-color, border-color;
        }

        .available-plugin-item:hover {
            border-color: rgba(0, 245, 255, 0.3);
            background: rgba(26, 26, 46, 0.4);
            transform: translate3d(4px, 0, 0);
        }

        /* 插件信息区域 */
        .available-plugin-info {
            display: flex;
            width: 100%;
            gap: 20px;
        }

        /* 左侧：插件名称和描述 */
        .plugin-main-info {
            flex: 1;
            min-width: 0;
        }

        /* 插件标题行：名称 + 作者 */
        .plugin-title-line {
            display: flex;
            align-items: baseline;
            gap: 8px;
            margin-bottom: 6px;
        }

        .available-plugin-name {
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            min-width: 0;
        }

        .plugin-author {
            color: #ffa500;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            flex-shrink: 0;
        }

        /* 插件描述 */
        .plugin-description {
            color: var(--text-secondary);
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 底部信息行：分类、星标、主页、安装 */
        .plugin-bottom-line {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
            font-size: 12px;
            margin-top: 8px;
        }

        /* 左侧信息组 */
        .plugin-left-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        /* 右侧按钮组 */
        .plugin-right-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: auto;
        }

        .plugin-category {
            color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
            padding: 3px 8px;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            white-space: nowrap;
            display: inline-block;
            min-width: 80px;
            text-align: center;
        }

        .plugin-stars {
            color: #ffd700;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
            min-width: 60px;
        }

        .plugin-action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            white-space: nowrap;
            min-width: 60px;
        }

        .plugin-action-btn.install {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .plugin-action-btn.install:hover {
            background: linear-gradient(135deg, #218838, #1ea080);
            transform: translateY(-1px);
        }

        .plugin-action-btn.installed {
            background: rgba(108, 117, 125, 0.6);
            color: rgba(255, 255, 255, 0.8);
            cursor: not-allowed;
        }

        .plugin-action-btn.info {
            background: rgba(0, 123, 255, 0.1);
            color: #007bff;
            border: 1px solid rgba(0, 123, 255, 0.3);
        }

        .plugin-action-btn.info:hover {
            background: rgba(0, 123, 255, 0.2);
            border-color: rgba(0, 123, 255, 0.5);
            transform: translateY(-1px);
        }

        /* 加载状态样式 */
        .plugin-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 60px 40px;
            color: var(--text-secondary);
            font-size: 16px;
            gap: 16px;
        }

        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 3px solid rgba(0, 245, 255, 0.2);
            border-top: 3px solid var(--neon-glow);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 环境配置页面样式 */
        .environment-container {
            padding: 20px;
        }

        /* 环境配置子标签导航 */
        .environment-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
            border-bottom: 1px solid rgba(0, 245, 255, 0.2);
            padding-bottom: 16px;
        }

        .environment-tab-btn {
            background: rgba(26, 26, 46, 0.3);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 8px;
            padding: 12px 20px;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .environment-tab-btn:hover {
            border-color: rgba(0, 245, 255, 0.4);
            background: rgba(26, 26, 46, 0.5);
            color: var(--text-primary);
        }

        .environment-tab-btn.active {
            background: rgba(0, 245, 255, 0.1);
            border-color: var(--neon-glow);
            color: var(--neon-glow);
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.2);
        }



        /* 设置页面样式 */
        .settings-container {
            padding: 0;
        }

        /* 卡片式网格布局 */
        .settings-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 24px;
            padding: 0;
        }

        /* 设置卡片 */
        .settings-card {
            background: rgba(0, 245, 255, 0.03);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .settings-card:hover {
            border-color: rgba(0, 245, 255, 0.4);
            box-shadow: 0 8px 32px rgba(0, 245, 255, 0.1);
            transform: translateY(-2px);
        }

        /* 卡片头部 */
        .settings-card-header {
            background: rgba(0, 245, 255, 0.08);
            padding: 16px 24px;
            border-bottom: 1px solid rgba(0, 245, 255, 0.15);
        }

        .settings-card-header h3 {
            color: var(--neon-glow);
            font-size: 1.3rem;
            margin: 0 0 4px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            text-shadow: 0 0 10px var(--neon-glow);
        }

        .settings-card-header p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin: 0;
            line-height: 1.3;
        }

        /* 卡片内容 */
        .settings-card-content {
            padding: 24px;
        }

        /* 设置组 */
        .settings-group {
            margin-bottom: 24px;
        }

        .settings-group:last-child {
            margin-bottom: 0;
        }

        .settings-group h4 {
            color: var(--text-primary);
            font-size: 1rem;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .settings-group h4 i {
            font-size: 14px;
            color: var(--neon-glow);
        }

        /* 设置项容器 */
        .settings-items {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-item label {
            color: var(--text-primary);
            font-weight: 500;
            font-size: 14px;
            flex: 1;
        }

        /* 信息列表 */
        .info-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .info-list .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .info-list .info-item:last-child {
            border-bottom: none;
        }

        /* 设置页面返回按钮 */
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }



        /* 紧凑型设置项 */
        .setting-item.compact {
            padding: 8px 0;
        }

        .upload-btn.compact {
            padding: 6px 12px;
            font-size: 12px;
            min-width: 100px;
        }

        /* 紧凑型主题选择器 */
        .theme-selector.compact {
            gap: 6px;
        }

        .theme-btn.compact {
            padding: 6px 10px;
            font-size: 11px;
        }

        .theme-btn.compact .theme-color {
            width: 10px;
            height: 10px;
        }

        /* 文件上传区域 */
        .file-upload-area {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: flex-end;
        }

        .upload-btn {
            background: rgba(0, 245, 255, 0.1);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 6px;
            padding: 8px 16px;
            color: var(--neon-glow);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .upload-btn:hover {
            background: rgba(0, 245, 255, 0.2);
            border-color: var(--neon-glow);
            transform: translateY(-1px);
        }

        .upload-info {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
        }

        /* 主题选择器 */
        .theme-selector {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .theme-btn {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 8px 12px;
            color: var(--text-primary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .theme-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .theme-btn.active {
            border-color: var(--neon-glow);
            background: rgba(0, 245, 255, 0.1);
            color: var(--neon-glow);
        }

        .theme-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 操作按钮 */
        .action-btn {
            background: rgba(0, 245, 255, 0.1);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 6px;
            padding: 10px 20px;
            color: var(--neon-glow);
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            background: rgba(0, 245, 255, 0.2);
            border-color: var(--neon-glow);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 245, 255, 0.2);
        }

        .action-btn.primary {
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-blue));
            border-color: var(--neon-glow);
            color: #000;
        }

        /* 切换开关 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-label {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            transition: 0.3s;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .toggle-label:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background: white;
            border-radius: 50%;
            transition: 0.3s;
        }

        .toggle-switch input:checked + .toggle-label {
            background: var(--neon-glow);
            border-color: var(--neon-glow);
        }

        .toggle-switch input:checked + .toggle-label:before {
            transform: translateX(26px);
            background: #000;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            gap: 12px;
        }

        .info-grid .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-grid .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 13px;
        }

        .info-value {
            color: var(--neon-glow);
            font-weight: 500;
            font-size: 13px;
        }

        /* 环境标签页内容 */
        .environment-tab-content {
            display: none;
        }

        .environment-tab-content.active {
            display: block;
        }

        /* 环境信息网格 */
        .env-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        /* 环境信息卡片 */
        .env-info-card {
            background: rgba(26, 26, 46, 0.3);
            border: 1px solid rgba(0, 245, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .env-info-card h4 {
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .env-info-card h4 i {
            color: var(--neon-glow);
        }

        /* 环境信息列表 */
        .env-info-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .env-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 245, 255, 0.1);
        }

        .env-info-item:last-child {
            border-bottom: none;
        }

        .env-label {
            color: var(--text-secondary);
            font-size: 13px;
            font-weight: 500;
        }

        .env-value {
            color: var(--text-primary);
            font-size: 13px;
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            word-break: break-all;
        }

        /* 环境操作按钮 */
        .env-actions {
            display: flex;
            justify-content: center;
            gap: 16px;
        }

        .env-refresh-btn {
            background: linear-gradient(135deg, var(--neon-glow), #00ff88);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            color: #000;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .env-refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 245, 255, 0.3);
        }

        /* 终端容器样式 */
        .terminal-container {
            background: rgba(26, 26, 46, 0.3);
            border: 1px solid rgba(0, 245, 255, 0.1);
            border-radius: 12px;
            overflow: hidden;
            height: 600px;
            display: flex;
            flex-direction: column;
        }

        .terminal-header {
            background: rgba(0, 0, 0, 0.3);
            padding: 12px 16px;
            border-bottom: 1px solid rgba(0, 245, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .terminal-header h4 {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .terminal-controls {
            display: flex;
            gap: 8px;
        }

        .terminal-btn {
            background: rgba(0, 245, 255, 0.1);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 6px;
            padding: 6px 12px;
            color: var(--neon-glow);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .terminal-btn:hover {
            background: rgba(0, 245, 255, 0.2);
            border-color: var(--neon-glow);
        }

        .terminal-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #0a0a0a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .terminal-welcome {
            padding: 16px;
            border-bottom: 1px solid rgba(0, 255, 0, 0.2);
            color: #00ff88;
        }

        .terminal-welcome p {
            margin: 4px 0;
        }

        .terminal-welcome ul {
            margin: 8px 0 0 20px;
            padding: 0;
        }

        .terminal-welcome li {
            margin: 4px 0;
        }

        .terminal-welcome code {
            background: rgba(0, 255, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            color: #00ff00;
        }

        .terminal-output {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            scroll-behavior: smooth;
        }

        .terminal-output::-webkit-scrollbar {
            width: 8px;
        }

        .terminal-output::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }

        .terminal-output::-webkit-scrollbar-thumb {
            background: rgba(0, 255, 0, 0.3);
            border-radius: 4px;
        }

        .terminal-output::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 255, 0, 0.5);
        }

        .terminal-input-line {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-top: 1px solid rgba(0, 255, 0, 0.2);
            background: rgba(0, 0, 0, 0.5);
        }

        .terminal-prompt {
            color: #00ff88;
            font-weight: bold;
            margin-right: 8px;
        }

        .terminal-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            outline: none;
        }

        .terminal-input::placeholder {
            color: rgba(0, 255, 0, 0.5);
        }

        .plugin-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: rgba(26, 26, 46, 0.4);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 10px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }

        .plugin-item:hover {
            border-color: rgba(0, 245, 255, 0.4);
            background: rgba(26, 26, 46, 0.6);
        }

        .plugin-item.disabled {
            opacity: 0.6;
        }

        .plugin-item.disabled .plugin-name {
            text-decoration: line-through;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            color: var(--accent-cyan);
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .empty-state p {
            font-size: 1rem;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .empty-state .btn {
            padding: 12px 24px;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-blue));
            border: 2px solid var(--neon-glow);
            border-radius: 8px;
            color: #000;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .empty-state .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.4);
        }

        .plugin-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .plugin-name {
            color: var(--neon-glow);
            font-size: 1rem;
            font-weight: 600;
            text-shadow: 0 0 5px var(--neon-glow);
            min-width: 200px;
        }

        .plugin-version {
            color: var(--text-secondary);
            font-size: 0.85rem;
            min-width: 80px;
        }

        .plugin-date {
            color: var(--text-secondary);
            font-size: 0.8rem;
            min-width: 100px;
        }

        .plugin-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .plugin-btn {
            padding: 6px 12px;
            border: 1px solid;
            border-radius: 6px;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 5px;
            text-decoration: none;
        }

        .plugin-btn-link {
            border-color: rgba(0, 150, 255, 0.5);
            color: #0096ff;
        }

        .plugin-btn-link:hover {
            background: rgba(0, 150, 255, 0.1);
            border-color: #0096ff;
        }

        .plugin-btn-update {
            border-color: rgba(0, 200, 100, 0.5);
            color: #00c864;
        }

        .plugin-btn-update:hover {
            background: rgba(0, 200, 100, 0.1);
            border-color: #00c864;
        }

        .plugin-btn-switch {
            border-color: rgba(255, 165, 0, 0.5);
            color: #ffa500;
        }

        .plugin-btn-switch:hover {
            background: rgba(255, 165, 0, 0.1);
            border-color: #ffa500;
        }

        .plugin-btn-toggle {
            border-color: rgba(0, 245, 255, 0.5);
            color: var(--neon-glow);
        }

        .plugin-btn-toggle:hover {
            background: rgba(0, 245, 255, 0.1);
            border-color: var(--neon-glow);
        }

        .plugin-btn-toggle.disabled {
            border-color: rgba(255, 100, 100, 0.5);
            color: #ff6464;
        }

        .plugin-btn-toggle.disabled:hover {
            background: rgba(255, 100, 100, 0.1);
            border-color: #ff6464;
        }

        .plugin-btn-uninstall {
            border-color: rgba(255, 100, 100, 0.5);
            color: #ff6464;
        }

        .plugin-btn-uninstall:hover {
            background: rgba(255, 100, 100, 0.1);
            border-color: #ff6464;
        }

        /* 新插件安装样式 */
        .install-plugins {
            display: none;
        }

        .install-plugins.active {
            display: block;
        }

        .plugin-search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .plugin-search-input {
            flex: 1;
            padding: 10px 15px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .plugin-search-input:focus {
            outline: none;
            border-color: var(--neon-glow);
            box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
        }

        .plugin-category-select {
            padding: 10px 15px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 0.9rem;
            min-width: 150px;
        }

        .available-plugin-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: rgba(26, 26, 46, 0.4);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 10px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }

        .available-plugin-item:hover {
            border-color: rgba(0, 245, 255, 0.4);
            background: rgba(26, 26, 46, 0.6);
        }

        .available-plugin-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .available-plugin-name {
            color: var(--neon-glow);
            font-size: 1rem;
            font-weight: 600;
            text-shadow: 0 0 5px var(--neon-glow);
            min-width: 250px;
        }

        .plugin-author {
            color: var(--text-secondary);
            font-size: 0.85rem;
            min-width: 120px;
        }

        .plugin-stars {
            color: #ffa500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 5px;
            min-width: 80px;
        }

        .plugin-btn-install {
            border-color: rgba(0, 200, 100, 0.5);
            color: #00c864;
        }

        .plugin-btn-install:hover {
            background: rgba(0, 200, 100, 0.1);
            border-color: #00c864;
        }

        /* 版本切换模态框 */
        .version-switch-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 15000;
            animation: modalFadeIn 0.3s ease;
        }

        .version-switch-modal.show {
            display: flex;
        }

        .version-switch-content {
            background: rgba(26, 26, 46, 0.95);
            border: 1px solid var(--neon-glow);
            border-radius: 12px;
            padding: 25px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            backdrop-filter: blur(20px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.3);
        }

        .version-switch-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .version-switch-header h3 {
            color: var(--neon-glow);
            margin: 0;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 10px;
            text-shadow: 0 0 10px var(--neon-glow);
        }

        .version-close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .version-close-btn:hover {
            color: var(--text-primary);
        }

        .version-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .version-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .version-item:hover {
            border-color: rgba(0, 245, 255, 0.4);
            background: rgba(0, 0, 0, 0.5);
        }

        .version-item.current {
            border-color: rgba(0, 200, 100, 0.6);
            background: rgba(0, 200, 100, 0.03);
        }

        .version-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .version-number {
            color: var(--neon-glow);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .version-date {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .version-switch-btn {
            padding: 6px 15px;
            border: 1px solid rgba(0, 245, 255, 0.5);
            border-radius: 6px;
            background: transparent;
            color: var(--neon-glow);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .version-switch-btn:hover {
            background: rgba(0, 245, 255, 0.1);
            border-color: var(--neon-glow);
        }

        .version-switch-btn.current {
            border-color: rgba(0, 200, 100, 0.6);
            color: #00c864;
            cursor: default;
        }

        .version-switch-btn.current:hover {
            background: transparent;
        }

        /* 插件管理加载状态 */
        .plugin-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 40px;
            color: var(--text-secondary);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 245, 255, 0.2);
            border-top: 3px solid var(--neon-glow);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .plugin-list {
            display: none;
        }

        .plugin-list.show {
            display: block;
        }

        /* 插件版本切换模态框样式 */
        .version-switch-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 15000;
            animation: modalFadeIn 0.3s ease;
        }

        .version-switch-modal.show {
            display: flex;
        }

        .version-switch-content {
            background: rgba(26, 26, 46, 0.95);
            border: 1px solid var(--neon-glow);
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            backdrop-filter: blur(20px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .version-switch-header {
            padding: 20px 25px;
            border-bottom: 1px solid rgba(0, 245, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .version-switch-header h3 {
            color: var(--neon-glow);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.1rem;
            text-shadow: 0 0 10px var(--neon-glow);
        }

        .close-btn {
            background: transparent;
            border: 1px solid rgba(255, 0, 0, 0.5);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            color: #ff4757;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: rgba(255, 0, 0, 0.1);
            border-color: #ff4757;
            transform: scale(1.1);
        }

        .version-switch-list {
            max-height: 50vh;
            overflow-y: auto;
            padding: 15px 25px 25px;
        }

        .version-switch-list .version-item {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .version-switch-list .version-item:hover {
            border-color: rgba(0, 245, 255, 0.4);
            background: rgba(26, 26, 46, 0.8);
        }

        .version-switch-list .version-item.current {
            border-color: var(--neon-glow);
            background: rgba(0, 245, 255, 0.03);
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
        }

        /* 自定义确认对话框样式 */
        .custom-confirm-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .custom-confirm-content {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 12px;
            min-width: 400px;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            animation: confirmFadeIn 0.3s ease-out;
        }

        @keyframes confirmFadeIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .custom-confirm-header {
            padding: 20px 20px 0 20px;
            border-bottom: 1px solid #444;
            margin-bottom: 20px;
        }

        .custom-confirm-header h3 {
            margin: 0;
            color: #fff;
            font-size: 18px;
            font-weight: 600;
        }

        .custom-confirm-body {
            padding: 0 20px 20px 20px;
            color: #ccc;
            line-height: 1.6;
        }

        .custom-confirm-body p {
            margin: 0;
            font-size: 14px;
        }

        .custom-confirm-footer {
            padding: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            border-top: 1px solid #444;
        }

        .custom-confirm-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .custom-confirm-btn.cancel {
            background: #444;
            color: #ccc;
        }

        .custom-confirm-btn.cancel:hover {
            background: #555;
            color: #fff;
        }

        .custom-confirm-btn.confirm {
            background: #00ff88;
            color: #000;
        }

        .custom-confirm-btn.confirm:hover {
            background: #00e67a;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <!-- 背景图片 -->
        <div class="background-container">
            <img id="backgroundImage" class="background-image" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxyYWRpYWxHcmFkaWVudCBpZD0iZ3JhZGllbnQiIGN4PSI1MCUiIGN5PSI1MCUiIHI9IjUwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiMwZjRjNzUiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0b3AtY29sb3I9IiMxNjIxM2UiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMGEwYTBmIi8+CiAgICA8L3JhZGlhbEdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8L3N2Zz4=" alt="Default Background">
            <video id="backgroundVideo" class="background-video" autoplay muted loop>
                <source src="" type="video/mp4">
                <source src="" type="video/webm">
            </video>
            <div class="background-overlay"></div>
        </div>



        <!-- 内容层 -->
        <div class="content-layer">
            <!-- 顶部标题 -->
            <div class="header-section">
                <h1 class="app-title">AI视界</h1>
                <p class="app-subtitle">Create New Visual Horizons with AI</p>
            </div>

            <!-- 功能导航标签 -->
            <nav class="navigation-tabs">
                <button class="tab-btn active" data-tab="home">主页</button>
                <button class="tab-btn" data-tab="launch-settings">启动设置</button>
                <button class="tab-btn" data-tab="version-management">版本管理</button>
                <button class="tab-btn" data-tab="plugin-management">插件管理</button>
                <button class="tab-btn" data-tab="environment-config">环境配置</button>
                <button class="tab-btn" data-tab="settings">设置</button>
            </nav>

            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 主页面内容 -->
                <div class="main-home" id="mainHome">
                    <!-- 底部启动区域 -->
                    <div class="launch-section">
                        <button class="launch-btn" id="launchBtn">
                            <i class="fas fa-rocket"></i>
                            启动 COMFYUI
                        </button>

                        <!-- 打开浏览器按钮 -->
                        <button class="browser-btn" id="browserBtn" style="display: none;">
                            <i class="fas fa-external-link-alt"></i>
                            打开浏览器
                        </button>

                        <!-- 显卡信息 -->
                        <div class="gpu-info" id="gpuInfo">
                            <div class="gpu-details">
                                <div class="gpu-icon-container">
                                    <i class="fas fa-microchip gpu-icon"></i>
                                    <div class="rotating-ring"></div>
                                </div>
                                <div class="gpu-text">
                                    <span class="gpu-name" id="gpuName">检测中...</span>
                                    <span class="gpu-usage" id="gpuUsage">显存: 0GB / 16GB</span>
                                </div>
                            </div>
                            <div class="gpu-status" id="gpuStatus">45%</div>
                        </div>
                    </div>
                </div>

                <!-- 启动设置页面 -->
                <div id="launch-settings" class="tab-content">
                <div class="launch-settings-container">
                    <!-- 快速配置预设 -->
                    <section class="preset-section">
                        <h3>快速配置预设</h3>
                        <div class="preset-cards">
                            <div class="preset-card" data-preset="high-end">
                                <h4>高端显卡 (16GB+)</h4>
                                <p>--highvram --use-flash-attention --bf16-unet</p>
                            </div>
                            <div class="preset-card" data-preset="mid-range">
                                <h4>中端显卡 (8-16GB)</h4>
                                <p>--normalvram --use-pytorch-cross-attention</p>
                            </div>
                            <div class="preset-card" data-preset="low-end">
                                <h4>低端显卡 (4-8GB)</h4>
                                <p>--lowvram --use-split-cross-attention</p>
                            </div>
                        </div>
                    </section>

                    <!-- 显存优化 -->
                    <section class="memory-section">
                        <h3><i class="fas fa-memory"></i> 显存优化</h3>
                        <div class="memory-mode-selector">
                            <div class="radio-group">
                                <label class="radio-item">
                                    <input type="radio" name="vram-mode" value="gpu-only" id="gpu-only">
                                    <span>GPU专用模式</span>
                                    <small>所有模型保持在GPU (需大显存)</small>
                                </label>
                                
                                <label class="radio-item">
                                    <input type="radio" name="vram-mode" value="highvram" id="highvram" checked>
                                    <span>高显存模式</span>
                                    <small>模型保持在GPU内存中 (推荐)</small>
                                </label>
                                
                                <label class="radio-item">
                                    <input type="radio" name="vram-mode" value="normalvram" id="normalvram">
                                    <span>普通模式</span>
                                    <small>平衡显存使用</small>
                                </label>
                                
                                <label class="radio-item">
                                    <input type="radio" name="vram-mode" value="lowvram" id="lowvram">
                                    <span>低显存模式</span>
                                    <small>拆分UNet减少显存使用</small>
                                </label>
                                
                                <label class="radio-item">
                                    <input type="radio" name="vram-mode" value="cpu" id="cpu">
                                    <span>CPU模式</span>
                                    <small>所有计算在CPU (显存不足时)</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="advanced-memory">
                            <div class="setting-item horizontal">
                                <label>预留显存 (GB)</label>
                                <div class="input-with-hint">
                                    <input type="number" id="reserve-vram" step="0.5" min="0" max="16" value="0">
                                    <small class="hint">为系统预留的显存大小</small>
                                </div>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="async-offload">
                                <label for="async-offload">异步权重卸载</label>
                                <small class="hint">提升大模型加载速度</small>
                            </div>
                        </div>
                    </section>

                    <!-- 交叉注意力优化 -->
                    <section class="attention-section">
                        <h3><i class="fas fa-brain"></i> 交叉注意力优化</h3>
                        <div class="attention-selector">
                            <div class="setting-item horizontal">
                                <label>注意力优化方法</label>
                                <select id="attention-method">
                                    <option value="">默认</option>
                                    <option value="use-flash-attention">FlashAttention (推荐 - 高端显卡)</option>
                                    <option value="use-pytorch-cross-attention">PyTorch 2.0 (稳定性好)</option>
                                    <option value="use-sage-attention">Sage Attention (新技术)</option>
                                    <option value="use-split-cross-attention">分割注意力 (省显存)</option>
                                    <option value="use-quad-cross-attention">二次注意力 (兼容性好)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="attention-options">
                            <div class="setting-item horizontal">
                                <label>注意力上采样</label>
                                <select id="upcast-mode">
                                    <option value="">自动</option>
                                    <option value="force-upcast-attention">强制启用</option>
                                    <option value="dont-upcast-attention">禁用</option>
                                </select>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="disable-xformers">
                                <label for="disable-xformers">禁用 xformers</label>
                                <small class="hint">解决某些兼容性问题</small>
                            </div>
                        </div>
                    </section>

                    <!-- 监听设置 -->
                    <section class="network-section">
                        <h3><i class="fas fa-network-wired"></i> 监听设置</h3>
                        <div class="setting-grid">
                            <div class="setting-item horizontal">
                                <label>监听地址</label>
                                <div class="input-with-hint">
                                    <select id="listen-ip">
                                        <option value="127.0.0.1">127.0.0.1 (仅本机访问)</option>
                                        <option value="0.0.0.0">0.0.0.0 (允许外部访问)</option>
                                    </select>
                                    <small class="hint">选择0.0.0.0可通过局域网访问</small>
                                </div>
                            </div>
                            <div class="setting-item horizontal">
                                <label>端口</label>
                                <div class="input-with-hint">
                                    <input type="number" id="port" value="8188" min="1" max="65535">
                                    <small class="hint">Web界面访问端口</small>
                                </div>
                            </div>
                            <div class="setting-item horizontal">
                                <label>CORS设置</label>
                                <div class="input-with-hint">
                                    <select id="cors-setting">
                                        <option value="">禁用</option>
                                        <option value="enable-cors-header">启用跨域支持</option>
                                    </select>
                                    <small class="hint">API调用时需要启用</small>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 代理设置 -->
                    <section class="proxy-section">
                        <h3><i class="fas fa-globe"></i> 代理设置</h3>
                        <div class="proxy-settings">
                            <div class="checkbox-item">
                                <input type="checkbox" id="enable-proxy">
                                <label for="enable-proxy">启用代理</label>
                                <small class="hint">用于模型下载和API访问</small>
                            </div>
                            <div class="proxy-inputs" id="proxy-inputs" style="display: none;">
                                <div class="setting-item horizontal">
                                    <label>HTTP代理</label>
                                    <input type="text" id="http-proxy" placeholder="http://proxy.example.com:8080">
                                </div>
                                <div class="setting-item horizontal">
                                    <label>HTTPS代理</label>
                                    <input type="text" id="https-proxy" placeholder="https://proxy.example.com:8080">
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 命令预览 -->
                    <section class="preview-section">
                        <h3><i class="fas fa-terminal"></i> 启动命令预览</h3>
                        <div class="command-preview" id="command-preview">
                            python main.py --listen 127.0.0.1 --port 8188 --highvram
                        </div>
                    </section>

                    <!-- 操作按钮 -->
                    <section class="actions-section">
                        <div class="button-group">
                            <button class="btn btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save"></i> 保存配置
                            </button>
                            <button class="btn btn-success" onclick="applyAndLaunch()">
                                <i class="fas fa-rocket"></i> 应用并启动
                            </button>
                            <button class="btn btn-warning" onclick="resetToDefault()">
                                <i class="fas fa-undo"></i> 重置默认
                            </button>
                        </div>
                    </section>
                </div>
                </div>

                <!-- 版本管理页面 -->
                <div id="version-management" class="tab-content">
                <div class="version-management-container">
                    <!-- 页面头部 -->
                    <div class="version-header">
                        <h2><i class="fas fa-code-branch"></i> 版本管理</h2>
                    </div>

                    <!-- 版本类型切换 -->
                    <div class="version-tabs">
                        <button class="version-tab-btn active" data-type="stable">
                            <i class="fas fa-shield-alt"></i> 稳定版
                        </button>
                        <button class="version-tab-btn" data-type="development">
                            <i class="fas fa-flask"></i> 开发版
                        </button>
                    </div>

                    <!-- 版本列表容器 -->
                    <div class="version-list-container">
                        <!-- 加载状态 -->
                        <div class="version-loading" id="version-loading">
                            <div class="loading-spinner"></div>
                            <span>正在加载版本信息...</span>
                        </div>

                        <!-- 稳定版列表 -->
                        <div class="version-list" id="stable-versions">
                            <!-- 动态生成内容 -->
                        </div>

                        <!-- 开发版列表 -->
                        <div class="version-list" id="development-versions" style="display: none;">
                            <!-- 动态生成内容 -->
                        </div>
                    </div>
                </div>

                <!-- 版本切换确认对话框 -->
                <div class="version-modal" id="version-confirm-modal">
                    <div class="modal-content">
                        <h3><i class="fas fa-exclamation-triangle"></i> 确认版本切换</h3>
                        <p id="modal-message">确定要切换到版本 <span id="target-version"></span> 吗？</p>
                        <div class="modal-actions">
                            <button class="btn btn-warning" onclick="if(window.audioManager) audioManager.play('click'); cancelVersionSwitch()">取消</button>
                            <button class="btn btn-primary" onclick="if(window.audioManager) audioManager.play('click'); confirmVersionSwitch()">确认切换</button>
                        </div>
                    </div>
                </div>

                <!-- 切换进度对话框 -->
                <div class="version-modal" id="version-progress-modal">
                    <div class="modal-content">
                        <h3><i class="fas fa-sync-alt fa-spin"></i> 正在切换版本</h3>
                        <p id="progress-message">正在切换到版本 <span id="progress-version"></span>...</p>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <small>请稍候，不要关闭页面</small>
                    </div>
                </div>
                </div>

                <!-- 插件管理页面 -->
                <div id="plugin-management" class="tab-content">
                    <div class="plugin-management-container">
                        <!-- 页面头部 -->
                        <div class="plugin-header">
                            <h2><i class="fas fa-puzzle-piece"></i> 插件管理</h2>
                        </div>

                        <!-- 插件类型切换 -->
                        <div class="plugin-tabs">
                            <button class="plugin-tab-btn active" data-type="installed">
                                <i class="fas fa-check-circle"></i> 已安装插件
                            </button>
                            <button class="plugin-tab-btn" data-type="available">
                                <i class="fas fa-download"></i> 安装新插件
                            </button>
                        </div>

                        <!-- 插件内容容器 -->
                        <div class="plugin-content">
                            <!-- 已安装插件列表 -->
                            <div class="installed-plugins active" id="installed-plugins">
                                <div class="plugin-loading" id="installed-loading" style="display: flex;">
                                    <div class="loading-spinner"></div>
                                    <span>正在加载已安装插件...</span>
                                </div>

                                <div class="plugin-list" id="installed-plugin-list" style="display: none;">
                                    <!-- 插件列表将在这里动态生成 -->
                                </div>
                            </div>

                            <!-- 可安装插件列表 -->
                            <div class="available-plugins" id="available-plugins" style="display: none;">
                                <!-- 搜索和过滤工具栏 -->
                                <div class="plugin-toolbar">
                                    <div class="search-container">
                                        <input type="text" id="plugin-search" placeholder="搜索插件名称、作者或描述..." class="search-input">
                                        <i class="fas fa-search search-icon"></i>
                                    </div>
                                    <div class="filter-container">
                                        <select id="category-filter" class="category-select">
                                            <option value="">所有分类</option>
                                            <option value="image">图像处理</option>
                                            <option value="video">视频处理</option>
                                            <option value="audio">音频处理</option>
                                            <option value="ai">AI模型</option>
                                            <option value="3d">3D相关</option>
                                            <option value="tool">工具类</option>
                                            <option value="other">其他</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="plugin-loading" id="available-loading" style="display: none;">
                                    <div class="loading-spinner"></div>
                                    <span>正在加载可安装插件...</span>
                                </div>

                                <div class="plugin-list" id="available-plugin-list">
                                    <!-- 可安装插件列表将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 环境配置页面 -->
                <div id="environment-config" class="tab-content">
                    <div class="environment-container">
                        <!-- 环境配置子标签导航 -->
                        <div class="environment-tabs">
                            <button class="environment-tab-btn active" onclick="switchEnvironmentTab('env-info')">
                                <i class="fas fa-info-circle"></i>
                                环境信息
                            </button>
                            <button class="environment-tab-btn" onclick="switchEnvironmentTab('env-terminal')">
                                <i class="fas fa-terminal"></i>
                                项目终端
                            </button>
                        </div>

                        <!-- 环境信息标签页 -->
                        <div id="env-info" class="environment-tab-content active">
                            <div class="env-info-grid">
                                <!-- Python环境信息 -->
                                <div class="env-info-card">
                                    <h4><i class="fab fa-python"></i> Python环境</h4>
                                    <div class="env-info-list">
                                        <div class="env-info-item">
                                            <span class="env-label">Python版本:</span>
                                            <span class="env-value" id="python-version">检测中...</span>
                                        </div>
                                        <div class="env-info-item">
                                            <span class="env-label">Python路径:</span>
                                            <span class="env-value" id="python-path">检测中...</span>
                                        </div>
                                        <div class="env-info-item">
                                            <span class="env-label">虚拟环境:</span>
                                            <span class="env-value" id="venv-status">检测中...</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- CUDA环境信息 -->
                                <div class="env-info-card">
                                    <h4><i class="fas fa-microchip"></i> CUDA环境</h4>
                                    <div class="env-info-list">
                                        <div class="env-info-item">
                                            <span class="env-label">CUDA版本:</span>
                                            <span class="env-value" id="cuda-version">检测中...</span>
                                        </div>
                                        <div class="env-info-item">
                                            <span class="env-label">GPU设备:</span>
                                            <span class="env-value" id="gpu-info">检测中...</span>
                                        </div>
                                        <div class="env-info-item">
                                            <span class="env-label">显存信息:</span>
                                            <span class="env-value" id="gpu-memory">检测中...</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- PyTorch环境信息 -->
                                <div class="env-info-card">
                                    <h4><i class="fas fa-fire"></i> PyTorch环境</h4>
                                    <div class="env-info-list">
                                        <div class="env-info-item">
                                            <span class="env-label">PyTorch版本:</span>
                                            <span class="env-value" id="pytorch-version">检测中...</span>
                                        </div>
                                        <div class="env-info-item">
                                            <span class="env-label">CUDA支持:</span>
                                            <span class="env-value" id="pytorch-cuda">检测中...</span>
                                        </div>
                                        <div class="env-info-item">
                                            <span class="env-label">设备状态:</span>
                                            <span class="env-value" id="pytorch-device">检测中...</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 依赖状态信息 -->
                                <div class="env-info-card">
                                    <h4><i class="fas fa-puzzle-piece"></i> 依赖状态</h4>
                                    <div class="env-info-list">
                                        <div class="env-info-item">
                                            <span class="env-label">核心依赖:</span>
                                            <span class="env-value" id="core-deps">检测中...</span>
                                        </div>
                                        <div class="env-info-item">
                                            <span class="env-label">可选依赖:</span>
                                            <span class="env-value" id="optional-deps">检测中...</span>
                                        </div>
                                        <div class="env-info-item">
                                            <span class="env-label">环境状态:</span>
                                            <span class="env-value" id="env-status">检测中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 刷新按钮 -->
                            <div class="env-actions">
                                <button class="env-refresh-btn" onclick="refreshEnvironmentInfo()">
                                    <i class="fas fa-sync-alt"></i>
                                    刷新环境信息
                                </button>
                            </div>
                        </div>

                        <!-- 项目终端标签页 -->
                        <div id="env-terminal" class="environment-tab-content">
                            <div class="terminal-container">
                                <div class="terminal-header">
                                    <h4><i class="fas fa-terminal"></i> ComfyUI项目终端</h4>
                                    <div class="terminal-controls">
                                        <button class="terminal-btn" onclick="clearTerminal()">
                                            <i class="fas fa-trash"></i>
                                            清空
                                        </button>
                                        <button class="terminal-btn" onclick="openNewTerminal()">
                                            <i class="fas fa-plus"></i>
                                            新建终端
                                        </button>
                                    </div>
                                </div>
                                <div class="terminal-content" id="project-terminal">
                                    <div class="terminal-welcome">
                                        <p><i class="fas fa-info-circle"></i> ComfyUI项目虚拟环境终端</p>
                                        <p>当前工作目录: <span id="terminal-cwd">D:\AI\ComfyUI-AI-Vision</span></p>
                                        <p>虚拟环境: <span id="terminal-venv">venv (conda)</span></p>
                                        <hr>
                                        <p>可用命令示例:</p>
                                        <ul>
                                            <li><code>pip list</code> - 查看已安装包</li>
                                            <li><code>pip install package_name</code> - 安装新包</li>
                                            <li><code>python main.py</code> - 运行ComfyUI</li>
                                            <li><code>git status</code> - 查看Git状态</li>
                                        </ul>
                                    </div>
                                    <div class="terminal-output" id="terminal-output">
                                        <!-- 终端输出将在这里显示 -->
                                    </div>
                                    <div class="terminal-input-line">
                                        <span class="terminal-prompt">venv> </span>
                                        <input type="text" class="terminal-input" id="terminal-input" placeholder="输入命令..." onkeypress="handleTerminalInput(event)">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设置页面 -->
                <div id="settings" class="tab-content">
                    <div class="content-header">
                        <h2><i class="fas fa-cog"></i> 设置</h2>
                    </div>

                    <div class="settings-container">
                        <!-- 卡片式网格布局 -->
                        <div class="settings-cards-grid">
                            <!-- 界面个性化卡片 -->
                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h3><i class="fas fa-palette"></i> 界面个性化</h3>
                                    <p>自定义启动器的外观和主题</p>
                                </div>

                                <div class="settings-card-content">
                                    <!-- 背景设置 -->
                                    <div class="settings-group">
                                        <h4><i class="fas fa-image"></i> 背景设置</h4>
                                        <div class="settings-items">
                                            <div class="setting-item compact">
                                                <label>背景图片</label>
                                                <button class="upload-btn compact" onclick="document.getElementById('backgroundImageUpload').click()">
                                                    <i class="fas fa-upload"></i> 选择图片
                                                </button>
                                                <input type="file" id="backgroundImageUpload" accept="image/*" style="display: none;">
                                            </div>

                                            <div class="setting-item compact">
                                                <label>背景视频</label>
                                                <button class="upload-btn compact" onclick="document.getElementById('backgroundVideoUpload').click()">
                                                    <i class="fas fa-video"></i> 选择视频
                                                </button>
                                                <input type="file" id="backgroundVideoUpload" accept="video/*" style="display: none;">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 主题设置 -->
                                    <div class="settings-group">
                                        <h4><i class="fas fa-paint-brush"></i> 主题设置</h4>
                                        <div class="settings-items">
                                            <div class="setting-item compact">
                                                <label>霓虹色彩主题</label>
                                                <div class="theme-selector compact">
                                                    <button class="theme-btn active compact" data-theme="blue" onclick="switchTheme('blue')">
                                                        <span class="theme-color" style="background: #00f5ff;"></span>
                                                        蓝色
                                                    </button>
                                                    <button class="theme-btn compact" data-theme="green" onclick="switchTheme('green')">
                                                        <span class="theme-color" style="background: #00ff88;"></span>
                                                        绿色
                                                    </button>
                                                    <button class="theme-btn compact" data-theme="purple" onclick="switchTheme('purple')">
                                                        <span class="theme-color" style="background: #8a2be2;"></span>
                                                        紫色
                                                    </button>
                                                    <button class="theme-btn compact" data-theme="red" onclick="switchTheme('red')">
                                                        <span class="theme-color" style="background: #ff4757;"></span>
                                                        红色
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 启动器管理卡片 -->
                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h3><i class="fas fa-rocket"></i> 启动器管理</h3>
                                    <p>管理启动器的更新和版本信息</p>
                                </div>

                                <div class="settings-card-content">
                                    <!-- 更新设置 -->
                                    <div class="settings-group">
                                        <h4><i class="fas fa-sync-alt"></i> 更新设置</h4>
                                        <div class="settings-items">
                                            <div class="setting-item">
                                                <label>检查更新</label>
                                                <button class="action-btn primary">
                                                    <i class="fas fa-search"></i> 检查更新
                                                </button>
                                            </div>

                                            <div class="setting-item">
                                                <label>自动更新</label>
                                                <div class="toggle-switch">
                                                    <input type="checkbox" id="autoUpdate" checked>
                                                    <label for="autoUpdate" class="toggle-label"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 版本信息 -->
                                    <div class="settings-group">
                                        <h4><i class="fas fa-info-circle"></i> 版本信息</h4>
                                        <div class="info-list">
                                            <div class="info-item">
                                                <span class="info-label">当前版本</span>
                                                <span class="info-value">v1.0.0</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">最后更新</span>
                                                <span class="info-value">2025-01-06</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 系统设置卡片 -->
                            <div class="settings-card">
                                <div class="settings-card-header">
                                    <h3><i class="fas fa-cogs"></i> 系统设置</h3>
                                    <p>配置系统启动和运行选项</p>
                                </div>

                                <div class="settings-card-content">
                                    <!-- 启动设置 -->
                                    <div class="settings-group">
                                        <h4><i class="fas fa-power-off"></i> 启动设置</h4>
                                        <div class="settings-items">
                                            <div class="setting-item">
                                                <label>开机自启动</label>
                                                <div class="toggle-switch">
                                                    <input type="checkbox" id="autoStart">
                                                    <label for="autoStart" class="toggle-label"></label>
                                                </div>
                                            </div>

                                            <div class="setting-item">
                                                <label>最小化到托盘</label>
                                                <div class="toggle-switch">
                                                    <input type="checkbox" id="minimizeToTray" checked>
                                                    <label for="minimizeToTray" class="toggle-label"></label>
                                                </div>
                                            </div>

                                            <div class="setting-item">
                                                <label>启动时检查状态</label>
                                                <div class="toggle-switch">
                                                    <input type="checkbox" id="checkComfyUIOnStart" checked>
                                                    <label for="checkComfyUIOnStart" class="toggle-label"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 快捷方式设置 -->
                                    <div class="settings-group">
                                        <h4><i class="fas fa-desktop"></i> 快捷方式</h4>
                                        <div class="settings-items">
                                            <div class="setting-item">
                                                <label>创建桌面快捷方式</label>
                                                <button class="action-btn primary" onclick="createDesktopShortcut(event)">
                                                    <i class="fas fa-plus"></i> 创建快捷方式
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 声音设置 -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-volume-up"></i> 声音设置</h3>
                        </div>
                        <div class="settings-card-content">
                            <div class="settings-group">
                                <div class="settings-items">
                                    <div class="setting-item">
                                        <label>启用背景视频声音</label>
                                        <input type="checkbox" id="video-audio-enabled">
                                    </div>
                                    <div class="setting-item">
                                        <label>视频音量</label>
                                        <input type="range" id="video-volume" min="0" max="100" value="30" style="width: 150px;">
                                        <span id="video-volume-display">30%</span>
                                    </div>
                                    <div class="setting-item">
                                        <label>启用音效</label>
                                        <input type="checkbox" id="audio-enabled" checked>
                                    </div>
                                    <div class="setting-item">
                                        <label>音效音量</label>
                                        <input type="range" id="audio-volume" min="0" max="100" value="50" style="width: 150px;">
                                        <span id="volume-display">50%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 插件版本切换对话框 - 放在body顶层 -->
    <div class="version-switch-modal" id="version-switch-modal" onclick="closeVersionSwitchModal()">
        <div class="version-switch-content" onclick="event.stopPropagation()">
            <div class="version-switch-header">
                <h3><i class="fas fa-code-branch"></i> 选择插件版本</h3>
                <button class="close-btn" onclick="closeVersionSwitchModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="version-switch-list" id="version-switch-list">
                <!-- 版本列表将由JavaScript动态生成 -->
            </div>
        </div>
    </div>



    <script>
        class AIVisionLauncher {
            constructor() {
                console.log('AIVisionLauncher constructor called');
                this.currentTab = 'home';
                this.isRunning = false;
                this.API_BASE = null; // 将通过electronAPI动态获取
                this.backendUrl = null; // 将通过electronAPI动态获取

                // 工作流状态监控
                this.isWorkflowRunning = false;
                this.workflowCheckInterval = null;
                this.comfyUIWebSocket = null;

                this.initEventListeners();
                this.loadSavedBackground();
                this.loadSavedSettings();

                // 初始化数据加载
                this.initializeData();

                // 验证插件管理方法
                console.log('showPluginVersions method defined:', typeof this.showPluginVersions);
                console.log('Constructor complete');
            }

            async initializeData() {
                console.log('Starting data initialization...');
                try {
                    // 首先获取后端URL
                    await this.initializeBackendUrl();
                    
                    await this.loadSystemInfo();
                    await this.loadComfyUIStatus();
                    await this.loadGitStatus();
                    await this.loadRealTimeGPUInfo(); // 初始加载GPU信息

                    // 预加载环境信息，避免用户切换到环境页面时需要等待
                    setTimeout(() => {
                        this.detectEnvironmentInfo();
                        console.log('Environment info pre-loaded');
                    }, 1000);

                    // 预加载插件信息，避免用户切换到插件页面时需要等待
                    setTimeout(() => {
                        this.preloadPluginInfo();
                        console.log('Plugin info pre-loading started');
                    }, 2000);

                    // 设置定时刷新
                    setInterval(() => this.loadComfyUIStatus(), 3000);
                    setInterval(() => this.loadSystemInfo(), 5000);
                    setInterval(() => this.loadRealTimeGPUInfo(), 2000); // 每2秒更新GPU信息
                    
                    console.log('Data initialization complete');
                } catch (error) {
                    console.error('Failed to initialize data:', error);
                }
            }

            async initializeBackendUrl() {
                try {
                    if (window.electronAPI && window.electronAPI.getBackendUrl) {
                        this.backendUrl = await window.electronAPI.getBackendUrl();
                        this.API_BASE = this.backendUrl;
                        console.log('Backend URL initialized:', this.backendUrl);
                    } else {
                        // 如果electronAPI不可用，使用默认值
                        this.backendUrl = 'http://127.0.0.1:8404';
                        this.API_BASE = this.backendUrl;
                        console.warn('electronAPI not available, using default backend URL');
                    }
                } catch (error) {
                    console.error('Failed to get backend URL from main process:', error);
                    // 使用默认值作为后备
                    this.backendUrl = 'http://127.0.0.1:8404';
                    this.API_BASE = this.backendUrl;
                }
            }

            initEventListeners() {
                console.log('initEventListeners called');
                
                // 标签切换
                const tabBtns = document.querySelectorAll('.tab-btn');
                console.log('Found tab buttons:', tabBtns.length);
                tabBtns.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.switchTab(e.currentTarget.dataset.tab);
                    });
                });

                // 设置页面背景图片上传
                const backgroundImageUpload = document.getElementById('backgroundImageUpload');
                if (backgroundImageUpload) {
                    backgroundImageUpload.addEventListener('change', (e) => {
                        this.handleBackgroundUpload(e.target.files[0]);
                    });
                }

                // 设置页面背景视频上传
                const backgroundVideoUpload = document.getElementById('backgroundVideoUpload');
                if (backgroundVideoUpload) {
                    backgroundVideoUpload.addEventListener('change', (e) => {
                        this.handleVideoUpload(e.target.files[0]);
                    });
                }

                // 启动按钮
                const launchBtn = document.getElementById('launchBtn');
                if (launchBtn) {
                    launchBtn.addEventListener('click', () => {
                        this.toggleComfyUI();
                    });
                } else {
                    console.warn('launchBtn element not found');
                }

                // 浏览器按钮
                const browserBtn = document.getElementById('browserBtn');
                if (browserBtn) {
                    browserBtn.addEventListener('click', () => {
                        this.openComfyUIInBrowser();
                    });
                } else {
                    console.warn('browserBtn element not found');
                }

                // 专家模式按钮
                const expertBtn = document.querySelector('.expert-mode-btn');
                if (expertBtn) {
                    expertBtn.addEventListener('click', () => {
                        this.toggleExpertMode();
                    });
                } else {
                    console.warn('expert-mode-btn element not found');
                }

                // 预设卡片选择
                const presetCards = document.querySelectorAll('.preset-card');
                console.log('Found preset cards:', presetCards.length);
                presetCards.forEach(card => {
                    card.addEventListener('click', () => {
                        // 播放点击音效
                        if (window.audioManager) {
                            audioManager.play('click');
                        }
                        this.selectPreset(card.dataset.preset);
                    });
                });

                // 设置选项变化监听
                const settingInputs = document.querySelectorAll('input[type="radio"], input[type="checkbox"], input[type="text"], input[type="number"]');
                console.log('Found setting inputs:', settingInputs.length);
                settingInputs.forEach(input => {
                    input.addEventListener('change', () => {
                        // 只在启动设置页面元素存在时更新命令预览
                        if (document.getElementById('listen-ip')) {
                            this.updateCommandPreview();
                        }
                    });
                });

                // 代理设置显示/隐藏逻辑
                const enableProxyCheckbox = document.getElementById('enable-proxy');
                const proxyInputs = document.getElementById('proxy-inputs');
                if (enableProxyCheckbox && proxyInputs) {
                    enableProxyCheckbox.addEventListener('change', () => {
                        if (enableProxyCheckbox.checked) {
                            proxyInputs.style.display = 'block';
                        } else {
                            proxyInputs.style.display = 'none';
                        }
                        // 更新命令预览
                        if (document.getElementById('listen-ip')) {
                            this.updateCommandPreview();
                        }
                    });
                }

                // 版本管理标签切换将在initVersionManagement中绑定
            }

            switchTab(tabName) {
                console.log('switchTab called with:', tabName);
                
                // 更新标签按钮状态
                const allTabs = document.querySelectorAll('.tab-btn');
                console.log('Found tab buttons:', allTabs.length);
                
                allTabs.forEach(btn => {
                    btn.classList.remove('active');
                });
                
                const targetTab = document.querySelector(`[data-tab="${tabName}"]`);
                console.log('Target tab found:', targetTab);
                
                if (targetTab) {
                    targetTab.classList.add('active');
                } else {
                    console.error('Target tab not found for:', tabName);
                }

                // 背景媒体透明度控制
                const backgroundImage = document.getElementById('backgroundImage');
                const backgroundVideo = document.getElementById('backgroundVideo');
                const mainHome = document.getElementById('mainHome');
                
                console.log('Elements found:', {
                    backgroundImage: !!backgroundImage,
                    backgroundVideo: !!backgroundVideo,
                    mainHome: !!mainHome
                });
                
                if (tabName === 'home') {
                    console.log('Switching to home page');
                    // 主页 - 显示主页内容，背景正常
                    if (mainHome) {
                        mainHome.style.display = 'flex';
                        console.log('Main home displayed');
                    }
                    if (backgroundImage) backgroundImage.classList.remove('dimmed');
                    if (backgroundVideo) backgroundVideo.classList.remove('dimmed');
                    
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    console.log('All tab contents deactivated');
                } else {
                    // 功能页面 - 使用高效的页面切换
                    if (mainHome) {
                        mainHome.style.display = 'none';
                    }
                    if (backgroundImage) backgroundImage.classList.add('dimmed');
                    if (backgroundVideo) backgroundVideo.classList.add('dimmed');

                    // 高效的页面切换：只操作当前活跃的页面和目标页面
                    const currentActive = document.querySelector('.tab-content.active');
                    if (currentActive && currentActive.id !== tabName) {
                        currentActive.classList.remove('active');
                    }

                    const targetContent = document.getElementById(tabName);
                    if (targetContent) {
                        targetContent.classList.add('active');
                        console.log(`Switched to ${tabName}`);

                        // 插件管理页面 - 初始化插件管理功能
                        if (tabName === 'plugin-management') {
                            console.log('Plugin management page activated');
                            this.initPluginManagement();

                            // 确保默认显示已安装插件标签
                            setTimeout(() => {
                                const installedTab = document.querySelector('[data-type="installed"]');
                                const availableTab = document.querySelector('[data-type="available"]');

                                if (installedTab && availableTab) {
                                    installedTab.classList.add('active');
                                    availableTab.classList.remove('active');

                                    // 确保显示正确的内容
                                    this.showPluginTab('installed');
                                }
                            }, 50);
                        }

                        // 环境配置页面 - 自动检测环境信息
                        if (tabName === 'environment-config') {
                            console.log('Environment config page activated');
                            // 自动检测环境信息
                            this.detectEnvironmentInfo();
                        }
                    } else {
                        console.error(`Target content not found for: ${tabName}`);
                    }
                    
                    // 特殊处理：版本管理页面
                    if (tabName === 'version-management') {
                        this.initVersionManagement();
                    }
                }

                this.currentTab = tabName;
            }

            handleBackgroundUpload(file) {
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const imageUrl = e.target.result;
                        this.setBackgroundImage(imageUrl);
                        localStorage.setItem('ai-vision-background', imageUrl);
                        localStorage.removeItem('ai-vision-background-video');
                    };
                    reader.readAsDataURL(file);
                }
            }

            handleVideoUpload(file) {
                if (file && file.type.startsWith('video/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const videoUrl = e.target.result;
                        this.setBackgroundVideo(videoUrl);
                        localStorage.setItem('ai-vision-background-video', videoUrl);
                        localStorage.removeItem('ai-vision-background');
                    };
                    reader.readAsDataURL(file);
                }
            }

            setBackgroundImage(imageUrl) {
                const backgroundImage = document.getElementById('backgroundImage');
                const backgroundVideo = document.getElementById('backgroundVideo');
                
                backgroundImage.src = imageUrl;
                backgroundImage.style.display = 'block';
                backgroundVideo.style.display = 'none';
                backgroundVideo.pause();
            }

            setBackgroundVideo(videoUrl) {
                const backgroundImage = document.getElementById('backgroundImage');
                const backgroundVideo = document.getElementById('backgroundVideo');

                backgroundVideo.src = videoUrl;
                backgroundVideo.style.display = 'block';
                backgroundImage.style.display = 'none';

                // 应用保存的声音设置
                const videoAudioEnabled = localStorage.getItem('video-audio-enabled') === 'true';
                const videoVolume = parseFloat(localStorage.getItem('video-volume')) || 0.3;
                backgroundVideo.muted = !videoAudioEnabled;
                if (videoAudioEnabled) {
                    backgroundVideo.volume = videoVolume;
                }

                backgroundVideo.load();
                backgroundVideo.play().catch(e => console.log('视频自动播放被阻止:', e));
            }

            loadSavedBackground() {
                const savedBackground = localStorage.getItem('ai-vision-background');
                const savedVideo = localStorage.getItem('ai-vision-background-video');
                
                if (savedVideo) {
                    this.setBackgroundVideo(savedVideo);
                } else if (savedBackground) {
                    this.setBackgroundImage(savedBackground);
                }
            }

            loadSavedSettings() {
                const savedSettings = localStorage.getItem('ai-vision-launch-settings');
                if (!savedSettings) return;
                
                try {
                    const settings = JSON.parse(savedSettings);
                    
                    // 恢复显存模式
                    if (settings.vramMode) {
                        const vramRadio = document.getElementById(settings.vramMode);
                        if (vramRadio) vramRadio.checked = true;
                    }
                    
                    // 恢复注意力优化方法
                    if (settings.attentionMethod) {
                        const attentionSelect = document.getElementById('attention-method');
                        if (attentionSelect) attentionSelect.value = settings.attentionMethod;
                    }

                    // 恢复注意力上采样
                    if (settings.upcastMode !== undefined) {
                        const upcastSelect = document.getElementById('upcast-mode');
                        if (upcastSelect) upcastSelect.value = settings.upcastMode;
                    }
                    
                    // 恢复网络设置
                    if (settings.listenIp) {
                        const listenInput = document.getElementById('listen-ip');
                        if (listenInput) listenInput.value = settings.listenIp;
                    }
                    
                    if (settings.port) {
                        const portInput = document.getElementById('port');
                        if (portInput) portInput.value = settings.port;
                    }

                    // 恢复CORS设置
                    if (settings.corsMode) {
                        const corsSelect = document.getElementById('cors-setting');
                        if (corsSelect) corsSelect.value = settings.corsMode;
                    }

                    // 恢复代理设置
                    if (settings.enableProxy !== undefined) {
                        const enableProxyCheckbox = document.getElementById('enable-proxy');
                        if (enableProxyCheckbox) {
                            enableProxyCheckbox.checked = settings.enableProxy;
                            // 触发显示/隐藏逻辑
                            const proxyInputs = document.getElementById('proxy-inputs');
                            if (proxyInputs) {
                                proxyInputs.style.display = settings.enableProxy ? 'block' : 'none';
                            }
                        }
                    }

                    if (settings.httpProxy) {
                        const httpProxyInput = document.getElementById('http-proxy');
                        if (httpProxyInput) httpProxyInput.value = settings.httpProxy;
                    }

                    if (settings.httpsProxy) {
                        const httpsProxyInput = document.getElementById('https-proxy');
                        if (httpsProxyInput) httpsProxyInput.value = settings.httpsProxy;
                    }
                    
                    // 恢复选项设置
                    if (settings.options) {
                        Object.keys(settings.options).forEach(key => {
                            const kebabCase = key.replace(/([A-Z])/g, '-$1').toLowerCase();
                            const checkbox = document.getElementById(kebabCase);
                            if (checkbox) {
                                checkbox.checked = settings.options[key];
                            }
                        });
                    }
                    
                } catch (e) {
                    console.warn('Failed to load saved settings:', e);
                }
            }

            // API调用方法
            async apiCall(endpoint, method = 'GET', data = null) {
                try {
                    const options = {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    };
                    
                    if (data) {
                        options.body = JSON.stringify(data);
                    }
                    
                    const response = await fetch(`${this.API_BASE}${endpoint}`, options);
                    return await response.json();
                } catch (error) {
                    console.error(`API调用失败: ${error.message}`);
                    return { status: 'error', message: error.message };
                }
            }

            async loadSystemInfo() {
                try {
                    const result = await this.apiCall('/system/info');
                    if (result.status === 'success' || result.cpu_percent !== undefined) {
                        this.updateGPUInfo(result);
                    }
                } catch (error) {
                    console.error('Failed to load system info:', error);
                }
            }

            async loadComfyUIStatus() {
                try {
                    const result = await this.apiCall('/comfyui/status');
                    console.log('ComfyUI状态API响应:', result);
                    // 处理所有状态：running, stopped, starting等
                    if (result && result.status) {
                        this.updateComfyUIStatus(result);
                    }
                } catch (error) {
                    console.error('Failed to load ComfyUI status:', error);
                }
            }

            async loadGitStatus() {
                try {
                    const result = await this.apiCall('/git/status');
                    if (result.status === 'success') {
                        console.log('Git status loaded:', result);
                        // 可以在这里更新版本管理页面的显示
                    }
                } catch (error) {
                    console.error('Failed to load Git status:', error);
                }
            }

            updateGPUInfo(systemInfo) {
                // 获取实时GPU信息，而不是依赖systemInfo
                this.loadRealTimeGPUInfo();
            }

            async loadRealTimeGPUInfo() {
                try {
                    const response = await fetch(`${this.backendUrl}/system/cuda-info`);
                    if (response.ok) {
                        const data = await response.json();

                        // 更新GPU名称
                        const gpuName = document.getElementById('gpuName');
                        if (gpuName && data.gpu_name && data.gpu_name !== '未检测到GPU') {
                            // 简化GPU名称显示
                            let displayName = data.gpu_name;
                            if (displayName.includes('NVIDIA')) {
                                displayName = displayName.replace('NVIDIA GeForce ', '').replace('NVIDIA ', '');
                            }
                            if (displayName.includes('AMD')) {
                                displayName = displayName.replace('AMD Radeon ', '').replace('AMD ', '');
                            }
                            gpuName.textContent = displayName;
                        }

                        // 更新显存信息
                        const gpuUsage = document.getElementById('gpuUsage');
                        if (gpuUsage && data.memory && data.memory !== '未知') {
                            gpuUsage.textContent = `显存: ${data.memory}`;
                        } else if (gpuUsage) {
                            gpuUsage.textContent = `显存: 检测中...`;
                        }

                        // 更新GPU利用率
                        const gpuStatus = document.getElementById('gpuStatus');
                        if (gpuStatus && data.utilization !== undefined) {
                            gpuStatus.textContent = `${Math.round(data.utilization)}%`;
                        } else if (gpuStatus) {
                            gpuStatus.textContent = '0%';
                        }

                        console.log('GPU实时信息更新:', {
                            name: data.gpu_name,
                            memory: data.memory,
                            utilization: data.utilization,
                            temperature: data.temperature
                        });

                    }
                } catch (error) {
                    console.error('Failed to load real-time GPU info:', error);
                    // 如果获取失败，显示默认信息
                    const gpuUsage = document.getElementById('gpuUsage');
                    const gpuStatus = document.getElementById('gpuStatus');
                    const gpuName = document.getElementById('gpuName');

                    if (gpuUsage) gpuUsage.textContent = '显存: 检测失败';
                    if (gpuStatus) gpuStatus.textContent = '0%';
                    if (gpuName && gpuName.textContent === '检测中...') {
                        gpuName.textContent = 'GPU检测失败';
                    }
                }
            }



            updateComfyUIStatus(statusInfo) {
                const launchBtn = document.getElementById('launchBtn');
                const browserBtn = document.getElementById('browserBtn');
                const gpuInfo = document.getElementById('gpuInfo');

                // 兼容不同的状态格式：status === 'running' 或 is_running === true
                const isRunning = statusInfo.status === 'running' ||
                                statusInfo.status === 'starting' ||
                                statusInfo.status === 'already_running' ||
                                statusInfo.is_running === true;

                console.log('🔍 ComfyUI状态检查:', {
                    status: statusInfo.status,
                    is_running: statusInfo.is_running,
                    isRunning: isRunning,
                    gpuInfoElement: !!gpuInfo,
                    previousRunning: this.isRunning
                });

                if (isRunning) {
                    if (!this.isRunning) {
                        console.log('🚀 ComfyUI启动检测到，开始初始化工作流监控');
                    }
                    this.isRunning = true;
                    if (launchBtn) {
                        launchBtn.innerHTML = '<i class="fas fa-stop"></i> 停止 COMFYUI';
                        launchBtn.classList.add('running');
                    }
                    // 显示浏览器按钮
                    if (browserBtn) {
                        browserBtn.style.display = 'flex';
                    }
                    // 开始监控工作流状态，而不是直接启动光效
                    this.startWorkflowMonitoring();
                } else {
                    this.isRunning = false;
                    if (launchBtn) {
                        launchBtn.innerHTML = '<i class="fas fa-rocket"></i> 启动 COMFYUI';
                        launchBtn.classList.remove('running');
                    }
                    // 隐藏浏览器按钮
                    if (browserBtn) {
                        browserBtn.style.display = 'none';
                    }
                    // 停止工作流监控和光效
                    this.stopWorkflowMonitoring();
                }

                console.log('ComfyUI状态更新:', {status: statusInfo.status, isRunning: this.isRunning});
            }

            // 开始监控工作流状态
            startWorkflowMonitoring() {
                console.log('🔍 开始监控工作流状态');

                // 清除之前的监控
                this.stopWorkflowMonitoring();

                // 延迟启动监控，确保ComfyUI完全启动
                setTimeout(() => {
                    console.log('🔗 尝试连接WebSocket监控');
                    this.connectComfyUIWebSocket();

                    console.log('⏰ 启动HTTP轮询备用监控');
                    // 同时使用HTTP轮询作为备用方案
                    this.workflowCheckInterval = setInterval(() => {
                        this.checkWorkflowStatus();
                    }, 1000); // 每秒检查一次
                }, 3000); // 延迟3秒启动
            }

            // 停止监控工作流状态
            stopWorkflowMonitoring() {
                console.log('停止监控工作流状态');

                // 清除轮询
                if (this.workflowCheckInterval) {
                    clearInterval(this.workflowCheckInterval);
                    this.workflowCheckInterval = null;
                }

                // 关闭WebSocket连接
                if (this.comfyUIWebSocket) {
                    this.comfyUIWebSocket.close();
                    this.comfyUIWebSocket = null;
                }

                // 停止光效
                this.setWorkflowRunning(false);
            }

            // 连接ComfyUI WebSocket
            connectComfyUIWebSocket() {
                if (!this.isRunning) return;

                try {
                    // ComfyUI默认WebSocket地址
                    const wsUrl = 'ws://127.0.0.1:8188/ws';
                    console.log('尝试连接ComfyUI WebSocket:', wsUrl);

                    this.comfyUIWebSocket = new WebSocket(wsUrl);

                    this.comfyUIWebSocket.onopen = () => {
                        console.log('ComfyUI WebSocket连接成功');
                    };

                    this.comfyUIWebSocket.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleWorkflowMessage(data);
                        } catch (error) {
                            console.error('解析WebSocket消息失败:', error);
                        }
                    };

                    this.comfyUIWebSocket.onclose = () => {
                        console.log('ComfyUI WebSocket连接关闭');
                        this.comfyUIWebSocket = null;

                        // 如果ComfyUI还在运行，尝试重连
                        if (this.isRunning) {
                            setTimeout(() => {
                                this.connectComfyUIWebSocket();
                            }, 3000);
                        }
                    };

                    this.comfyUIWebSocket.onerror = (error) => {
                        console.error('ComfyUI WebSocket错误:', error);
                    };

                } catch (error) {
                    console.error('创建WebSocket连接失败:', error);
                }
            }

            // 处理工作流消息
            handleWorkflowMessage(data) {
                // ComfyUI WebSocket消息格式分析
                if (data.type === 'status') {
                    // 状态消息
                    const queueRemaining = data.data?.status?.exec_info?.queue_remaining || 0;
                    this.setWorkflowRunning(queueRemaining > 0);
                } else if (data.type === 'execution_start') {
                    // 开始执行
                    console.log('工作流开始执行');
                    this.setWorkflowRunning(true);
                } else if (data.type === 'execution_success' || data.type === 'execution_error') {
                    // 执行完成或出错
                    console.log('工作流执行完成');
                    this.setWorkflowRunning(false);
                } else if (data.type === 'executing') {
                    // 正在执行某个节点
                    const nodeId = data.data?.node;
                    if (nodeId) {
                        this.setWorkflowRunning(true);
                    } else {
                        // nodeId为null表示执行完成
                        this.setWorkflowRunning(false);
                    }
                }
            }

            // HTTP轮询检查工作流状态（备用方案）
            async checkWorkflowStatus() {
                try {
                    // 通过后端代理检查ComfyUI队列状态，避免CORS问题
                    const response = await this.apiCall('/comfyui/queue');
                    if (response && response.status !== 'error') {
                        const queueRunning = response.queue_running || [];
                        const queuePending = response.queue_pending || [];

                        const isRunning = queueRunning.length > 0 || queuePending.length > 0;

                        // 输出详细的队列状态信息
                        console.log('📊 队列状态检查:', {
                            running: queueRunning.length,
                            pending: queuePending.length,
                            isRunning: isRunning,
                            previousState: this.isWorkflowRunning,
                            queueData: response
                        });

                        this.setWorkflowRunning(isRunning);
                    } else {
                        console.log('❌ ComfyUI队列API响应失败:', response?.message || 'Unknown error', response);
                    }
                } catch (error) {
                    // 只在第一次失败时输出错误
                    if (!this.queueCheckErrorLogged) {
                        console.log('⚠️ ComfyUI队列检查失败 (可能还未完全启动):', error.message);
                        this.queueCheckErrorLogged = true;
                    }
                }
            }

            // 设置工作流运行状态和光效
            setWorkflowRunning(isRunning) {
                if (this.isWorkflowRunning !== isRunning) {
                    this.isWorkflowRunning = isRunning;
                    const gpuInfo = document.getElementById('gpuInfo');

                    console.log('🎯 工作流状态变化:', {
                        isRunning: isRunning,
                        previousState: this.isWorkflowRunning,
                        gpuInfoElement: !!gpuInfo,
                        gpuInfoId: gpuInfo ? gpuInfo.id : 'not found'
                    });

                    if (isRunning) {
                        console.log('✨ 工作流开始运行 - 启动GPU光效');
                        if (gpuInfo) {
                            gpuInfo.classList.add('working');
                            console.log('✅ 已添加working类到GPU信息框');
                            console.log('🔍 当前GPU信息框类列表:', gpuInfo.className);
                        } else {
                            console.error('❌ 找不到GPU信息框元素 (gpuInfo)');
                        }
                    } else {
                        console.log('⏹️ 工作流停止运行 - 停止GPU光效');
                        if (gpuInfo) {
                            gpuInfo.classList.remove('working');
                            console.log('✅ 已移除working类从GPU信息框');
                            console.log('🔍 当前GPU信息框类列表:', gpuInfo.className);
                        } else {
                            console.error('❌ 找不到GPU信息框元素 (gpuInfo)');
                        }
                    }
                }
            }

            async toggleComfyUI() {
                try {
                    if (!this.isRunning) {
                        // 启动 ComfyUI
                        console.log('正在启动ComfyUI...');
                        // 播放启动音效
                        if (window.audioManager) {
                            audioManager.play('startup');
                        }
                        const result = await this.apiCall('/comfyui/start', 'POST');
                        console.log('ComfyUI启动响应:', result);

                        // 检查启动是否成功（包括running和starting状态）
                        if (result.status === 'running' || result.status === 'starting' || result.status === 'already_running') {
                            console.log('ComfyUI启动成功，状态:', result.status);
                            // 播放ComfyUI启动成功音效
                            if (window.audioManager) {
                                audioManager.play('startup-success');
                            }
                            this.showNotification(result.message || 'ComfyUI启动成功', 'success', 'comfyui-startup');

                            // 根据不同状态决定延迟时间
                            let delay = 3000; // 默认3秒
                            if (result.status === 'running') {
                                delay = 1000; // 如果已经运行，1秒后打开
                            } else if (result.status === 'starting') {
                                delay = 5000; // 如果还在启动，5秒后打开
                            } else if (result.status === 'already_running') {
                                delay = 500; // 如果已经在运行，立即打开
                            }

                            // 延迟后自动打开浏览器
                            setTimeout(() => {
                                this.openComfyUIInBrowser();
                            }, delay);

                            // 延迟启动工作流监控，确保ComfyUI完全启动
                            setTimeout(() => {
                                console.log('🚀 ComfyUI启动成功，强制启动工作流监控');
                                this.startWorkflowMonitoring();
                            }, delay + 2000); // 在打开浏览器后2秒启动监控
                        } else {
                            console.error('启动失败:', result.message);
                            this.showNotification(result.message || '启动失败', 'error');
                        }
                    } else {
                        // 停止 ComfyUI
                        console.log('正在停止ComfyUI...');
                        // 播放停止音效
                        if (window.audioManager) {
                            audioManager.play('shutdown');
                        }
                        const result = await this.apiCall('/comfyui/stop', 'POST');
                        if (result.status === 'stopped' || result.status === 'already_stopped') {
                            console.log('ComfyUI已停止');
                            // 播放ComfyUI关闭成功音效
                            if (window.audioManager) {
                                audioManager.play('shutdown-success');
                            }
                            this.showNotification(result.message || 'ComfyUI已停止', 'success', 'comfyui-shutdown');
                        } else {
                            console.error('停止失败:', result.message);
                            this.showNotification(result.message || '停止失败', 'error');
                        }
                    }

                    // 刷新状态
                    await this.loadComfyUIStatus();
                } catch (error) {
                    console.error('ComfyUI操作失败:', error);
                    this.showNotification('操作失败: ' + error.message, 'error');
                }
            }

            async openComfyUIInBrowser() {
                try {
                    // 获取ComfyUI的访问地址
                    const listenIp = document.getElementById('listen-ip')?.value || '127.0.0.1';
                    const port = document.getElementById('port')?.value || '8188';
                    const url = `http://${listenIp}:${port}`;

                    console.log('Opening ComfyUI in browser:', url);

                    // 先检查ComfyUI是否真的可访问
                    try {
                        const checkResult = await this.apiCall('/comfyui/check');
                        if (checkResult.status === 'accessible') {
                            console.log('ComfyUI web service is accessible, opening browser...');
                        } else {
                            console.warn('ComfyUI web service may not be ready yet, but trying to open anyway...');
                        }
                    } catch (checkError) {
                        console.warn('Failed to check ComfyUI accessibility:', checkError);
                    }

                    // 尝试多种方式打开浏览器
                    let opened = false;

                    // 方式1: 使用Electron的shell.openExternal (推荐)
                    if (window.electronAPI && window.electronAPI.openExternal) {
                        try {
                            await window.electronAPI.openExternal(url);
                            opened = true;
                            console.log('Opened using Electron shell.openExternal');
                        } catch (e) {
                            console.warn('Failed to open with Electron shell:', e);
                        }
                    }

                    // 方式2: 使用window.open作为备选
                    if (!opened) {
                        try {
                            const newWindow = window.open(url, '_blank');
                            if (newWindow) {
                                opened = true;
                                console.log('Opened using window.open');
                            }
                        } catch (e) {
                            console.warn('Failed to open with window.open:', e);
                        }
                    }

                    if (opened) {
                        this.showNotification('已在浏览器中打开ComfyUI', 'success');
                    } else {
                        this.showNotification(`请手动打开浏览器访问: ${url}`, 'info');
                        // 复制URL到剪贴板（如果支持）
                        if (navigator.clipboard) {
                            try {
                                await navigator.clipboard.writeText(url);
                                this.showNotification('URL已复制到剪贴板', 'info');
                            } catch (e) {
                                console.warn('Failed to copy to clipboard:', e);
                            }
                        }
                    }

                } catch (error) {
                    console.error('Failed to open ComfyUI in browser:', error);
                    this.showNotification('打开浏览器失败: ' + error.message, 'error');
                }
            }



            toggleExpertMode() {
                const btn = document.querySelector('.expert-mode-btn');
                if (btn.textContent === '启用高级选项') {
                    btn.textContent = '禁用高级选项';
                    btn.style.background = 'rgba(0, 245, 255, 0.2)';
                    btn.style.borderColor = 'rgba(0, 245, 255, 0.5)';
                    btn.style.color = 'var(--neon-glow)';
                } else {
                    btn.textContent = '启用高级选项';
                    btn.style.background = 'rgba(255, 102, 0, 0.2)';
                    btn.style.borderColor = 'rgba(255, 102, 0, 0.5)';
                    btn.style.color = '#ff6600';
                }
            }

            selectPreset(presetType) {
                // 移除所有卡片的active状态
                document.querySelectorAll('.preset-card').forEach(card => {
                    card.classList.remove('active');
                });
                
                // 激活选中的卡片
                document.querySelector(`[data-preset="${presetType}"]`).classList.add('active');
                
                // 根据预设类型设置参数
                switch(presetType) {
                    case 'high-end':
                        document.getElementById('highvram').checked = true;
                        document.getElementById('flash-attention').checked = true;
                        document.getElementById('bf16-unet').checked = true;
                        break;
                    case 'mid-range':
                        document.getElementById('normalvram').checked = true;
                        document.getElementById('pytorch-attention').checked = true;
                        break;
                    case 'low-end':
                        document.getElementById('lowvram').checked = true;
                        document.getElementById('split-attention').checked = true;
                        break;
                }
                
                // 只在启动设置页面时更新命令预览
                if (document.getElementById('listen-ip')) {
                    this.updateCommandPreview();
                }
            }

            updateCommandPreview() {
                let command = 'python main.py';
                
                // 监听设置
                const listenEl = document.getElementById('listen-ip');
                const portEl = document.getElementById('port');
                if (listenEl && portEl) {
                    const listen = listenEl.value || '127.0.0.1';
                    const port = portEl.value || '8188';
                    command += ` --listen ${listen} --port ${port}`;
                }
                
                // 显存模式
                const vramMode = document.querySelector('input[name="vram-mode"]:checked');
                if (vramMode) {
                    switch(vramMode.value) {
                        case 'gpu-only':
                            command += ' --gpu-only';
                            break;
                        case 'highvram':
                            command += ' --highvram';
                            break;
                        case 'normalvram':
                            command += ' --normalvram';
                            break;
                        case 'lowvram':
                            command += ' --lowvram';
                            break;
                        case 'cpu':
                            command += ' --cpu';
                            break;
                    }
                }

                // 注意力优化方法
                const attentionMethod = document.getElementById('attention-method');
                if (attentionMethod && attentionMethod.value) {
                    command += ` --${attentionMethod.value}`;
                }

                // 注意力上采样
                const upcastMode = document.getElementById('upcast-mode');
                if (upcastMode && upcastMode.value) {
                    command += ` --${upcastMode.value}`;
                }

                // 禁用xformers
                if (document.getElementById('disable-xformers')?.checked) {
                    command += ' --disable-xformers';
                }
                
                // 预留显存
                const reserveVram = document.getElementById('reserve-vram');
                if (reserveVram && parseFloat(reserveVram.value) > 0) {
                    command += ` --reserve-vram ${reserveVram.value}`;
                }

                // 异步权重卸载
                if (document.getElementById('async-offload')?.checked) {
                    command += ' --async-offload';
                }

                // CORS设置
                const corsMode = document.getElementById('cors-setting');
                if (corsMode && corsMode.value) {
                    command += ` --${corsMode.value}`;
                }

                // 代理设置（只有启用代理时才添加）
                const enableProxy = document.getElementById('enable-proxy');
                if (enableProxy && enableProxy.checked) {
                    const httpProxy = document.getElementById('http-proxy');
                    const httpsProxy = document.getElementById('https-proxy');

                    if (httpProxy && httpProxy.value) {
                        command += ` --proxy-http ${httpProxy.value}`;
                    }
                    if (httpsProxy && httpsProxy.value) {
                        command += ` --proxy-https ${httpsProxy.value}`;
                    }
                }
                
                // 更新命令预览
                document.getElementById('command-preview').textContent = command;
            }

            saveSettings() {
                const settings = {
                    vramMode: document.querySelector('input[name="vram-mode"]:checked')?.value,
                    attentionMethod: document.getElementById('attention-method')?.value,
                    upcastMode: document.getElementById('upcast-mode')?.value,
                    listenIp: document.getElementById('listen-ip')?.value,
                    port: document.getElementById('port')?.value,
                    corsMode: document.getElementById('cors-setting')?.value,
                    enableProxy: document.getElementById('enable-proxy')?.checked,
                    httpProxy: document.getElementById('http-proxy')?.value,
                    httpsProxy: document.getElementById('https-proxy')?.value,
                    options: {
                        reserveVram: document.getElementById('reserve-vram')?.value,
                        asyncOffload: document.getElementById('async-offload')?.checked,
                        disableXformers: document.getElementById('disable-xformers')?.checked
                    }
                };
                
                localStorage.setItem('ai-vision-launch-settings', JSON.stringify(settings));
                
                // 显示保存成功提示
                this.showNotification('设置已保存', 'success');
            }

            applyAndLaunch() {
                this.saveSettings();
                // 这里可以添加实际启动ComfyUI的逻辑
                this.showNotification('正在应用设置并启动...', 'info');
                setTimeout(() => {
                    this.toggleComfyUI();
                }, 1000);
            }

            resetToDefault() {
                // 重置为默认设置
                document.getElementById('highvram').checked = true;
                document.getElementById('default-attention').checked = true;
                document.getElementById('listen-ip').value = '127.0.0.1';
                document.getElementById('port').value = '8188';
                document.getElementById('http-proxy').value = '';
                document.getElementById('https-proxy').value = '';
                
                // 重置复选框
                document.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                    cb.checked = false;
                });
                
                // 只在启动设置页面时更新命令预览
                if (document.getElementById('listen-ip')) {
                    this.updateCommandPreview();
                }
                this.showNotification('已重置为默认设置', 'warning');
            }

            showNotification(message, type = 'info', context = null) {
                // 播放对应的音效
                if (window.audioManager) {
                    switch (type) {
                        case 'success':
                            // 根据上下文播放不同的成功音效
                            if (context === 'comfyui-startup' || (message.includes('ComfyUI') && message.includes('启动'))) {
                                audioManager.play('startup-success');
                            } else if (context === 'comfyui-shutdown' || (message.includes('ComfyUI') && (message.includes('停止') || message.includes('关闭')))) {
                                audioManager.play('shutdown-success');
                            } else if (context === 'plugin' || message.includes('插件') || message.includes('plugin')) {
                                audioManager.play('plugin-success');
                            } else if (context === 'version' || message.includes('版本') || message.includes('version')) {
                                audioManager.play('version-success');
                            } else if (context === 'install' || message.includes('安装') || message.includes('install')) {
                                audioManager.play('install-success');
                            } else if (context === 'update' || message.includes('更新') || message.includes('update')) {
                                audioManager.play('update-success');
                            } else {
                                audioManager.play('success');
                            }
                            break;
                        case 'error':
                            audioManager.play('error');
                            break;
                        case 'warning':
                            audioManager.play('warning');
                            break;
                        case 'info':
                        default:
                            audioManager.play('notification');
                            break;
                    }
                }

                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.textContent = message;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(26, 26, 46, 0.95);
                    border: 1px solid var(--neon-glow);
                    border-radius: 8px;
                    padding: 12px 20px;
                    color: var(--text-primary);
                    font-size: 0.9rem;
                    z-index: 10000;
                    backdrop-filter: blur(10px);
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;
                
                document.body.appendChild(notification);
                
                // 显示动画
                setTimeout(() => {
                    notification.style.opacity = '1';
                    notification.style.transform = 'translateX(0)';
                }, 10);
                
                // 自动移除
                setTimeout(() => {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            }

            // 版本管理相关方法
            initVersionManagement() {
                this.currentVersionType = 'stable';
                this.pendingVersionSwitch = null;

                // 绑定版本管理页面的事件
                this.bindVersionManagementEvents();

                // 确保默认显示稳定版标签页并设置正确的高亮状态
                setTimeout(() => {
                    this.switchVersionTab('stable');
                }, 150);

                this.loadVersions();
            }

            bindVersionManagementEvents() {
                // 等待DOM更新后绑定事件
                setTimeout(() => {
                    // 绑定版本类型切换标签
                    const versionTabs = document.querySelectorAll('.version-tab-btn');
                    console.log('Found version tabs:', versionTabs.length);
                    
                    versionTabs.forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            e.preventDefault();
                            console.log('Version tab clicked:', e.currentTarget.dataset.type);
                            this.switchVersionTab(e.currentTarget.dataset.type);
                        });
                    });
                }, 100);
            }

            async loadVersions() {
                const loading = document.getElementById('version-loading');
                const stableList = document.getElementById('stable-versions');
                const devList = document.getElementById('development-versions');
                
                // 显示加载状态
                loading.style.display = 'flex';
                stableList.style.display = 'none';
                devList.style.display = 'none';
                
                try {
                    // 模拟获取版本数据（实际应该调用git命令）
                    const versionData = await this.fetchVersionData();
                    console.log('Version data loaded:', versionData);
                    
                    // 渲染版本列表
                    this.renderVersionList('stable', versionData.stable);
                    this.renderVersionList('development', versionData.development);
                    
                    // 隐藏加载状态，显示对应列表
                    loading.style.display = 'none';
                    this.showVersionList(this.currentVersionType);
                    
                } catch (error) {
                    console.error('Failed to load versions:', error);
                    loading.innerHTML = `
                        <div style="color: #ff4757; text-align: center;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>加载版本信息失败</p>
                            <button onclick="launcherInstance.loadVersions()" class="btn btn-primary">重试</button>
                        </div>
                    `;
                }
            }

            async fetchVersionData() {
                try {
                    // 调用真实的API获取ComfyUI版本信息
                    const response = await fetch(`${this.backendUrl}/comfyui/versions`);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.status === 'success') {
                            return {
                                stable: data.stable,
                                development: data.development,
                                current_branch: data.current_branch,
                                current_commit: data.current_commit
                            };
                        } else {
                            throw new Error(data.message || '获取版本信息失败');
                        }
                    } else {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                } catch (error) {
                    console.error('获取版本数据失败:', error);
                    // 如果API调用失败，返回错误信息
                    throw error;
                }
            }

            renderVersionList(type, versions) {
                const container = document.getElementById(`${type}-versions`);

                // 找到当前版本的索引
                const currentIndex = versions.findIndex(v => v.isCurrent);

                container.innerHTML = versions.map((version, index) => {
                    const isStable = type === 'stable';

                    // 判断是否是更新版本（在当前版本之前的版本）
                    const isNewer = currentIndex > -1 && index < currentIndex;
                    const isLatest = index === 0; // 第一个版本是最新版本

                    const newerBadge = isNewer ? '<span style="color: #ff6b35; font-size: 11px; margin-left: 8px;">🔥 更新</span>' : '';
                    const currentBadge = version.isCurrent ? '<span style="color: #28a745; font-size: 11px; margin-left: 8px; font-weight: bold; text-shadow: 0 0 3px rgba(40, 167, 69, 0.5);">✓ 当前</span>' : '';
                    const latestBadge = isLatest && !version.isCurrent ? '<span style="color: #00f5ff; font-size: 11px; margin-left: 8px; font-weight: bold; text-shadow: 0 0 3px rgba(0, 245, 255, 0.5);">⭐ 最新</span>' : '';

                    // 版本类型标识
                    let typeIcon = isStable ? '🏷️' : '📝';

                    // 为开发版本添加来源标识
                    if (!isStable && version.source) {
                        switch(version.source) {
                            case 'local':
                                typeIcon = '🔧'; // 本地fork提交
                                break;
                            case 'upstream':
                                typeIcon = '🌐'; // 原始仓库提交
                                break;
                            case 'origin':
                                typeIcon = '📡'; // origin仓库提交
                                break;
                            default:
                                typeIcon = '📝';
                        }
                    }

                    return `
                        <div class="version-item ${version.isCurrent ? 'current' : ''} ${isNewer ? 'newer' : ''} ${isLatest ? 'latest' : ''}" data-version="${version.id}">
                            <div class="version-info">
                                <div class="version-main">
                                    <div class="version-indicator"></div>
                                    <span class="version-id">${typeIcon} ${version.id}${currentBadge}${latestBadge}${newerBadge}</span>
                                    <span class="version-date">${version.date}</span>
                                </div>
                                ${!isStable ? `<div class="version-commit">${version.commit}</div>` : ''}
                                ${version.author ? `<div class="version-author" style="color: #888; font-size: 11px; margin-top: 2px;">作者: ${version.author}</div>` : ''}
                            </div>
                            <button class="version-switch-btn ${version.isCurrent ? 'current' : ''} ${isNewer ? 'newer' : ''}"
                                    onclick="launcherInstance.requestVersionSwitch('${version.id}')"
                                    ${version.isCurrent ? 'disabled' : ''}>
                                <i class="fas fa-${version.isCurrent ? 'check' : (isNewer ? 'arrow-up' : 'download')}"></i>
                                ${version.isCurrent ? '当前版本' : (isNewer ? '升级' : '切换')}
                            </button>
                        </div>
                    `;
                }).join('');
            }

            switchVersionTab(type) {
                console.log('Switching to version tab:', type);
                
                // 更新标签状态
                document.querySelectorAll('.version-tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                const targetTab = document.querySelector(`[data-type="${type}"]`);
                if (targetTab) {
                    targetTab.classList.add('active');
                }
                
                // 显示对应的版本列表
                this.currentVersionType = type;
                this.showVersionList(type);
            }

            showVersionList(type) {
                const stableList = document.getElementById('stable-versions');
                const devList = document.getElementById('development-versions');
                
                if (type === 'stable') {
                    stableList.style.display = 'flex';
                    devList.style.display = 'none';
                } else {
                    stableList.style.display = 'none';
                    devList.style.display = 'flex';
                }
            }

            requestVersionSwitch(versionId) {
                this.pendingVersionSwitch = versionId;
                
                // 显示确认对话框
                document.getElementById('target-version').textContent = versionId;
                document.getElementById('version-confirm-modal').classList.add('show');
            }

            async performVersionSwitch(versionId) {
                // 显示进度对话框
                document.getElementById('progress-version').textContent = versionId;
                document.getElementById('version-progress-modal').classList.add('show');

                const progressFill = document.getElementById('progress-fill');

                try {
                    // 开始切换进度
                    progressFill.style.width = '20%';

                    // 调用后端API切换版本
                    const response = await fetch(`${this.backendUrl}/comfyui/switch-version`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            version_id: versionId
                        })
                    });

                    progressFill.style.width = '60%';

                    if (response.ok) {
                        const result = await response.json();
                        progressFill.style.width = '80%';

                        if (result.status === 'success') {
                            // 切换成功，更新UI
                            progressFill.style.width = '100%';
                            await new Promise(resolve => setTimeout(resolve, 500));

                            this.updateCurrentVersion(versionId);

                            // 重新加载版本列表以获取最新状态
                            await this.loadVersions();

                            this.showNotification(`✅ 成功切换到版本 ${versionId}`, 'success', 'version');
                        } else {
                            throw new Error(result.message || '版本切换失败');
                        }
                    } else {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }

                } catch (error) {
                    console.error('Version switch failed:', error);
                    this.showNotification(`❌ 版本切换失败: ${error.message}`, 'error');
                } finally {
                    // 隐藏进度对话框
                    document.getElementById('version-progress-modal').classList.remove('show');
                    progressFill.style.width = '0%';
                }
            }

            updateCurrentVersion(newVersionId) {
                // 移除所有当前版本标记
                document.querySelectorAll('.version-item').forEach(item => {
                    item.classList.remove('current');
                    const btn = item.querySelector('.version-switch-btn');
                    btn.textContent = '切换';
                    btn.classList.remove('current');
                    btn.disabled = false;
                });
                
                // 标记新的当前版本
                const newCurrentItem = document.querySelector(`[data-version="${newVersionId}"]`);
                if (newCurrentItem) {
                    newCurrentItem.classList.add('current');
                    const btn = newCurrentItem.querySelector('.version-switch-btn');
                    btn.textContent = '当前版本';
                    btn.classList.add('current');
                    btn.disabled = true;
                }
            }

            async preloadPluginInfo() {
                try {
                    console.log('Pre-loading plugin information...');
                    // 静默加载插件信息，不显示加载状态
                    const pluginData = await this.fetchInstalledPlugins();
                    this.installedPlugins = pluginData;
                    this.pluginCacheTime = Date.now();
                    console.log(`Plugin info pre-loaded: ${pluginData.length} plugins cached`);
                } catch (error) {
                    console.log('Plugin pre-loading failed (will load when needed):', error.message);
                }
            }

            // 使用DOM操作创建插件卡片元素，避免HTML解析问题
            createPluginCardElement(plugin) {
                // 安全地处理所有可能包含特殊字符的字段
                const safeName = (plugin.name || '未知插件').toString();
                const safeVersion = (plugin.version || '未知').toString();
                const safeAuthor = (plugin.author || '未知').toString();
                const safeDate = (plugin.date || '未知').toString();
                const safeDescription = (plugin.description || '暂无描述').toString();

                const isEnabled = plugin.enabled === true || plugin.status === 'enabled';
                const statusColor = isEnabled ? '#28a745' : '#dc3545';
                const statusText = isEnabled ? '✓ 已启用' : '✗ 已禁用';
                const safeUrl = plugin.url || `https://github.com/search?q=${encodeURIComponent(safeName)}`;

                // 判断是否为最新版本（用于更新按钮颜色）
                const isLatestVersion = plugin.isLatestVersion !== false; // 默认认为是最新版本
                const updateButtonColor = isLatestVersion ? '#6c757d' : '#28a745'; // 灰色或绿色

                // 创建主容器
                const cardDiv = document.createElement('div');
                cardDiv.setAttribute('data-plugin-name', safeName);
                cardDiv.style.cssText = 'background: rgba(26, 26, 46, 0.2); padding: 20px; margin: 15px 0; border-radius: 8px; border: 1px solid rgba(0, 245, 255, 0.1); width: 100%; box-sizing: border-box; transition: all 0.2s ease;';

                // 添加hover效果
                cardDiv.addEventListener('mouseenter', () => {
                    cardDiv.style.borderColor = 'rgba(0, 245, 255, 0.3)';
                    cardDiv.style.background = 'rgba(26, 26, 46, 0.4)';
                });
                cardDiv.addEventListener('mouseleave', () => {
                    cardDiv.style.borderColor = 'rgba(0, 245, 255, 0.1)';
                    cardDiv.style.background = 'rgba(26, 26, 46, 0.2)';
                });

                // 创建信息区域
                const infoDiv = document.createElement('div');
                infoDiv.style.cssText = 'margin-bottom: 15px;';

                // 插件名称和状态
                const nameDiv = document.createElement('div');
                nameDiv.style.cssText = 'margin-bottom: 10px;';

                const nameStrong = document.createElement('strong');
                nameStrong.style.cssText = 'color: #00f5ff; font-size: 18px; display: block; margin-bottom: 5px;';
                nameStrong.textContent = safeName;

                const statusSpan = document.createElement('span');
                statusSpan.style.cssText = `color: ${statusColor}; font-size: 13px; font-weight: bold;`;
                statusSpan.textContent = statusText;

                nameDiv.appendChild(nameStrong);
                nameDiv.appendChild(statusSpan);

                // 版本信息
                const versionDiv = document.createElement('div');
                versionDiv.style.cssText = 'color: #ccc; font-size: 13px; margin-bottom: 8px; line-height: 1.4;';

                const versionSpan1 = document.createElement('span');
                versionSpan1.style.cssText = 'margin-right: 20px;';
                versionSpan1.textContent = `版本: ${safeVersion}`;

                const versionSpan2 = document.createElement('span');
                versionSpan2.style.cssText = 'margin-right: 20px;';
                versionSpan2.textContent = `作者: ${safeAuthor}`;

                const versionSpan3 = document.createElement('span');
                versionSpan3.textContent = `日期: ${safeDate}`;

                versionDiv.appendChild(versionSpan1);
                versionDiv.appendChild(versionSpan2);
                versionDiv.appendChild(versionSpan3);

                // 描述信息
                const descDiv = document.createElement('div');
                descDiv.style.cssText = 'color: #aaa; font-size: 12px; line-height: 1.5; margin-bottom: 15px;';
                descDiv.textContent = safeDescription;

                infoDiv.appendChild(nameDiv);
                infoDiv.appendChild(versionDiv);
                infoDiv.appendChild(descDiv);

                // 创建按钮区域
                const buttonDiv = document.createElement('div');
                buttonDiv.style.cssText = 'display: flex; gap: 8px; flex-wrap: nowrap; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.1); overflow-x: auto;';



                // 禁用/启用按钮的样式和文本
                const toggleButtonColor = isEnabled ? '#ffc107' : '#28a745';
                const toggleButtonText = isEnabled ? '禁用' : '启用';
                const toggleButtonIcon = isEnabled ? 'fas fa-pause' : 'fas fa-play';
                const toggleButtonTextColor = isEnabled ? 'black' : 'white';

                // 创建按钮
                const buttons = [
                    { text: '主页', icon: 'fas fa-external-link-alt', color: '#007bff', textColor: 'white', action: () => window.open(safeUrl, '_blank') },
                    { text: toggleButtonText, icon: toggleButtonIcon, color: toggleButtonColor, textColor: toggleButtonTextColor, action: () => window.togglePlugin && window.togglePlugin(safeName, isEnabled) },
                    { text: '卸载', icon: 'fas fa-trash', color: '#dc3545', textColor: 'white', action: () => window.uninstallPlugin && window.uninstallPlugin(safeName) },
                    { text: '切换', icon: 'fas fa-code-branch', color: '#6c757d', textColor: 'white', action: () => window.showPluginVersions && window.showPluginVersions(safeName) },
                    { text: '更新', icon: 'fas fa-sync-alt', color: updateButtonColor, textColor: 'white', action: () => window.updatePlugin && window.updatePlugin(safeName) }
                ];

                buttons.forEach(btn => {
                    const button = document.createElement('button');
                    const textColor = btn.textColor || 'white';
                    button.style.cssText = `padding: 7px 12px; background: ${btn.color}; color: ${textColor}; border: none; border-radius: 4px; cursor: pointer; font-size: 11.5px; display: flex; align-items: center; gap: 4px; flex-shrink: 0; white-space: nowrap;`;

                    const icon = document.createElement('i');
                    icon.className = btn.icon;

                    button.appendChild(icon);
                    button.appendChild(document.createTextNode(` ${btn.text}`));
                    button.addEventListener('click', (e) => {
                        // 播放点击音效
                        if (window.audioManager) {
                            audioManager.play('click');
                        }
                        btn.action(e);
                    });

                    buttonDiv.appendChild(button);
                });

                // 组装卡片
                cardDiv.appendChild(infoDiv);
                cardDiv.appendChild(buttonDiv);

                return cardDiv;
            }

            // 插件管理相关方法
            initPluginManagement() {
                console.log('=== initPluginManagement START ===');
                console.log('backendUrl:', this.backendUrl);

                // 检查插件管理页面的DOM元素
                const pluginManagement = document.getElementById('plugin-management');
                const installedPluginList = document.getElementById('installed-plugin-list');
                const installedLoading = document.getElementById('installed-loading');

                console.log('Plugin management element:', pluginManagement);
                console.log('Installed plugin list element:', installedPluginList);
                console.log('Installed loading element:', installedLoading);

                if (!pluginManagement) {
                    console.error('Plugin management page not found!');
                    return;
                }

                if (!installedPluginList) {
                    console.error('Installed plugin list container not found!');
                    return;
                }

                // 应用滚动条防护CSS到所有插件容器
                this.applyScrollbarProtection();

                // 初始化插件状态（保留现有数据，避免重复加载）
                this.currentPluginTab = 'installed';
                if (!this.installedPlugins) {
                    this.installedPlugins = [];
                }
                if (!this.availablePlugins) {
                    this.availablePlugins = [];
                }
                this.currentVersionSwitchPlugin = null;

                // 绑定插件管理页面的事件
                this.bindPluginManagementEvents();

                // 检查是否有缓存的插件数据
                const hasInstalledCache = this.installedPlugins && this.installedPlugins.length > 0;
                const hasContentInDOM = installedPluginList && installedPluginList.children.length > 0;

                if (hasInstalledCache && hasContentInDOM) {
                    console.log('Using cached plugin data and DOM content');
                    // 确保容器可见且滚动条设置正确
                    installedPluginList.style.display = 'block';
                    if (installedLoading) {
                        installedLoading.style.display = 'none';
                    }
                } else {
                    console.log('No cache available, loading fresh plugin data...');
                    this.loadInstalledPlugins();
                }

                console.log('=== initPluginManagement END ===');
            }

            // 应用滚动条防护到所有插件容器
            applyScrollbarProtection() {
                const containers = [
                    '#installed-plugin-list',
                    '#available-plugin-list',
                    '.installed-plugins',
                    '.available-plugins',
                    '.plugin-list'
                ];

                containers.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        element.style.overflow = 'visible';
                        element.style.maxHeight = 'none';
                        element.style.height = 'auto';
                    });
                });

                console.log('Scrollbar protection applied to all plugin containers');
            }

            // 清理插件缓存（当需要强制刷新时调用）
            clearPluginCache() {
                this.installedPlugins = null;
                this.pluginCacheTime = null;
                this.cachedPluginHTML = null;
                console.log('Plugin cache cleared');
            }

            bindPluginManagementEvents() {
                // 等待DOM更新后绑定事件
                setTimeout(() => {
                    // 绑定插件类型切换标签
                    const pluginTabs = document.querySelectorAll('.plugin-tab-btn');
                    console.log('Found plugin tabs:', pluginTabs.length);
                    
                    pluginTabs.forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            e.preventDefault();
                            console.log('Plugin tab clicked:', e.currentTarget.dataset.type);
                            this.switchPluginTab(e.currentTarget.dataset.type);
                        });
                    });

                    // 绑定搜索功能 - 优化防抖和性能
                    const searchInput = document.getElementById('plugin-search');
                    if (searchInput) {
                        let searchTimeout;
                        let lastSearchValue = '';

                        searchInput.addEventListener('input', (e) => {
                            const currentValue = e.target.value;

                            // 如果搜索值没有变化，跳过处理
                            if (currentValue === lastSearchValue) {
                                return;
                            }

                            clearTimeout(searchTimeout);
                            searchTimeout = setTimeout(() => {
                                lastSearchValue = currentValue;
                                this.filterAvailablePlugins(currentValue);
                            }, 500); // 增加防抖时间到500ms，减少频繁搜索
                        });

                        // 添加键盘事件优化
                        searchInput.addEventListener('keydown', (e) => {
                            // ESC键清空搜索
                            if (e.key === 'Escape') {
                                searchInput.value = '';
                                this.filterAvailablePlugins('');
                            }
                        });
                    }

                    // 绑定分类筛选
                    const categorySelect = document.getElementById('category-filter');
                    if (categorySelect) {
                        categorySelect.addEventListener('change', (e) => {
                            this.filterPluginsByCategory(e.target.value);
                        });
                    }
                }, 100);
            }

            switchPluginTab(type) {
                console.log('Switching to plugin tab:', type);
                
                // 更新标签状态
                document.querySelectorAll('.plugin-tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                const targetTab = document.querySelector(`[data-type="${type}"]`);
                if (targetTab) {
                    targetTab.classList.add('active');
                }
                
                // 显示对应的插件列表
                this.currentPluginTab = type;
                this.showPluginTab(type);
            }

            showPluginTab(type) {
                console.log('=== showPluginTab START ===');
                console.log('Switching to tab type:', type);

                const installedContainer = document.getElementById('installed-plugins');
                const availableContainer = document.getElementById('available-plugins');

                console.log('Installed container:', installedContainer);
                console.log('Available container:', availableContainer);

                // 如果找不到available容器，查找所有可能的容器
                if (!availableContainer) {
                    console.log('Available container not found! Searching for alternatives...');
                    const allContainers = document.querySelectorAll('[id*="available"], [class*="available"]');
                    console.log('All available-related elements:', allContainers);
                }

                if (type === 'installed') {
                    if (installedContainer) {
                        installedContainer.style.display = 'block';
                        installedContainer.classList.add('active');
                        // 确保滚动条设置正确
                        installedContainer.style.overflow = 'visible';
                    }
                    if (availableContainer) {
                        availableContainer.style.display = 'none';
                        availableContainer.classList.remove('active');
                    }

                    // 检查是否需要重新加载插件数据
                    const pluginListContainer = document.querySelector('#installed-plugin-list');
                    const hasContent = pluginListContainer && pluginListContainer.children.length > 0;

                    if (!hasContent || !this.installedPlugins || this.installedPlugins.length === 0) {
                        console.log('No cached content or empty plugin data, reloading...');
                        this.loadInstalledPlugins();
                    } else {
                        console.log('Using cached plugin data');
                        // 确保容器可见
                        if (pluginListContainer) {
                            pluginListContainer.style.display = 'block';
                            // 应用滚动条防护
                            pluginListContainer.style.overflow = 'visible';
                            pluginListContainer.style.maxHeight = 'none';
                            pluginListContainer.style.height = 'auto';
                        }
                        // 隐藏加载状态
                        const loading = document.getElementById('installed-loading');
                        if (loading) {
                            loading.style.display = 'none';
                        }
                    }
                } else if (type === 'available') {
                    if (installedContainer) {
                        installedContainer.style.display = 'none';
                        installedContainer.classList.remove('active');
                    }
                    if (availableContainer) {
                        availableContainer.style.display = 'block';
                        availableContainer.classList.add('active');
                        // 确保滚动条设置正确
                        availableContainer.style.overflow = 'visible';
                    }
                    if (this.availablePlugins.length === 0) {
                        this.loadAvailablePlugins();
                    }
                }

                console.log('=== showPluginTab END ===');
            }

            async loadInstalledPlugins() {
                console.log('=== loadInstalledPlugins START ===');

                // 检查是否已经加载过且缓存仍然有效（5分钟缓存）
                if (this.installedPlugins && this.installedPlugins.length > 0 && this.pluginCacheTime && (Date.now() - this.pluginCacheTime < 300000)) {
                    console.log('Using cached plugin data, skipping API call');

                    // 快速显示缓存的内容
                    const loading = document.getElementById('installed-loading');
                    const pluginList = document.getElementById('installed-plugin-list');

                    if (loading) {
                        loading.style.display = 'none';
                    }

                    if (pluginList) {
                        // 如果已经有缓存的HTML内容，直接显示
                        if (this.cachedPluginHTML && pluginList.innerHTML !== this.cachedPluginHTML) {
                            pluginList.innerHTML = this.cachedPluginHTML;
                        }
                        pluginList.style.display = 'block';
                        pluginList.style.visibility = 'visible';
                        pluginList.style.opacity = '1';
                        console.log('Cached content displayed instantly');
                    }
                    return;
                }

                const loading = document.getElementById('installed-loading');
                const pluginList = document.getElementById('installed-plugin-list');

                console.log('Loading element:', loading);
                console.log('Plugin list element:', pluginList);
                console.log('Backend URL:', this.backendUrl);

                // 显示加载状态
                if (loading) {
                    loading.style.display = 'flex';
                    console.log('Loading display set to flex');
                }

                if (pluginList) {
                    pluginList.style.display = 'none';
                    console.log('Plugin list hidden during loading');
                }

                try {
                    console.log('Calling fetchInstalledPlugins...');
                    const pluginData = await this.fetchInstalledPlugins();
                    console.log('=== PLUGIN DATA RECEIVED ===');
                    console.log('Plugin data type:', typeof pluginData);
                    console.log('Plugin data length:', pluginData?.length);
                    console.log('Plugin data:', pluginData);

                    this.installedPlugins = pluginData;
                    this.pluginCacheTime = Date.now(); // 设置缓存时间

                    console.log('Calling renderInstalledPlugins...');
                    this.renderInstalledPlugins(pluginData);

                    // 隐藏加载状态，显示插件列表
                    if (loading) {
                        loading.style.display = 'none';
                        console.log('Loading hidden');
                    }
                    if (pluginList) {
                        pluginList.style.display = 'block';
                        pluginList.style.visibility = 'visible';
                        pluginList.style.opacity = '1';
                        console.log('Plugin list shown');
                        console.log('Plugin list final display:', window.getComputedStyle(pluginList).display);
                    }

                    console.log('=== loadInstalledPlugins SUCCESS ===');

                } catch (error) {
                    console.error('=== loadInstalledPlugins ERROR ===');
                    console.error('Failed to load installed plugins:', error);
                    if (loading) {
                        loading.innerHTML = `
                            <div style="color: #ff4757; text-align: center; padding: 20px;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>加载已安装插件失败</p>
                                <p style="font-size: 0.9em; opacity: 0.8;">${error.message}</p>
                                <button onclick="launcherInstance.loadInstalledPlugins()" class="btn btn-primary">重试</button>
                            </div>
                        `;
                    }
                }
            }

            async fetchInstalledPlugins() {
                try {
                    console.log('Fetching plugins from:', `${this.backendUrl}/nodes/installed`);
                    const response = await fetch(`${this.backendUrl}/nodes/installed`);
                    console.log('Plugin API response status:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();
                    console.log('Plugin API response data:', result);
                    console.log('Result status:', result.status);
                    console.log('Nodes array:', result.nodes);
                    console.log('Nodes count:', result.nodes?.length || 0);

                    if (result.status === 'success') {
                        const mappedNodes = result.nodes.map(node => {
                            console.log('Mapping node:', node.name, node);
                            return {
                                name: node.name,
                                version: node.version || '未知',
                                date: node.git_date || node.date || 'N/A',
                                enabled: node.status === 'enabled',
                                url: node.repo_url || `https://github.com/search?q=${encodeURIComponent(node.name)}`,
                                hasUpdate: node.hasUpdate || false,
                                description: node.description || '无描述',
                                fileCount: node.fileCount || 0,
                                author: node.author || '未知',
                                status: node.status, // 保持原始状态信息
                                isLatestVersion: node.isLatestVersion !== false // 默认认为是最新版本
                            };
                        });
                        console.log('Mapped nodes:', mappedNodes);
                        return mappedNodes;
                    } else {
                        console.error('API returned error status:', result.status, result.message);
                        throw new Error(result.message || 'Failed to fetch plugins');
                    }
                } catch (error) {
                    console.error('Error fetching installed plugins:', error);
                    throw error;
                }
            }

            // 统一的插件卡片渲染函数
            renderPluginCard(plugin) {
                // 安全地处理所有可能包含特殊字符的字段
                const safeName = this.escapeHtml(plugin.name || '').replace(/'/g, "\\'");
                const safeDisplayName = this.escapeHtml(plugin.name || '未知插件');
                const safeVersion = this.escapeHtml(plugin.version || '未知');
                const safeAuthor = this.escapeHtml(plugin.author || '未知');
                const safeDate = this.escapeHtml(plugin.date || '未知');
                const safeDescription = this.escapeHtml(plugin.description || '暂无描述');

                const isEnabled = plugin.enabled === true || plugin.status === 'enabled';
                const statusColor = isEnabled ? '#28a745' : '#dc3545';
                const statusText = isEnabled ? '✓ 已启用' : '✗ 已禁用';
                const safeUrl = plugin.url || `https://github.com/search?q=${encodeURIComponent(plugin.name || '')}`;



                // 禁用/启用按钮的样式和文本
                const toggleButtonColor = isEnabled ? '#ffc107' : '#28a745';
                const toggleButtonText = isEnabled ? '禁用' : '启用';
                const toggleButtonIcon = isEnabled ? 'fa-pause' : 'fa-play';





                return `
                    <div data-plugin-name="${safeName}" style="background: rgba(255,255,255,0.05); padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 3px solid #00f5ff; width: 100%; box-sizing: border-box;">
                        <!-- 插件信息区域 -->
                        <div style="margin-bottom: 15px;">
                            <div style="margin-bottom: 10px;">
                                <strong style="color: #00f5ff; font-size: 18px; display: block; margin-bottom: 5px;">${safeDisplayName}</strong>
                                <span style="color: ${statusColor}; font-size: 13px; font-weight: bold;">${statusText}</span>
                            </div>
                            <div style="color: #ccc; font-size: 13px; margin-bottom: 8px; line-height: 1.4;">
                                <span style="margin-right: 20px;">版本: ${safeVersion}</span>
                                <span style="margin-right: 20px;">作者: ${safeAuthor}</span>
                                <span>更新: ${safeDate}</span>
                            </div>
                            <div style="color: #aaa; font-size: 12px; line-height: 1.5; margin-bottom: 15px;">
                                ${safeDescription}
                            </div>
                        </div>

                        <!-- 操作按钮区域 -->
                        <div style="display: flex; gap: 10px; flex-wrap: wrap; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.1);">
                            <button onclick="window.open('${safeUrl}', '_blank')" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; align-items: center; gap: 5px;">
                                <i class="fas fa-external-link-alt"></i> 主页
                            </button>
                            <button onclick="togglePlugin('${safeName}', ${isEnabled ? 'true' : 'false'})" style="padding: 8px 16px; background: ${toggleButtonColor}; color: ${isEnabled ? 'black' : 'white'}; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; align-items: center; gap: 5px;">
                                <i class="fas ${toggleButtonIcon}"></i> ${toggleButtonText}
                            </button>
                            <button onclick="uninstallPlugin('${safeName}')" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; align-items: center; gap: 5px;">
                                <i class="fas fa-trash"></i> 卸载
                            </button>
                            <button onclick="showPluginVersions('${safeName}')" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; align-items: center; gap: 5px;">
                                <i class="fas fa-code-branch"></i> 切换
                            </button>
                            <button onclick="updatePlugin('${safeName}')" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; align-items: center; gap: 5px;">
                                <i class="fas fa-sync-alt"></i> 更新
                            </button>
                        </div>
                    </div>
                `;
            }

            // HTML转义函数，防止特殊字符破坏HTML结构
            escapeHtml(text) {
                if (!text) return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            renderInstalledPlugins(plugins) {
                console.log('=== renderInstalledPlugins START ===');
                console.log('Plugins parameter:', plugins);
                console.log('Plugins type:', typeof plugins);
                console.log('Plugins length:', plugins?.length || 0);

                // 在插件管理页面的容器内渲染，而不是替换main-content
                const pluginListContainer = document.querySelector('#installed-plugin-list');
                console.log('Plugin list container element:', pluginListContainer);

                if (!pluginListContainer) {
                    console.error('Plugin list container not found!');
                    return;
                }

                // 应用滚动条防护设置
                pluginListContainer.style.overflow = 'visible';
                pluginListContainer.style.maxHeight = 'none';
                pluginListContainer.style.height = 'auto';

                if (!plugins || plugins.length === 0) {
                    console.log('No plugins to render, showing empty state');
                    this.renderEmptyStateInContainer(pluginListContainer);
                    return;
                }

                // 预设容器高度，避免渲染过程中的高度跳跃
                const estimatedItemHeight = 150; // 已安装插件项高度估计
                const estimatedTotalHeight = plugins.length * estimatedItemHeight;
                pluginListContainer.style.minHeight = `${estimatedTotalHeight}px`;

                // 在插件容器内生成插件列表
                console.log('Generating plugin list in container...');
                this.generatePluginListInContainer(pluginListContainer, plugins);

                // 确保插件列表容器可见
                pluginListContainer.classList.add('show');
                pluginListContainer.style.display = 'block';

                // 重置容器高度为自动
                requestAnimationFrame(() => {
                    pluginListContainer.style.minHeight = 'auto';
                });

                // 缓存渲染后的HTML内容，用于快速显示
                this.cachedPluginHTML = pluginListContainer.innerHTML;

                console.log('Plugin list container made visible with .show class');
                console.log('HTML content cached for future use');
                console.log('=== renderInstalledPlugins COMPLETE ===');
            }

            renderEmptyStateInContainer(container) {
                container.innerHTML = `
                    <div style="padding: 40px; text-align: center; color: white;">
                        <i class="fas fa-puzzle-piece" style="font-size: 48px; color: #00f5ff; margin-bottom: 20px;"></i>
                        <h3 style="color: #00f5ff;">暂无已安装插件</h3>
                        <p style="color: #ccc;">您还没有安装任何自定义节点插件</p>
                    </div>
                `;
                console.log('Empty state rendered in container');
            }

            generatePluginListInContainer(container, plugins) {
                console.log('=== generatePluginListInContainer START ===');

                try {
                    // 清空容器
                    container.innerHTML = '';

                    // 使用DOM操作而不是innerHTML，避免HTML解析问题
                    plugins.forEach((plugin, index) => {
                        console.log(`Creating DOM for plugin ${index + 1}/${plugins.length}: ${plugin.name}`);

                        try {
                            const pluginElement = this.createPluginCardElement(plugin);
                            container.appendChild(pluginElement);
                        } catch (error) {
                            console.error(`Error creating plugin card for ${plugin.name}:`, error);
                            // 创建一个错误提示卡片
                            const errorCard = document.createElement('div');
                            errorCard.style.cssText = 'background: rgba(255,0,0,0.1); padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 3px solid #ff0000; color: #ff6666;';
                            errorCard.textContent = `插件 "${plugin.name || '未知'}" 渲染失败`;
                            container.appendChild(errorCard);
                        }
                    });

                    console.log('Plugin list generated in container successfully using DOM operations');

                } catch (error) {
                    console.error('Error generating plugin list in container:', error);
                    container.innerHTML = `
                        <div style="padding: 20px; text-align: center; color: #dc3545;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                            <h3>插件列表生成失败</h3>
                            <p>错误信息: ${error.message}</p>
                        </div>
                    `;
                }
            }





            generateFullPluginList(pluginManagement, plugins) {
                console.log('=== generateFullPluginList START ===');

                console.log('Starting to generate HTML for plugins...');
                try {
                    // 生成简化的插件管理界面（使用统一模板）
                    const pluginItems = plugins.map((plugin, index) => {
                        console.log(`Generating HTML for plugin ${index + 1}/${plugins.length}: ${plugin.name}`);
                        return this.renderPluginCard(plugin);
                    }).join('');

                    // 生成完整的插件管理页面
                    const fullHTML = `
                        <div style="padding: 20px; color: white; /* 移除overflow-y: auto，使用页面级别滚动条 */">
                            <div style="display: flex; align-items: center; margin-bottom: 25px; padding-bottom: 15px; border-bottom: 1px solid #333;">
                                <i class="fas fa-puzzle-piece" style="color: #00f5ff; font-size: 24px; margin-right: 15px;"></i>
                                <h2 style="color: #00f5ff; margin: 0; font-size: 24px;">插件管理</h2>
                                <div style="margin-left: auto; color: #ccc; font-size: 14px;">
                                    共 ${plugins.length} 个插件
                                </div>
                            </div>

                            <div style="background: rgba(0, 245, 255, 0.1); border: 1px solid #00f5ff; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                                <h3 style="color: #00f5ff; margin: 0 0 15px 0; font-size: 18px;">
                                    <i class="fas fa-check-circle" style="margin-right: 10px;"></i>
                                    已安装插件
                                </h3>
                                <div style="/* 移除高度限制，使用页面级别滚动条 */">
                                    ${pluginItems}
                                </div>
                            </div>

                            <div style="text-align: center; color: #666; padding: 20px; border-top: 1px solid #333;">
                                <p style="margin: 0; font-size: 12px;">
                                    插件管理功能正在完善中，更多功能即将推出
                                </p>
                            </div>
                        </div>
                    `;

                    console.log('HTML generation complete, length:', fullHTML.length);
                    console.log('Setting plugin management innerHTML...');

                    pluginManagement.innerHTML = fullHTML;

                    console.log('Plugin management innerHTML set successfully');
                    console.log('Plugin management size:', pluginManagement.getBoundingClientRect());

                    console.log('=== generateFullPluginList COMPLETE ===');

                } catch (error) {
                    console.error('Error generating plugin HTML:', error);
                    console.error('Error stack:', error.stack);
                    pluginManagement.innerHTML = `
                        <div style="padding: 40px; text-align: center; color: white;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #dc3545; margin-bottom: 20px;"></i>
                            <h3 style="color: #dc3545;">渲染插件列表时出错</h3>
                            <p style="color: #ccc;">错误信息: ${error.message}</p>
                            <button onclick="location.reload()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                                重新加载页面
                            </button>
                        </div>
                    `;
                }
            }

            async loadAvailablePlugins(forceRefresh = false) {
                console.log('=== loadAvailablePlugins START ===');
                const loading = document.getElementById('available-loading');
                const pluginList = document.getElementById('available-plugin-list');

                console.log('Available loading element:', loading);
                console.log('Available plugin list element:', pluginList);

                if (!loading || !pluginList) {
                    console.error('Available plugins DOM elements not found!');
                    return;
                }

                // 显示加载状态
                if (loading) {
                    loading.style.display = 'flex';
                }

                try {
                    console.log('Fetching available plugins from API...');
                    const pluginData = await this.fetchAvailablePlugins(forceRefresh);
                    console.log('Available plugins data received:', pluginData);

                    this.availablePlugins = pluginData;
                    this.allAvailablePlugins = [...pluginData]; // 保存完整列表用于搜索

                    // 渲染可用插件列表
                    this.renderAvailablePlugins(pluginData);

                    // 隐藏加载状态
                    if (loading) {
                        loading.style.display = 'none';
                    }

                    // 强制显示插件列表
                    pluginList.style.display = 'block';
                    pluginList.style.visibility = 'visible';
                    pluginList.style.opacity = '1';

                    console.log('Available plugins loaded successfully');

                    // 异步获取真实的GitHub star数（仅对前50个插件）
                    this.loadRealGitHubStars();

                } catch (error) {
                    console.error('Failed to load available plugins:', error);

                    // 显示错误信息
                    pluginList.innerHTML = `
                        <div style="padding: 20px; text-align: center; color: #ff4757; background: rgba(255,71,87,0.1); border: 2px solid #ff4757; margin: 20px; border-radius: 10px;">
                            <h3>❌ 加载可用插件失败</h3>
                            <p>无法从ComfyUI Manager获取插件列表</p>
                            <p style="font-size: 14px; opacity: 0.8;">错误信息: ${error.message}</p>
                            <button onclick="launcherInstance.loadAvailablePlugins()" style="background: #ff4757; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 10px; cursor: pointer;">
                                重试
                            </button>
                            <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                                <p style="font-size: 14px; opacity: 0.8;">
                                    💡 提示：请确保ComfyUI Manager已正确安装<br>
                                    或者手动将插件文件夹放入 custom_nodes 目录
                                </p>
                            </div>
                        </div>
                    `;

                    // 强制显示插件列表
                    pluginList.style.display = 'block';
                    pluginList.style.visibility = 'visible';
                    pluginList.style.opacity = '1';

                    // 隐藏加载状态
                    if (loading) {
                        loading.style.display = 'none';
                    }
                }

                console.log('=== loadAvailablePlugins END ===');
            }



            // 辅助方法：检查插件是否已安装
            isPluginInstalled(pluginName) {
                return this.installedPlugins.some(plugin =>
                    plugin.name.toLowerCase() === pluginName.toLowerCase()
                );
            }

            async fetchAvailablePlugins(forceRefresh = false) {
                try {
                    const url = `${this.backendUrl}/nodes/available`;
                    const params = new URLSearchParams();
                    if (forceRefresh) {
                        params.append('force_refresh', 'true');
                    }
                    params.append('_t', Date.now().toString());

                    const fullUrl = `${url}?${params}`;
                    console.log('Fetching available plugins from:', fullUrl);

                    const response = await fetch(fullUrl, {
                        headers: forceRefresh ? {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0'
                        } : {}
                    });
                    console.log('Available plugins API response status:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log('Available plugins API response data:', data);

                    if (data.status === 'success') {
                        // 转换后端数据格式为前端期望的格式
                        const plugins = data.nodes.map(node => {
                            console.log('Processing node:', node); // 调试日志
                            return {
                                name: node.title || node.name,
                                author: node.author || '未知',
                                stars: node.stars || 0, // 使用后端返回的真实star数
                                url: node.reference || node.repo_url || `https://github.com/search?q=${encodeURIComponent(node.title || node.name)}`,
                                category: node.category || 'other', // 使用后端返回的分类
                                description: node.description || "无描述",
                                installed: node.is_installed || false, // 使用后端返回的安装状态
                                tags: node.tags || [],
                                files: node.files || [],
                                install_type: node.install_type || 'git-clone'
                            };
                        });

                        console.log(`Processed ${plugins.length} available plugins`);
                        return plugins;
                    } else {
                        throw new Error(data.message || 'Failed to fetch available plugins');
                    }
                } catch (error) {
                    console.error('Error fetching available plugins:', error);

                    // 如果API失败，返回示例数据作为后备
                    console.log('Falling back to example data');
                    return [
                        {
                            name: "ComfyUI-Manager",
                            author: "ltdrdata",
                            stars: 8234,
                            url: "https://github.com/ltdrdata/ComfyUI-Manager",
                            category: "utils",
                            description: "ComfyUI extension manager"
                        },
                        {
                            name: "ComfyUI-Custom-Scripts",
                            author: "pythongosssss",
                            stars: 1567,
                            url: "https://github.com/pythongosssss/ComfyUI-Custom-Scripts",
                            category: "ui",
                            description: "Custom UI scripts and enhancements"
                        }
                    ];
                }
            }



            renderAvailablePlugins(plugins) {
                console.log('=== renderAvailablePlugins START ===');
                console.log('Plugins to render:', plugins.length);

                const container = document.getElementById('available-plugin-list');
                if (!container) {
                    console.error('Available plugin list container not found!');
                    return;
                }

                if (plugins.length === 0) {
                    container.innerHTML = `
                        <div style="grid-column: 1 / -1; padding: 60px 40px; text-align: center; color: var(--text-secondary);">
                            <div style="background: rgba(26, 26, 46, 0.3); border-radius: 20px; padding: 40px; border: 1px solid rgba(0, 245, 255, 0.1);">
                                <i class="fas fa-search" style="font-size: 64px; margin-bottom: 24px; opacity: 0.3; color: var(--neon-glow);"></i>
                                <h3 style="color: var(--text-primary); margin-bottom: 12px; font-size: 20px;">没有找到匹配的插件</h3>
                                <p style="font-size: 14px; opacity: 0.8;">尝试调整搜索条件或选择不同的分类</p>
                            </div>
                        </div>
                    `;
                    return;
                }

                // 预先设置容器最小高度，避免动态高度变化导致滚动条闪烁
                const estimatedItemHeight = 120; // 每个插件项的估计高度
                const estimatedTotalHeight = plugins.length * estimatedItemHeight;
                container.style.minHeight = `${estimatedTotalHeight}px`;

                // 使用虚拟滚动优化，一次性渲染所有内容但延迟显示
                const allHTML = plugins.map(plugin => {
                    // 检查插件是否已安装
                    const backendInstalled = plugin.installed; // 后端检测结果
                    const frontendInstalled = this.isPluginInstalled(plugin.name); // 前端检测结果

                    // 优先使用后端检测结果，前端作为备用
                    const isInstalled = backendInstalled || frontendInstalled;

                    const installButtonText = isInstalled ? '已安装' : '安装';
                    const installButtonClass = isInstalled ? 'installed' : 'install';
                    const installButtonDisabled = isInstalled ? 'disabled' : '';

                    return `
                        <div class="available-plugin-item" data-plugin="${plugin.name}" data-category="${plugin.category}">
                            <div class="available-plugin-info">
                                <!-- 主要信息区域 -->
                                <div class="plugin-main-info">
                                    <!-- 插件名称和作者 -->
                                    <div class="plugin-title-line">
                                        <span class="available-plugin-name" title="${plugin.name}">${plugin.name}</span>
                                        <span class="plugin-author">@${plugin.author}</span>
                                    </div>

                                    <!-- 插件描述 -->
                                    <div class="plugin-description" title="${plugin.description}">${plugin.description}</div>

                                    <!-- 底部信息行：分类、星标、主页、安装 -->
                                    <div class="plugin-bottom-line">
                                        <div class="plugin-left-info">
                                            <span class="plugin-category">${this.getCategoryDisplayName(plugin.category)}</span>
                                            <span class="plugin-stars">
                                                <i class="fas fa-star"></i> ${plugin.stars}
                                            </span>
                                        </div>
                                        <div class="plugin-right-actions">
                                            <button class="plugin-action-btn info"
                                                    onclick="launcherInstance.showPluginInfo('${plugin.name}', '${plugin.url}')"
                                                    title="查看插件详情">
                                                <i class="fas fa-info-circle"></i>
                                                主页
                                            </button>
                                            <button class="plugin-action-btn ${installButtonClass}"
                                                    onclick="launcherInstance.installAvailablePlugin('${plugin.name}', '${plugin.url}')"
                                                    ${installButtonDisabled}>
                                                <i class="fas ${isInstalled ? 'fa-check' : 'fa-download'}"></i>
                                                ${installButtonText}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                // 一次性设置所有内容，避免动态高度变化
                container.innerHTML = allHTML;

                // 重置容器高度为自动
                requestAnimationFrame(() => {
                    container.style.minHeight = 'auto';
                });

                console.log('Available plugins rendered successfully');
                console.log('=== renderAvailablePlugins END ===');
            }

            // 检查插件是否已安装（改进的匹配逻辑）
            isPluginInstalled(pluginName) {
                if (!this.installedPlugins || !pluginName) {
                    return false;
                }

                const searchName = pluginName.toLowerCase().trim();

                return this.installedPlugins.some(plugin => {
                    const installedName = plugin.name.toLowerCase();

                    // 精确匹配
                    if (installedName === searchName) {
                        return true;
                    }

                    // 移除常见前缀后匹配
                    const prefixes = ['comfyui-', 'comfyui_', 'comfy-', 'comfy_'];
                    for (const prefix of prefixes) {
                        if (installedName.startsWith(prefix) && searchName === installedName.substring(prefix.length)) {
                            return true;
                        }
                        if (searchName.startsWith(prefix) && installedName === searchName.substring(prefix.length)) {
                            return true;
                        }
                    }

                    // 包含匹配（双向，但要求长度足够避免误匹配）
                    if (searchName.length > 3 && installedName.length > 3) {
                        if (installedName.includes(searchName) || searchName.includes(installedName)) {
                            return true;
                        }
                    }

                    // 分隔符变体匹配
                    const normalizedInstalled = installedName.replace(/[-_\s]/g, '');
                    const normalizedSearch = searchName.replace(/[-_\s]/g, '');
                    if (normalizedInstalled === normalizedSearch) {
                        return true;
                    }

                    return false;
                });
            }

            // 获取分类显示名称
            getCategoryDisplayName(category) {
                const categoryNames = {
                    'image': '图像处理',
                    'video': '视频处理',
                    'audio': '音频处理',
                    'ai': 'AI模型',
                    '3d': '3D相关',
                    'tool': '工具类',
                    'other': '其他'
                };
                return categoryNames[category] || '其他';
            }

            // 安装可用插件
            async installAvailablePlugin(pluginName, pluginUrl) {
                try {
                    console.log('Installing available plugin:', pluginName, 'from:', pluginUrl);

                    // 确认安装
                    const confirmed = await this.showCustomConfirm(
                        '确认安装插件',
                        `确定要安装插件 "${pluginName}" 吗？\n\n这将从远程仓库下载并安装插件。`
                    );
                    if (!confirmed) {
                        return;
                    }

                    this.showNotification(`正在安装插件 ${pluginName}...`, 'info');

                    // 调用真实的安装API
                    const response = await fetch(`${this.backendUrl}/nodes/install`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            node_id: pluginName,
                            repo_url: pluginUrl,
                            install_type: 'git-clone'
                        })
                    });

                    if (response.ok) {
                        const result = await response.json();

                        if (result.status === 'success') {
                            // 清除缓存并刷新插件列表
                            this.clearAllPluginCaches();

                            // 等待一下确保安装完成
                            await new Promise(resolve => setTimeout(resolve, 1500));

                            // 重新加载已安装插件列表
                            await this.loadInstalledPlugins();

                            // 强制重新加载可用插件列表（带强制刷新）
                            await this.loadAvailablePlugins(true);

                            // 检查ComfyUI运行状态
                            const comfyuiRunning = await this.checkComfyUIStatus();
                            const restartMessage = comfyuiRunning ? ' 请重启ComfyUI以使更改生效。' : '';

                            this.showNotification(`✅ 插件 ${pluginName} 安装成功！${restartMessage}`, 'success', 'plugin');
                        } else {
                            this.showNotification(`❌ 安装失败：${result.message}`, 'error');
                        }
                    } else {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }

                } catch (error) {
                    console.error('Install plugin failed:', error);
                    this.showNotification(`❌ 插件安装失败: ${error.message}`, 'error');
                }
            }

            // 显示插件详情
            showPluginInfo(pluginName, pluginUrl) {
                console.log('Opening plugin homepage for:', pluginName, 'URL:', pluginUrl);

                // 直接打开插件主页，不需要弹窗提醒
                if (pluginUrl && pluginUrl !== '#') {
                    window.open(pluginUrl, '_blank');
                } else {
                    this.showNotification(`插件 ${pluginName} 的主页信息不可用`, 'warning');
                }
            }

            // 异步加载真实的GitHub star数
            async loadRealGitHubStars() {
                if (!this.allAvailablePlugins || this.allAvailablePlugins.length === 0) {
                    return;
                }

                console.log('Starting to load real GitHub stars for top plugins...');

                // 只对前50个插件获取真实star数，避免API限制
                const topPlugins = this.allAvailablePlugins.slice(0, 50);
                let updatedCount = 0;

                for (const plugin of topPlugins) {
                    try {
                        // 提取GitHub仓库信息
                        const githubUrl = plugin.url;
                        if (!githubUrl || !githubUrl.includes('github.com')) {
                            continue;
                        }

                        // 解析仓库owner和name
                        const urlParts = githubUrl.replace('.git', '').split('/');
                        if (urlParts.length < 5) {
                            continue;
                        }

                        const owner = urlParts[urlParts.length - 2];
                        const repo = urlParts[urlParts.length - 1];

                        // 调用后端API获取star数
                        const response = await fetch(`${this.backendUrl}/nodes/github-stars/${owner}/${repo}`);
                        if (response.ok) {
                            const result = await response.json();
                            if (result.status === 'success' && result.stars > 0) {
                                // 更新插件的star数
                                plugin.stars = result.stars;
                                updatedCount++;

                                console.log(`Updated ${plugin.name}: ${result.stars} stars`);

                                // 每更新10个插件就重新渲染一次，减少频繁渲染
                                if (updatedCount % 10 === 0) {
                                    this.sortPluginsByStars(this.allAvailablePlugins);
                                    this.renderAvailablePlugins(this.availablePlugins);
                                }
                            }
                        }

                        // 小延迟避免API限制
                        await new Promise(resolve => setTimeout(resolve, 100));

                    } catch (error) {
                        console.error(`Error loading stars for ${plugin.name}:`, error);
                    }
                }

                // 最终重新排序和渲染
                if (updatedCount > 0) {
                    console.log(`Updated ${updatedCount} plugins with real GitHub stars`);
                    this.sortPluginsByStars(this.allAvailablePlugins);
                    this.renderAvailablePlugins(this.availablePlugins);
                }
            }

            // 自定义确认对话框
            showCustomConfirm(title, message) {
                return new Promise((resolve) => {
                    // 创建模态框
                    const modal = document.createElement('div');
                    modal.className = 'custom-confirm-modal';
                    modal.innerHTML = `
                        <div class="custom-confirm-content">
                            <div class="custom-confirm-header">
                                <h3>${title}</h3>
                            </div>
                            <div class="custom-confirm-body">
                                <p>${message.replace(/\n/g, '<br>')}</p>
                            </div>
                            <div class="custom-confirm-footer">
                                <button class="custom-confirm-btn cancel">取消</button>
                                <button class="custom-confirm-btn confirm">确定</button>
                            </div>
                        </div>
                    `;

                    // 添加到页面
                    document.body.appendChild(modal);

                    // 绑定事件
                    const cancelBtn = modal.querySelector('.cancel');
                    const confirmBtn = modal.querySelector('.confirm');

                    const cleanup = () => {
                        document.body.removeChild(modal);
                    };

                    cancelBtn.onclick = () => {
                        // 播放点击音效
                        if (window.audioManager) {
                            audioManager.play('click');
                        }
                        cleanup();
                        resolve(false);
                    };

                    confirmBtn.onclick = () => {
                        // 播放点击音效
                        if (window.audioManager) {
                            audioManager.play('click');
                        }
                        cleanup();
                        resolve(true);
                    };

                    // 点击背景关闭
                    modal.onclick = (e) => {
                        if (e.target === modal) {
                            cleanup();
                            resolve(false);
                        }
                    };

                    // ESC键关闭
                    const handleKeydown = (e) => {
                        if (e.key === 'Escape') {
                            cleanup();
                            resolve(false);
                            document.removeEventListener('keydown', handleKeydown);
                        }
                    };
                    document.addEventListener('keydown', handleKeydown);
                });
            }

            // 搜索和过滤功能（智能搜索+排序）
            filterAvailablePlugins(searchTerm) {
                if (!this.allAvailablePlugins) return;

                if (!searchTerm || searchTerm.trim() === '') {
                    // 如果搜索词为空，显示所有插件（按star数排序）
                    this.availablePlugins = [...this.allAvailablePlugins];
                    this.sortPluginsByStars(this.availablePlugins);
                    this.renderAvailablePlugins(this.availablePlugins);
                    return;
                }

                const searchText = searchTerm.toLowerCase().trim();

                const filtered = this.allAvailablePlugins.filter(plugin => {
                    // 插件名称匹配（支持部分匹配）
                    const nameMatch = plugin.name.toLowerCase().includes(searchText);

                    // 作者匹配
                    const authorMatch = plugin.author.toLowerCase().includes(searchText);

                    // 描述匹配
                    const descMatch = plugin.description.toLowerCase().includes(searchText);

                    // 分类匹配
                    const categoryMatch = this.getCategoryDisplayName(plugin.category).toLowerCase().includes(searchText);

                    // 支持连字符和下划线的模糊匹配
                    const normalizedPluginName = plugin.name.toLowerCase().replace(/[-_]/g, '');
                    const normalizedSearchText = searchText.replace(/[-_]/g, '');
                    const fuzzyNameMatch = normalizedPluginName.includes(normalizedSearchText);

                    return nameMatch || authorMatch || descMatch || categoryMatch || fuzzyNameMatch;
                });

                // 按相似性和star数排序
                this.sortPluginsByRelevanceAndStars(filtered, searchText);

                console.log(`Search "${searchTerm}": ${filtered.length} results from ${this.allAvailablePlugins.length} total plugins`);

                this.availablePlugins = filtered;
                this.renderAvailablePlugins(filtered);
            }

            // 计算搜索相似性得分
            calculateRelevanceScore(plugin, searchText) {
                const pluginName = plugin.name.toLowerCase();
                const searchLower = searchText.toLowerCase();

                // 精确匹配得分最高
                if (pluginName === searchLower) return 100;

                // 开头匹配得分较高
                if (pluginName.startsWith(searchLower)) return 90;

                // 包含完整搜索词
                if (pluginName.includes(searchLower)) return 80;

                // 模糊匹配（去除分隔符）
                const normalizedName = pluginName.replace(/[-_\s]/g, '');
                const normalizedSearch = searchLower.replace(/[-_\s]/g, '');
                if (normalizedName.includes(normalizedSearch)) return 70;

                // 作者匹配
                if (plugin.author.toLowerCase().includes(searchLower)) return 60;

                // 描述匹配
                if (plugin.description.toLowerCase().includes(searchLower)) return 50;

                // 分类匹配
                if (this.getCategoryDisplayName(plugin.category).toLowerCase().includes(searchLower)) return 40;

                return 0;
            }

            // 按相似性和star数排序
            sortPluginsByRelevanceAndStars(plugins, searchText) {
                plugins.sort((a, b) => {
                    const scoreA = this.calculateRelevanceScore(a, searchText);
                    const scoreB = this.calculateRelevanceScore(b, searchText);

                    // 首先按相似性排序
                    if (scoreA !== scoreB) {
                        return scoreB - scoreA;
                    }

                    // 相似性相同时按star数排序
                    if (a.stars !== b.stars) {
                        return b.stars - a.stars;
                    }

                    // star数也相同时按名称排序
                    return a.name.localeCompare(b.name);
                });
            }

            // 按star数排序
            sortPluginsByStars(plugins) {
                plugins.sort((a, b) => {
                    // 首先按star数排序（降序）
                    if (a.stars !== b.stars) {
                        return b.stars - a.stars;
                    }

                    // star数相同时按名称排序
                    return a.name.localeCompare(b.name);
                });
            }

            filterPluginsByCategory(category) {
                if (!this.allAvailablePlugins) return;

                let filtered;
                if (category) {
                    filtered = this.allAvailablePlugins.filter(plugin => plugin.category === category);
                    console.log(`Category filter "${category}": ${filtered.length} results from ${this.allAvailablePlugins.length} total plugins`);
                } else {
                    filtered = [...this.allAvailablePlugins];
                    console.log(`Showing all plugins: ${filtered.length} total`);
                }

                // 按star数排序
                this.sortPluginsByStars(filtered);

                this.availablePlugins = filtered;
                this.renderAvailablePlugins(filtered);

                // 清除搜索框
                const searchInput = document.getElementById('plugin-search');
                if (searchInput) {
                    searchInput.value = '';
                }
            }



            async updatePlugin(pluginName) {
                try {
                    console.log('Updating plugin:', pluginName);

                    // 显示自定义确认对话框
                    const confirmed = await this.showCustomConfirm(
                        '确认更新插件',
                        `确定要更新插件 "${pluginName}" 到最新版本吗？\n\n这将从远程仓库拉取最新代码。`
                    );
                    if (!confirmed) {
                        return;
                    }

                    this.showNotification(`正在更新插件 ${pluginName}...`, 'info');

                    // 调用真实的更新API
                    const response = await fetch(`${this.backendUrl}/api/plugins/update`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ plugin_name: pluginName })
                    });

                    if (response.ok) {
                        const result = await response.json();

                        if (result.status === 'success') {
                            // 清除特定插件的版本缓存
                            this.clearPluginVersionCache(pluginName);

                            // 标记该插件刚刚更新过，下次打开版本弹窗时需要强制刷新
                            if (!this.recentlyUpdatedPlugins) {
                                this.recentlyUpdatedPlugins = new Set();
                            }
                            this.recentlyUpdatedPlugins.add(pluginName);

                            // 等待一下确保后端缓存已清除
                            await new Promise(resolve => setTimeout(resolve, 800));

                            // 强制清除缓存并刷新插件列表
                            await this.refreshPluginListAndScrollToPlugin(pluginName);

                            const updateMessage = result.updated ?
                                `插件 ${pluginName} 更新成功！` :
                                `插件 ${pluginName} 已经是最新版本。`;

                            // 检查ComfyUI运行状态
                            const comfyuiRunning = await this.checkComfyUIStatus();
                            const restartMessage = comfyuiRunning ? ' 请重启ComfyUI以使更改生效。' : '';

                            this.showNotification(`✅ ${updateMessage}${restartMessage}`, 'success');
                        } else {
                            this.showNotification(`❌ 更新失败：${result.message}`, 'error');
                        }
                    } else {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }

                } catch (error) {
                    console.error('Update plugin failed:', error);
                    this.showNotification(`❌ 插件更新失败: ${error.message}`, 'error');
                }
            }

            async showPluginVersions(pluginName) {
                this.currentVersionSwitchPlugin = pluginName;
                console.log('Showing versions for plugin:', pluginName);

                const modal = document.getElementById('version-switch-modal');
                console.log('Modal element found:', !!modal);

                if (!modal) {
                    console.error('Version switch modal not found!');
                    return;
                }

                // 更新弹窗标题
                const header = modal.querySelector('.version-switch-header h3');
                if (header) {
                    header.innerHTML = `<i class="fas fa-code-branch"></i> 选择 ${pluginName} 的版本`;
                }

                // 显示加载状态
                const container = document.getElementById('version-switch-list');
                if (container) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #ccc;">
                            <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i>
                            <div>正在获取版本信息...</div>
                        </div>
                    `;
                }

                // 显示模态框
                modal.style.display = 'flex';
                modal.classList.add('show');
                console.log('Modal display and show class applied');

                try {
                    // 检查是否需要强制刷新版本信息
                    // 如果插件刚刚更新过，需要强制刷新以获取最新的当前版本信息
                    const forceRefresh = this.recentlyUpdatedPlugins && this.recentlyUpdatedPlugins.has(pluginName);
                    if (forceRefresh) {
                        console.log(`Force refreshing version info for recently updated plugin: ${pluginName}`);
                        this.recentlyUpdatedPlugins.delete(pluginName); // 清除标记
                    }

                    // 异步获取版本历史
                    const versions = await this.generatePluginVersions(pluginName, forceRefresh);

                    // 渲染版本列表
                    this.renderPluginVersions(versions);
                } catch (error) {
                    console.error('Error loading plugin versions:', error);
                    if (container) {
                        container.innerHTML = `
                            <div style="text-align: center; padding: 40px; color: #dc3545;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                                <div>获取版本信息失败</div>
                                <div style="font-size: 12px; margin-top: 10px;">${error.message}</div>
                            </div>
                        `;
                    }
                }
            }

            renderPluginVersions(versions) {
                const container = document.getElementById('version-switch-list');
                if (!container) {
                    console.error('Version switch list container not found!');
                    return;
                }
                
                const html = versions.map((version, index) => {
                    // 当前版本标签（不使用背景高亮）
                    const currentBadge = version.current ? '<span style="color: #28a745; font-size: 11px; margin-left: 8px; font-weight: bold;">✓ 当前</span>' : '';

                    // 判断是否是更新版本（在当前版本之前的版本）
                    const currentIndex = versions.findIndex(v => v.current);
                    const isNewer = currentIndex > -1 && index < currentIndex;
                    const newerBadge = isNewer ? '<span style="color: #ff6b35; font-size: 11px; margin-left: 8px;">🔥 更新</span>' : '';

                    // 最新版本标记（第一个版本且不是当前版本）
                    const isLatest = index === 0 && !version.current;
                    const latestBadge = isLatest ? '<span style="color: #00f5ff; font-size: 11px; margin-left: 8px; font-weight: bold; text-shadow: 0 0 3px rgba(0, 245, 255, 0.5);">⭐ 最新</span>' : '';

                    // 版本类型标识
                    const typeIcon = {
                        'tag': '🏷️',
                        'branch': '🌿',
                        'remote_branch': '🌐',
                        'commit': '📝'
                    };
                    const typeLabel = typeIcon[version.type] || '📝';

                    return `
                        <div class="version-item ${isNewer ? 'newer' : ''}" data-version="${version.version}" style="background: ${version.current ? 'rgba(40, 167, 69, 0.1)' : 'rgba(255,255,255,0.05)'}; border-left: ${version.current ? '3px solid #28a745' : '3px solid transparent'}; padding: 15px; margin: 8px 0; border-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                            <div class="version-info" style="flex: 1;">
                                <div class="version-number" style="color: #fff; font-size: 14px; margin-bottom: 5px;">
                                    ${typeLabel} ${version.version}${currentBadge}${latestBadge}${newerBadge}
                                </div>
                                <div class="version-date" style="color: #ccc; font-size: 12px;">${version.date}</div>
                                <div class="version-description" style="color: #aaa; font-size: 11px; margin-top: 4px;">
                                    ${version.description || `${version.type}: ${version.message || '无描述'}`}
                                </div>
                            </div>
                            <button class="version-switch-btn ${version.current ? 'current' : ''} ${isNewer ? 'newer' : ''}"
                                    onclick="if(window.audioManager) audioManager.play('click'); switchPluginVersion('${version.version}')"
                                    ${version.current ? 'disabled' : ''}
                                    style="min-width: 100px; padding: 8px 16px; border: none; border-radius: 4px; cursor: ${version.current ? 'not-allowed' : 'pointer'}; background: ${version.current ? '#6c757d' : (isNewer ? '#28a745' : '#007bff')}; color: white; font-size: 12px; margin-left: 15px;">
                                <i class="fas fa-${version.current ? 'check' : (isNewer ? 'arrow-up' : 'download')}"></i>
                                ${version.current ? '当前版本' : (isNewer ? '升级' : '切换')}
                            </button>
                        </div>
                    `;
                }).join('');
                
                container.innerHTML = html;

                // 获取模态框并显示
                const modal = document.getElementById('version-switch-modal');
                if (modal) {
                    modal.classList.add('show');
                    modal.style.display = 'flex';
                    console.log('Modal shown');
                } else {
                    console.error('Modal not found!');
                }
            }

            async generatePluginVersions(pluginName, forceRefresh = false) {
                // 获取插件的当前版本状态
                const currentPluginVersion = this.getCurrentPluginVersion(pluginName);

                try {
                    // 尝试获取真实的插件版本信息
                    const realVersions = await this.fetchPluginVersions(pluginName, forceRefresh);
                    if (realVersions && realVersions.length > 0) {
                        // 设置当前版本
                        realVersions.forEach(v => {
                            v.current = (v.version === currentPluginVersion);
                        });

                        // 如果没有找到当前版本，默认第一个为当前版本
                        if (!realVersions.some(v => v.current)) {
                            realVersions[0].current = true;
                        }

                        return realVersions;
                    }
                } catch (error) {
                    console.warn('Failed to fetch real plugin versions, using fallback:', error);
                }

                // 如果获取真实版本失败，使用改进的模拟数据
                const fallbackVersions = this.generateFallbackVersions(pluginName);

                // 设置当前版本
                fallbackVersions.forEach(v => {
                    v.current = (v.version === currentPluginVersion);
                });

                // 如果没有找到当前版本，默认第一个为当前版本
                if (!fallbackVersions.some(v => v.current)) {
                    fallbackVersions[0].current = true;
                }

                return fallbackVersions;
            }

            async fetchPluginVersions(pluginName, forceRefresh = false) {
                try {
                    // 调用后端API获取插件的Git版本历史
                    const url = `${this.backendUrl}/plugins/${encodeURIComponent(pluginName)}/versions`;
                    const params = new URLSearchParams();
                    if (forceRefresh) {
                        params.append('force_refresh', 'true');
                    }
                    params.append('_t', Date.now().toString());

                    const response = await fetch(`${url}?${params}`, {
                        headers: forceRefresh ? {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0'
                        } : {}
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.status === 'success') {
                            // 转换API返回的数据格式为前端需要的格式
                            return data.versions.map(v => ({
                                version: v.version,
                                date: v.date,
                                current: v.current,
                                description: `${v.type}: ${v.message} (${v.author})`,
                                commit: v.commit,
                                type: v.type,
                                author: v.author,
                                message: v.message
                            }));
                        } else {
                            console.error('API error:', data.message);
                            return null;
                        }
                    } else {
                        console.error('HTTP error:', response.status);
                        return null;
                    }
                } catch (error) {
                    console.error('Error fetching plugin versions:', error);
                    return null;
                }
            }

            generateFallbackVersions(pluginName) {
                // 改进的模拟数据，更接近真实情况
                const today = new Date();
                const formatDate = (daysAgo) => {
                    const date = new Date(today);
                    date.setDate(date.getDate() - daysAgo);
                    return date.toISOString().split('T')[0];
                };

                // 为不同插件生成更真实的版本号
                if (pluginName.includes('comfyui-manager')) {
                    return [
                        { version: "main", date: formatDate(0), current: false, description: "主分支 (最新开发版)" },
                        { version: "v0.51.0", date: formatDate(7), current: false, description: "稳定版本" },
                        { version: "v0.50.2", date: formatDate(14), current: false, description: "修复版本" },
                        { version: "v0.50.0", date: formatDate(21), current: false, description: "重大更新" },
                        { version: "v0.49.5", date: formatDate(35), current: false, description: "功能增强" }
                    ];
                } else if (pluginName.includes('Easy-Use')) {
                    return [
                        { version: "main", date: formatDate(0), current: false, description: "主分支 (最新开发版)" },
                        { version: "v3.2.1", date: formatDate(5), current: false, description: "稳定版本" },
                        { version: "v3.2.0", date: formatDate(12), current: false, description: "新功能" },
                        { version: "v3.1.8", date: formatDate(20), current: false, description: "优化版本" },
                        { version: "v3.1.5", date: formatDate(28), current: false, description: "修复版本" }
                    ];
                } else {
                    return [
                        { version: "main", date: formatDate(0), current: false, description: "主分支 (最新开发版)" },
                        { version: "latest-stable", date: formatDate(3), current: false, description: "最新稳定版" },
                        { version: "v1.0.0", date: formatDate(10), current: false, description: "正式版本" },
                        { version: "beta", date: formatDate(15), current: false, description: "测试版本" },
                        { version: "legacy", date: formatDate(30), current: false, description: "历史版本" }
                    ];
                }
            }

            getCurrentPluginVersion(pluginName) {
                // 初始化版本状态存储
                if (!this.pluginVersionStates) {
                    this.pluginVersionStates = {};
                }

                // 如果有存储的版本状态，返回它
                if (this.pluginVersionStates[pluginName]) {
                    return this.pluginVersionStates[pluginName];
                }

                // 否则返回默认的当前版本（第一个版本）
                if (pluginName.includes('comfyui-manager')) {
                    return "latest";
                } else if (pluginName.includes('Easy-Use')) {
                    return "latest";
                } else {
                    return "latest";
                }
            }

            async refreshPluginList() {
                try {
                    console.log('Refreshing plugin list...');

                    // 保存当前滚动位置
                    const mainContent = document.querySelector('.main-content');
                    const scrollTop = mainContent ? mainContent.scrollTop : 0;
                    console.log('Saving scroll position:', scrollTop);

                    // 重新获取插件列表
                    const response = await fetch(`${this.backendUrl}/nodes/installed`);
                    if (response.ok) {
                        const data = await response.json();

                        // 处理插件数据，确保状态格式正确
                        const plugins = (data.nodes || []).map(plugin => ({
                            ...plugin,
                            enabled: plugin.status === 'enabled' // 统一转换为布尔值
                        }));

                        // 更新插件数据
                        this.installedPlugins = plugins;

                        // 重新渲染插件列表
                        this.renderInstalledPlugins(this.installedPlugins);

                        // 恢复滚动位置
                        setTimeout(() => {
                            if (mainContent) {
                                mainContent.scrollTop = scrollTop;
                                console.log('Restored scroll position:', scrollTop);
                            }
                        }, 100); // 稍微延迟以确保DOM更新完成

                        console.log('Plugin list refreshed successfully, plugins:', plugins.length);
                        console.log('Plugin statuses:', plugins.map(p => ({ name: p.name, enabled: p.enabled, status: p.status })));
                    } else {
                        console.error('Failed to refresh plugin list:', response.status);
                    }
                } catch (error) {
                    console.error('Error refreshing plugin list:', error);
                }
            }

            // 彻底清除所有插件相关缓存
            clearAllPluginCaches() {
                console.log('Clearing all plugin caches...');

                // 清除前端缓存
                if (this.cache) {
                    const pluginCacheKeys = [
                        'installed_plugins',
                        'plugin_list',
                        'nodes_installed',
                        'plugin_data'
                    ];

                    pluginCacheKeys.forEach(key => {
                        if (this.cache.has(key)) {
                            this.cache.delete(key);
                            console.log(`Cleared cache: ${key}`);
                        }
                    });
                }

                // 清除实例变量缓存
                this.installedPlugins = null;
                this.pluginListCache = null;
                this.lastPluginUpdate = 0;

                console.log('All plugin caches cleared');
            }

            // 清除特定插件的版本缓存
            clearPluginVersionCache(pluginName) {
                console.log(`Clearing version cache for plugin: ${pluginName}`);

                // 清除版本状态缓存
                if (this.pluginVersionStates && this.pluginVersionStates[pluginName]) {
                    delete this.pluginVersionStates[pluginName];
                }

                // 清除可能的版本数据缓存
                if (this.cache) {
                    const versionCacheKey = `plugin_versions_${pluginName}`;
                    if (this.cache.has(versionCacheKey)) {
                        this.cache.delete(versionCacheKey);
                        console.log(`Cleared version cache: ${versionCacheKey}`);
                    }
                }

                console.log(`Version cache cleared for plugin: ${pluginName}`);
            }

            async refreshPluginListAndScrollToPlugin(pluginName) {
                try {
                    console.log('Refreshing plugin list and scrolling to plugin:', pluginName);

                    // 彻底清除所有相关缓存
                    this.clearAllPluginCaches();

                    // 强制后端重新扫描插件（添加强制刷新参数）
                    const response = await fetch(`${this.backendUrl}/nodes/installed?force_refresh=true&_t=${Date.now()}`, {
                        method: 'GET',
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();

                        // 处理插件数据，确保状态格式正确和版本信息映射
                        const plugins = (data.nodes || []).map(plugin => ({
                            name: plugin.name,
                            version: plugin.version || '未知',
                            date: plugin.git_date || plugin.date || 'N/A',
                            enabled: plugin.status === 'enabled',
                            url: plugin.repo_url || `https://github.com/search?q=${encodeURIComponent(plugin.name)}`,
                            hasUpdate: plugin.hasUpdate || false,
                            description: plugin.description || '无描述',
                            fileCount: plugin.fileCount || 0,
                            author: plugin.author || '未知',
                            status: plugin.status,
                            isLatestVersion: plugin.isLatestVersion !== false // 默认认为是最新版本
                        }));

                        // 更新插件数据
                        this.installedPlugins = plugins;

                        // 只更新插件列表部分，而不是整个页面
                        await this.updatePluginListOnly(plugins);

                        // 滚动到指定插件
                        setTimeout(() => {
                            console.log('About to scroll to plugin:', pluginName);
                            this.scrollToPlugin(pluginName);
                        }, 300); // 稍微延迟以确保DOM更新完成

                        console.log('Plugin list refreshed and scrolled to plugin successfully');
                    } else {
                        console.error('Failed to refresh plugin list:', response.status);
                    }
                } catch (error) {
                    console.error('Error refreshing plugin list:', error);
                }
            }

            async updatePluginListOnly(plugins) {
                try {
                    // 查找插件列表容器
                    const pluginListContainer = document.querySelector('#plugin-list-container');
                    if (!pluginListContainer) {
                        console.error('Plugin list container not found, falling back to full render');
                        this.renderInstalledPlugins(plugins);
                        return;
                    }

                    // 生成插件列表HTML（使用统一模板）
                    const pluginHTML = plugins.map(plugin => {
                        return this.renderPluginCard(plugin);
                    }).join('');

                    // 只更新插件列表部分
                    pluginListContainer.innerHTML = pluginHTML;
                    console.log('Plugin list container updated without affecting scroll position');

                } catch (error) {
                    console.error('Error updating plugin list only:', error);
                    // 如果出错，回退到完整渲染
                    this.renderInstalledPlugins(plugins);
                }
            }

            scrollToPlugin(pluginName) {
                try {
                    console.log('Scrolling to plugin:', pluginName);

                    // 查找插件元素
                    const mainContent = document.querySelector('.main-content');
                    if (!mainContent) {
                        console.error('Main content not found');
                        return;
                    }

                    // 查找包含插件名称的元素 - 使用更广泛的搜索
                    let targetElement = null;

                    // 方法1：通过插件名称查找
                    const allElements = mainContent.querySelectorAll('*');
                    for (const element of allElements) {
                        if (element.textContent && element.textContent.includes(pluginName)) {
                            // 找到包含插件名称的元素，向上查找插件容器
                            const container = element.closest('div[style*="background: rgba(255,255,255,0.05)"]') ||
                                            element.closest('div[style*="background:rgba(255,255,255,0.05)"]') ||
                                            element.closest('.plugin-item') ||
                                            element.closest('div[style*="border-left"]');
                            if (container) {
                                targetElement = container;
                                break;
                            }
                        }
                    }

                    // 方法2：如果没找到，尝试通过data属性查找
                    if (!targetElement) {
                        targetElement = mainContent.querySelector(`[data-plugin-name="${pluginName}"]`);
                    }

                    if (targetElement) {
                        console.log('Found target element:', targetElement);

                        // 计算滚动位置，让插件显示在视窗中央
                        const containerRect = mainContent.getBoundingClientRect();
                        const elementRect = targetElement.getBoundingClientRect();
                        const scrollTop = mainContent.scrollTop + elementRect.top - containerRect.top - (containerRect.height / 2) + (elementRect.height / 2);

                        // 平滑滚动到目标位置
                        const targetScrollTop = Math.max(0, scrollTop);
                        console.log('Scrolling to position:', targetScrollTop);

                        mainContent.scrollTo({
                            top: targetScrollTop,
                            behavior: 'smooth'
                        });

                        // 确保滚动完成后再应用高亮效果
                        setTimeout(() => {
                            console.log('Current scroll position after scroll:', mainContent.scrollTop);
                        }, 800);

                        // 添加高亮效果
                        const originalStyle = {
                            transition: targetElement.style.transition || '',
                            boxShadow: targetElement.style.boxShadow || '',
                            border: targetElement.style.border || '',
                            backgroundColor: targetElement.style.backgroundColor || '',
                            transform: targetElement.style.transform || ''
                        };

                        // 应用高亮效果
                        targetElement.style.transition = 'all 0.5s ease';
                        targetElement.style.boxShadow = '0 0 30px rgba(0, 245, 255, 0.8), inset 0 0 20px rgba(0, 245, 255, 0.2)';
                        targetElement.style.border = '2px solid #00f5ff';
                        targetElement.style.backgroundColor = 'rgba(0, 245, 255, 0.15)';
                        targetElement.style.transform = 'scale(1.02)';

                        console.log('Applied highlight effect to plugin:', pluginName);

                        // 3秒后移除高亮效果
                        setTimeout(() => {
                            if (targetElement) {
                                targetElement.style.transition = originalStyle.transition;
                                targetElement.style.boxShadow = originalStyle.boxShadow;
                                targetElement.style.border = originalStyle.border;
                                targetElement.style.backgroundColor = originalStyle.backgroundColor;
                                targetElement.style.transform = originalStyle.transform;
                                console.log('Removed highlight effect from plugin:', pluginName);
                            }
                        }, 3000);

                        console.log('Scrolled to plugin successfully with highlight');
                    } else {
                        console.warn('Plugin element not found:', pluginName);
                        console.log('Available elements in main content:', mainContent.children.length);

                        // 调试：列出所有可能的插件元素
                        const possibleElements = mainContent.querySelectorAll('div[style*="background"]');
                        console.log('Possible plugin elements:', possibleElements.length);
                        possibleElements.forEach((el, index) => {
                            console.log(`Element ${index}:`, el.textContent?.substring(0, 50));
                        });
                    }
                } catch (error) {
                    console.error('Error scrolling to plugin:', error);
                }
            }

            async checkComfyUIStatus() {
                try {
                    const response = await fetch(`${this.backendUrl}/comfyui/status`);
                    if (response.ok) {
                        const data = await response.json();
                        return data.status === 'running';
                    }
                } catch (error) {
                    console.error('Error checking ComfyUI status:', error);
                }
                return false;
            }

            // 测试函数 - 可以在控制台调用
            testScrollToPlugin(pluginName) {
                console.log('Testing scroll to plugin:', pluginName);
                this.scrollToPlugin(pluginName);
            }

            async switchPluginVersion(version) {
                try {
                    console.log(`Switching plugin ${this.currentVersionSwitchPlugin} to version:`, version);

                    // 获取当前版本
                    const currentVersion = this.getCurrentPluginVersion(this.currentVersionSwitchPlugin);

                    // 如果选择的是当前版本，不需要切换
                    if (version === currentVersion) {
                        alert(`${this.currentVersionSwitchPlugin} 已经是版本 ${version}，无需切换。`);
                        return;
                    }

                    // 直接切换，无需确认

                    // 显示切换进度
                    const container = document.getElementById('version-switch-list');
                    if (container) {
                        container.innerHTML = `
                            <div style="text-align: center; padding: 40px; color: #00f5ff;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i>
                                <div>正在切换版本...</div>
                                <div style="font-size: 12px; margin-top: 10px; color: #ccc;">
                                    ${this.currentVersionSwitchPlugin}: ${currentVersion} → ${version}
                                </div>
                            </div>
                        `;
                    }

                    try {
                        // 调用真实的版本切换API
                        const response = await fetch(`${this.backendUrl}/plugins/${encodeURIComponent(this.currentVersionSwitchPlugin)}/switch-version`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ version: version })
                        });

                        if (response.ok) {
                            const result = await response.json();

                            if (result.status === 'success') {
                                // 更新插件的当前版本状态
                                if (!this.pluginVersionStates) {
                                    this.pluginVersionStates = {};
                                }
                                this.pluginVersionStates[this.currentVersionSwitchPlugin] = version;

                                // 重新生成版本列表以反映新的当前版本
                                const versions = await this.generatePluginVersions(this.currentVersionSwitchPlugin);

                                // 重新渲染版本列表
                                this.renderPluginVersions(versions);

                                // 关闭版本切换弹窗
                                const modal = document.getElementById('version-switch-modal');
                                if (modal) {
                                    modal.classList.remove('show');
                                    modal.style.display = 'none';
                                }

                                // 等待一下确保后端缓存已清除
                                await new Promise(resolve => setTimeout(resolve, 800));

                                // 强制刷新插件列表以显示最新版本信息
                                await this.refreshPluginListAndScrollToPlugin(this.currentVersionSwitchPlugin);

                                // 延迟显示成功消息，确保滚动和高亮效果先执行
                                setTimeout(async () => {
                                    // 检查ComfyUI运行状态并显示简化的成功消息
                                    const comfyuiRunning = await this.checkComfyUIStatus();
                                    const restartMessage = comfyuiRunning ? ' 请重启ComfyUI以使更改生效。' : '';
                                    this.showNotification(`✅ 版本切换成功！${restartMessage}`, 'success', 'version');
                                }, 1000); // 延长到1秒，确保滚动动画完成

                            } else if (result.status === 'warning') {
                                // 处理警告（如有未提交的更改）- 自动强制切换
                                {
                                    // 用户确认强制切换，重新调用API并添加force参数
                                    const forceResponse = await fetch(`${this.backendUrl}/plugins/${encodeURIComponent(this.currentVersionSwitchPlugin)}/switch-version`, {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({ version: version, force: true })
                                    });

                                    if (forceResponse.ok) {
                                        const forceResult = await forceResponse.json();
                                        if (forceResult.status === 'success') {
                                            // 更新状态并重新渲染
                                            if (!this.pluginVersionStates) {
                                                this.pluginVersionStates = {};
                                            }
                                            this.pluginVersionStates[this.currentVersionSwitchPlugin] = version;
                                            const versions = await this.generatePluginVersions(this.currentVersionSwitchPlugin);
                                            this.renderPluginVersions(versions);

                                            // 关闭版本切换弹窗
                                            const modal = document.getElementById('version-switch-modal');
                                            if (modal) {
                                                modal.classList.remove('show');
                                                modal.style.display = 'none';
                                            }

                                            // 等待一下确保后端缓存已清除
                                            await new Promise(resolve => setTimeout(resolve, 800));

                                            // 强制刷新插件列表并保持当前插件的可见性
                                            await this.refreshPluginListAndScrollToPlugin(this.currentVersionSwitchPlugin);

                                            // 延迟显示成功消息，确保滚动和高亮效果先执行
                                            setTimeout(async () => {
                                                // 检查ComfyUI运行状态并显示简化的成功消息
                                                const comfyuiRunning = await this.checkComfyUIStatus();
                                                const restartMessage = comfyuiRunning ? ' 请重启ComfyUI以使更改生效。' : '';
                                                this.showNotification(`✅ 版本切换成功！${restartMessage}`, 'success', 'version');
                                            }, 1000);
                                        } else {
                                            alert(`❌ 强制切换失败：${forceResult.message}`);
                                        }
                                    }
                                }
                            } else {
                                // 处理错误
                                alert(`❌ 切换失败：${result.message}`);
                            }

                        } else {
                            throw new Error(`HTTP错误: ${response.status}`);
                        }

                    } catch (apiError) {
                        console.error('API call failed:', apiError);
                        alert(`❌ 版本切换失败：${apiError.message}\n\n可能的原因：\n• 网络连接问题\n• 后端服务异常\n• 插件不是Git仓库\n• Git操作权限不足\n\n请检查控制台日志获取详细信息。`);
                    }

                } catch (error) {
                    console.error('Switch plugin version failed:', error);
                    alert(`版本切换失败: ${error.message}`);

                    // 恢复版本列表显示
                    try {
                        const versions = await this.generatePluginVersions(this.currentVersionSwitchPlugin);
                        this.renderPluginVersions(versions);
                    } catch (restoreError) {
                        console.error('Failed to restore version list:', restoreError);
                    }
                }
            }

            async uninstallPlugin(pluginName) {
                // 显示自定义确认对话框
                const confirmed = await this.showCustomConfirm(
                    '确认卸载插件',
                    `确定要卸载插件 "${pluginName}" 吗？\n\n⚠️ 警告：此操作将永久删除插件文件，无法撤销！\n建议在卸载前备份重要数据。`
                );

                if (confirmed) {
                    try {
                        console.log('Uninstalling plugin:', pluginName);
                        this.showNotification(`正在卸载插件 ${pluginName}...`, 'info');

                        // 调用真实的卸载API
                        const response = await fetch(`${this.backendUrl}/nodes/uninstall`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                node_name: pluginName,
                                create_backup: true  // 创建备份
                            })
                        });

                        const result = await response.json();

                        if (result.status === 'success') {
                            // 从列表中移除插件
                            this.installedPlugins = this.installedPlugins.filter(p => p.name !== pluginName);
                            this.renderInstalledPlugins(this.installedPlugins);

                            this.showNotification(`插件 ${pluginName} 卸载成功`, 'success', 'plugin');
                        } else {
                            throw new Error(result.message || '卸载失败');
                        }

                    } catch (error) {
                        console.error('Uninstall plugin failed:', error);
                        this.showNotification(`插件卸载失败: ${error.message}`, 'error');
                    }
                }
            }

            async togglePlugin(pluginName, currentEnabled) {
                try {
                    console.log('Toggling plugin:', pluginName, 'current enabled:', currentEnabled);

                    const action = currentEnabled ? '禁用' : '启用';

                    // 显示自定义确认对话框
                    const confirmed = await this.showCustomConfirm(
                        `确认${action}插件`,
                        `确定要${action}插件 "${pluginName}" 吗？\n\n${action}后需要重启ComfyUI才能生效。`
                    );

                    if (!confirmed) {
                        return;
                    }

                    this.showNotification(`正在${action}插件 ${pluginName}...`, 'info');

                    // 调用真实的切换API
                    const response = await fetch(`${this.backendUrl}/nodes/toggle`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            node_name: pluginName,
                            enable: !currentEnabled  // 切换状态
                        })
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        // 更新插件状态（同时更新enabled和status字段）
                        const plugin = this.installedPlugins.find(p => p.name === pluginName);
                        if (plugin) {
                            const newEnabledState = !currentEnabled;
                            plugin.enabled = newEnabledState;
                            plugin.status = newEnabledState ? 'enabled' : 'disabled';

                            console.log(`Plugin ${pluginName} state updated:`, {
                                enabled: plugin.enabled,
                                status: plugin.status
                            });
                        }

                        // 清除HTML缓存并重新渲染插件列表
                        this.cachedPluginHTML = null;
                        this.renderInstalledPlugins(this.installedPlugins);

                        this.showNotification(`插件 ${pluginName} ${action}成功`, 'success', 'plugin');
                    } else {
                        throw new Error(result.message || `${action}失败`);
                    }

                } catch (error) {
                    console.error('Toggle plugin failed:', error);
                    this.showNotification(`插件状态切换失败: ${error.message}`, 'error');
                }
            }

            async installPlugin(pluginName, pluginUrl) {
                try {
                    console.log('Installing plugin:', pluginName, 'from:', pluginUrl);
                    this.showNotification(`正在安装插件 ${pluginName}...`, 'info');
                    
                    // 模拟安装过程
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    
                    // 添加到已安装列表
                    const newPlugin = {
                        name: pluginName,
                        version: "1.0.0",
                        date: new Date().toISOString().split('T')[0],
                        enabled: true,
                        url: pluginUrl,
                        hasUpdate: false
                    };
                    
                    this.installedPlugins.push(newPlugin);
                    
                    // 如果当前在已安装标签页，刷新列表
                    if (this.currentPluginTab === 'installed') {
                        this.renderInstalledPlugins(this.installedPlugins);
                    }
                    
                    this.showNotification(`插件 ${pluginName} 安装成功`, 'success', 'plugin');
                    
                } catch (error) {
                    console.error('Install plugin failed:', error);
                    this.showNotification(`插件安装失败: ${error.message}`, 'error');
                }
            }



            filterPluginsByCategory(category) {
                if (!this.availablePlugins.length) return;

                const filtered = category ?
                    this.availablePlugins.filter(plugin => plugin.category === category) :
                    this.availablePlugins;

                this.renderAvailablePlugins(filtered);
            }

            // 环境信息检测方法
            async detectEnvironmentInfo() {
                try {
                    // 检测Python环境
                    await this.detectPythonInfo();
                    // 检测CUDA环境
                    await this.detectCudaInfo();
                    // 检测PyTorch环境
                    await this.detectPytorchInfo();
                    // 检测依赖状态
                    await this.detectDependencies();
                } catch (error) {
                    console.error('Environment detection failed:', error);
                }
            }

            async detectPythonInfo() {
                try {
                    const response = await fetch(`${this.backendUrl}/system/python-info`);
                    if (response.ok) {
                        const data = await response.json();
                        document.getElementById('python-version').textContent = data.version || '未检测到';
                        document.getElementById('python-path').textContent = data.path || '未知';
                        document.getElementById('venv-status').textContent = data.venv ? '已激活' : '未激活';
                    } else {
                        throw new Error('Failed to get Python info');
                    }
                } catch (error) {
                    document.getElementById('python-version').textContent = '检测失败';
                    document.getElementById('python-path').textContent = '检测失败';
                    document.getElementById('venv-status').textContent = '检测失败';
                }
            }

            async detectCudaInfo() {
                try {
                    const response = await fetch(`${this.backendUrl}/system/cuda-info`);
                    if (response.ok) {
                        const data = await response.json();
                        document.getElementById('cuda-version').textContent = data.version || '未安装';
                        document.getElementById('gpu-info').textContent = data.gpu_name || '未检测到GPU';
                        document.getElementById('gpu-memory').textContent = data.memory || '未知';
                    } else {
                        throw new Error('Failed to get CUDA info');
                    }
                } catch (error) {
                    document.getElementById('cuda-version').textContent = '检测失败';
                    document.getElementById('gpu-info').textContent = '检测失败';
                    document.getElementById('gpu-memory').textContent = '检测失败';
                }
            }

            async detectPytorchInfo() {
                try {
                    const response = await fetch(`${this.backendUrl}/system/pytorch-info`);
                    if (response.ok) {
                        const data = await response.json();
                        document.getElementById('pytorch-version').textContent = data.version || '未安装';
                        document.getElementById('pytorch-cuda').textContent = data.cuda_available ? '支持' : '不支持';
                        document.getElementById('pytorch-device').textContent = data.device || '未知';
                    } else {
                        throw new Error('Failed to get PyTorch info');
                    }
                } catch (error) {
                    document.getElementById('pytorch-version').textContent = '检测失败';
                    document.getElementById('pytorch-cuda').textContent = '检测失败';
                    document.getElementById('pytorch-device').textContent = '检测失败';
                }
            }

            async detectDependencies() {
                try {
                    const response = await fetch(`${this.backendUrl}/system/dependencies`);
                    if (response.ok) {
                        const data = await response.json();
                        document.getElementById('core-deps').textContent = data.core_status || '检测失败';
                        document.getElementById('optional-deps').textContent = data.optional_status || '检测失败';
                        document.getElementById('env-status').textContent = data.overall_status || '检测失败';
                    } else {
                        throw new Error('Failed to get dependencies info');
                    }
                } catch (error) {
                    document.getElementById('core-deps').textContent = '检测失败';
                    document.getElementById('optional-deps').textContent = '检测失败';
                    document.getElementById('env-status').textContent = '检测失败';
                }
            }
        }

        // 初始化启动器
        let launcherInstance;

        // 音效管理器初始化
        let audioManager;

        // 音效绑定函数
        function bindAudioToButtons() {
            if (!audioManager) return;

            // 为所有按钮绑定点击音效
            document.querySelectorAll('button').forEach(button => {
                if (!button.hasAttribute('data-audio-bound')) {
                    button.addEventListener('click', (e) => {
                        const buttonType = getButtonType(button);
                        audioManager.play(buttonType);
                    });

                    // 悬停音效（如果启用）
                    button.addEventListener('mouseenter', () => {
                        if (audioManager.settings.hoverSounds) {
                            audioManager.play('hover');
                        }
                    });

                    button.setAttribute('data-audio-bound', 'true');
                }
            });

            // 使用事件委托为动态添加的按钮绑定音效
            document.addEventListener('click', (e) => {
                if (e.target.tagName === 'BUTTON' && !e.target.hasAttribute('data-audio-bound')) {
                    const buttonType = getButtonType(e.target);
                    audioManager.play(buttonType);
                }
            });

            // 标签页音效现在通过 getButtonType() 函数和通用按钮绑定处理
            // 不需要单独的标签页音效绑定逻辑

            // 为输入框绑定输入音效
            document.querySelectorAll('input[type="text"], input[type="search"], textarea').forEach(input => {
                if (!input.hasAttribute('data-audio-bound')) {
                    input.addEventListener('focus', () => {
                        audioManager.play('input');
                    });
                    input.setAttribute('data-audio-bound', 'true');
                }
            });
        }

        // 优化的按钮音效识别 - 基于音效方案规划
        function getButtonType(button) {
            const classList = button.classList;
            const id = button.id;
            const text = button.textContent.toLowerCase();

            // 标签页按钮 - 使用标签页切换音效（导航标签音效）
            if (classList.contains('tab-btn') || classList.contains('plugin-tab-btn') ||
                classList.contains('version-tab-btn') || classList.contains('environment-tab-btn') ||
                button.hasAttribute('data-tab') || button.hasAttribute('data-type')) {
                return 'tab-switch';
            }

            // ComfyUI系统级操作 - 使用专用系统音效
            if (id === 'start-comfyui-btn' || (text.includes('启动') && text.includes('comfyui'))) {
                return 'startup';
            }
            if (id === 'stop-comfyui-btn' || (text.includes('停止') && text.includes('comfyui')) ||
                (text.includes('关闭') && text.includes('comfyui'))) {
                return 'shutdown';
            }

            // 警告类按钮 - 使用警告音效
            if (classList.contains('danger') || classList.contains('warning') || classList.contains('uninstall') ||
                text.includes('删除') || text.includes('卸载') || text.includes('清空') ||
                text.includes('重置') || text.includes('取消') || text.includes('移除')) {
                return 'warning';
            }

            // 主要操作按钮 - 使用主要按钮音效（按钮科技音效）
            if (classList.contains('primary') || classList.contains('btn-primary') ||
                classList.contains('main-btn') || classList.contains('important') ||
                classList.contains('install') || classList.contains('update') ||
                text.includes('保存') || text.includes('确认') || text.includes('安装') ||
                text.includes('更新') || text.includes('下载') || text.includes('应用') ||
                text.includes('切换版本') || text.includes('切换到')) {
                return 'click-primary';
            }

            // 开关切换按钮 - 使用切换音效（导航标签音效）
            if (classList.contains('toggle') || classList.contains('switch') ||
                text.includes('启用') || text.includes('禁用') || text.includes('开启') || text.includes('关闭')) {
                return 'switch';
            }

            // 确认对话框按钮 - 使用确认音效（导航标签音效）
            if (classList.contains('confirm') || text.includes('确定') || text.includes('好的')) {
                return 'confirm';
            }

            // 默认使用普通点击音效（按钮科技音效）
            return 'click';
        }

        // 初始化声音设置
        function initAudioSettings() {
            // 初始化背景视频声音设置
            initVideoAudioSettings();

            if (!audioManager) return;

            // 加载保存的设置
            const settings = audioManager.getSettings();

            // 设置UI控件的值
            const audioEnabled = document.getElementById('audio-enabled');
            const audioVolume = document.getElementById('audio-volume');
            const volumeDisplay = document.getElementById('volume-display');

            if (audioEnabled) {
                audioEnabled.checked = settings.enabled;
                audioEnabled.addEventListener('change', (e) => {
                    audioManager.setEnabled(e.target.checked);
                    if (e.target.checked) {
                        audioManager.play('click');
                    }
                });
            }

            if (audioVolume && volumeDisplay) {
                audioVolume.value = settings.volume * 100;
                volumeDisplay.textContent = Math.round(settings.volume * 100) + '%';
                audioVolume.addEventListener('input', (e) => {
                    const volume = e.target.value / 100;
                    audioManager.setVolume(volume);
                    volumeDisplay.textContent = e.target.value + '%';
                    // 播放预览音效
                    audioManager.play('click', volume);
                });
            }

        }

        // 初始化背景视频声音设置
        function initVideoAudioSettings() {
            const videoAudioEnabled = document.getElementById('video-audio-enabled');
            const videoVolume = document.getElementById('video-volume');
            const videoVolumeDisplay = document.getElementById('video-volume-display');
            const backgroundVideo = document.getElementById('backgroundVideo');

            // 从localStorage加载设置
            const savedVideoAudioEnabled = localStorage.getItem('video-audio-enabled') === 'true';
            const savedVideoVolume = parseFloat(localStorage.getItem('video-volume')) || 0.3;

            // 设置初始值
            if (videoAudioEnabled) {
                videoAudioEnabled.checked = savedVideoAudioEnabled;
                videoAudioEnabled.addEventListener('change', (e) => {
                    const enabled = e.target.checked;
                    localStorage.setItem('video-audio-enabled', enabled);
                    if (backgroundVideo) {
                        backgroundVideo.muted = !enabled;
                        if (enabled) {
                            backgroundVideo.volume = savedVideoVolume;
                        }
                    }
                    // 播放点击音效
                    if (window.audioManager) {
                        audioManager.play('click');
                    }
                });
            }

            if (videoVolume && videoVolumeDisplay) {
                videoVolume.value = savedVideoVolume * 100;
                videoVolumeDisplay.textContent = Math.round(savedVideoVolume * 100) + '%';
                videoVolume.addEventListener('input', (e) => {
                    const volume = e.target.value / 100;
                    localStorage.setItem('video-volume', volume);
                    videoVolumeDisplay.textContent = e.target.value + '%';
                    if (backgroundVideo && !backgroundVideo.muted) {
                        backgroundVideo.volume = volume;
                    }
                });
            }

            // 应用初始设置到视频元素
            if (backgroundVideo) {
                backgroundVideo.muted = !savedVideoAudioEnabled;
                if (savedVideoAudioEnabled) {
                    backgroundVideo.volume = savedVideoVolume;
                }
            }
        }

        // 音效预览函数
        function previewSound(soundName) {
            if (audioManager) {
                audioManager.preview(soundName);
            }
        }
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOMContentLoaded - initializing launcher...');

            // 初始化音效管理器
            if (window.audioManager) {
                audioManager = window.audioManager;
                console.log('Audio manager initialized');

                // 绑定按钮音效
                setTimeout(() => {
                    bindAudioToButtons();
                    initAudioSettings();
                    console.log('Audio effects bound to buttons');
                }, 100);

                // 播放启动音效
                setTimeout(() => audioManager.play('startup'), 500);
            }

            launcherInstance = new AIVisionLauncher();
            console.log('launcherInstance created:', !!launcherInstance);
            console.log('showPluginVersions method exists:', !!(launcherInstance && launcherInstance.showPluginVersions));

            // 添加窗口关闭音效
            window.addEventListener('beforeunload', () => {
                if (window.audioManager) {
                    audioManager.play('app-close');
                }
            });

            // 初始化时立即加载GPU信息
            if (launcherInstance && launcherInstance.loadRealGPUInfo) {
                setTimeout(() => {
                    launcherInstance.loadRealGPUInfo();
                }, 1000); // 延迟1秒确保后端服务已启动
            }



            // 加载保存的主题
            loadSavedTheme();

            // 初始化开机自启动设置
            initAutoStartSetting();

            // 为开机自启动复选框添加事件监听器
            const autoStartCheckbox = document.getElementById('autoStart');
            if (autoStartCheckbox) {
                autoStartCheckbox.addEventListener('change', toggleAutoStart);
            }

            // 初始化命令预览（仅在启动设置元素存在时）
            if (document.getElementById('listen-ip')) {
                launcherInstance.updateCommandPreview();
            }
            console.log('Launcher initialization complete');
        });

        // 全局函数，供HTML按钮调用
        function saveSettings() {
            if (launcherInstance) {
                launcherInstance.saveSettings();
            }
        }

        function applyAndLaunch() {
            if (launcherInstance) {
                launcherInstance.applyAndLaunch();
            }
        }

        // 环境配置页面相关函数
        function switchEnvironmentTab(tabId) {
            // 隐藏所有标签页内容
            const allTabs = document.querySelectorAll('.environment-tab-content');
            allTabs.forEach(tab => tab.classList.remove('active'));

            // 移除所有标签按钮的active类
            const allButtons = document.querySelectorAll('.environment-tab-btn');
            allButtons.forEach(btn => btn.classList.remove('active'));

            // 显示选中的标签页
            const selectedTab = document.getElementById(tabId);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // 激活对应的标签按钮
            const selectedButton = document.querySelector(`[onclick="switchEnvironmentTab('${tabId}')"]`);
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            // 如果切换到环境信息标签页，自动检测环境信息
            if (tabId === 'env-info' && launcherInstance) {
                launcherInstance.detectEnvironmentInfo();
            }
        }

        function refreshEnvironmentInfo() {
            if (launcherInstance) {
                launcherInstance.detectEnvironmentInfo();
            }
        }

        function returnToMainPage() {
            // 切换到主页标签
            if (launcherInstance && launcherInstance.switchTab) {
                launcherInstance.switchTab('home');
            } else {
                console.error('launcherInstance or switchTab method not available');
            }
        }

        // 主题切换功能
        function switchTheme(theme) {
            // 更新主题按钮状态
            const themeBtns = document.querySelectorAll('.theme-btn');
            themeBtns.forEach(btn => btn.classList.remove('active'));

            const activeBtn = document.querySelector(`[data-theme="${theme}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            // 应用主题颜色
            const root = document.documentElement;
            switch(theme) {
                case 'blue':
                    root.style.setProperty('--neon-glow', '#00f5ff');
                    root.style.setProperty('--accent-cyan', '#00f5ff');
                    break;
                case 'green':
                    root.style.setProperty('--neon-glow', '#00ff88');
                    root.style.setProperty('--accent-cyan', '#00ff88');
                    break;
                case 'purple':
                    root.style.setProperty('--neon-glow', '#8a2be2');
                    root.style.setProperty('--accent-cyan', '#8a2be2');
                    break;
                case 'red':
                    root.style.setProperty('--neon-glow', '#ff4757');
                    root.style.setProperty('--accent-cyan', '#ff4757');
                    break;
            }

            // 保存主题设置到本地存储
            localStorage.setItem('selectedTheme', theme);

            console.log(`Theme switched to: ${theme}`);
        }

        // 页面加载时恢复保存的主题
        function loadSavedTheme() {
            const savedTheme = localStorage.getItem('selectedTheme') || 'blue';
            switchTheme(savedTheme);
        }

        // 开机自启动功能
        async function initAutoStartSetting() {
            try {
                const result = await window.electronAPI.getAutoStart();
                if (result.success) {
                    const autoStartCheckbox = document.getElementById('autoStart');
                    if (autoStartCheckbox) {
                        autoStartCheckbox.checked = result.enabled;
                        console.log('开机自启动状态:', result.enabled);
                    }
                }
            } catch (error) {
                console.error('获取开机自启动状态失败:', error);
            }
        }

        async function toggleAutoStart(event) {
            try {
                const enabled = event.target.checked;
                console.log('设置开机自启动:', enabled);

                const result = await window.electronAPI.setAutoStart(enabled);

                if (result.success) {
                    console.log('开机自启动设置成功:', enabled);

                    // 显示成功提示
                    await window.electronAPI.showMessageBox({
                        type: 'info',
                        title: '设置成功',
                        message: enabled ? '已启用开机自启动' : '已禁用开机自启动',
                        buttons: ['确定']
                    });
                } else {
                    throw new Error(result.error || '设置开机自启动失败');
                }
            } catch (error) {
                console.error('设置开机自启动失败:', error);

                // 恢复复选框状态
                event.target.checked = !event.target.checked;

                // 显示错误提示
                await window.electronAPI.showMessageBox({
                    type: 'error',
                    title: '设置失败',
                    message: '设置开机自启动失败',
                    detail: error.message,
                    buttons: ['确定']
                });
            }
        }

        // 创建桌面快捷方式
        async function createDesktopShortcut(event) {
            try {
                // 显示加载状态
                const button = event ? event.target.closest('.action-btn') : document.querySelector('.action-btn[onclick*="createDesktopShortcut"]');
                if (!button) {
                    throw new Error('无法找到按钮元素');
                }
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 创建中...';
                button.disabled = true;

                // 调用Electron API创建快捷方式
                const result = await window.electronAPI.createDesktopShortcut();

                if (result.success) {
                    // 显示成功消息
                    await window.electronAPI.showMessageBox({
                        type: 'info',
                        title: '成功',
                        message: result.message,
                        detail: `快捷方式已创建到桌面：\n${result.path || ''}`,
                        buttons: ['确定']
                    });

                    // 更新按钮状态
                    button.innerHTML = '<i class="fas fa-check"></i> 创建成功';
                    button.style.background = 'linear-gradient(45deg, #00ff88, #00cc6a)';

                    // 3秒后恢复原状
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.disabled = false;
                        button.style.background = '';
                    }, 3000);

                } else {
                    throw new Error(result.error || '创建快捷方式失败');
                }

            } catch (error) {
                console.error('创建桌面快捷方式失败:', error);

                // 显示错误消息
                await window.electronAPI.showMessageBox({
                    type: 'error',
                    title: '错误',
                    message: '创建桌面快捷方式失败',
                    detail: error.message,
                    buttons: ['确定']
                });

                // 恢复按钮状态
                const button = event ? event.target.closest('.action-btn') : document.querySelector('.action-btn[onclick*="createDesktopShortcut"]');
                if (button) {
                    button.innerHTML = '<i class="fas fa-plus"></i> 创建快捷方式';
                    button.disabled = false;
                }
            }
        }



        function clearTerminal() {
            const terminalOutput = document.getElementById('terminal-output');
            if (terminalOutput) {
                terminalOutput.innerHTML = '';
            }
        }

        function openNewTerminal() {
            alert('新建终端功能开发中...');
        }

        function handleTerminalInput(event) {
            if (event.key === 'Enter') {
                const input = event.target;
                const command = input.value.trim();

                if (command) {
                    // 添加命令到输出
                    const terminalOutput = document.getElementById('terminal-output');
                    if (terminalOutput) {
                        const commandLine = document.createElement('div');
                        commandLine.innerHTML = `<span style="color: #00ff88;">venv> </span><span style="color: #00ff00;">${command}</span>`;
                        commandLine.style.marginBottom = '8px';
                        terminalOutput.appendChild(commandLine);

                        // 执行命令
                        executeTerminalCommand(command, terminalOutput);

                        // 自动滚动到底部
                        scrollTerminalToBottom(terminalOutput);
                    }

                    // 清空输入
                    input.value = '';
                }
            }
        }

        function scrollTerminalToBottom(terminalOutput) {
            // 平滑滚动到底部
            setTimeout(() => {
                terminalOutput.scrollTop = terminalOutput.scrollHeight;
            }, 100);
        }

        async function executeTerminalCommand(command, outputElement) {
            // 显示执行中状态
            const executingDiv = document.createElement('div');
            executingDiv.style.color = '#888';
            executingDiv.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 执行中...`;
            outputElement.appendChild(executingDiv);

            try {
                // 调用后端API执行命令
                const response = await fetch('http://127.0.0.1:8404/terminal/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ command: command })
                });

                // 移除执行中状态
                outputElement.removeChild(executingDiv);

                if (response.ok) {
                    const data = await response.json();

                    // 处理清空命令
                    if (data.clear) {
                        outputElement.innerHTML = '';
                        return;
                    }

                    // 显示命令输出
                    const result = document.createElement('div');
                    result.style.marginBottom = '12px';
                    result.style.lineHeight = '1.4';

                    if (data.error) {
                        result.style.color = '#ff6b6b';
                        result.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${data.error}`;
                    } else if (data.output) {
                        result.style.color = '#cccccc';
                        result.style.whiteSpace = 'pre-wrap';
                        result.textContent = data.output;
                    }

                    outputElement.appendChild(result);

                    // 自动滚动到底部
                    scrollTerminalToBottom(outputElement);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                // 移除执行中状态
                if (executingDiv.parentNode) {
                    outputElement.removeChild(executingDiv);
                }

                // 显示错误信息
                const errorDiv = document.createElement('div');
                errorDiv.style.color = '#ff6b6b';
                errorDiv.style.marginBottom = '12px';
                errorDiv.style.lineHeight = '1.4';
                errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 命令执行失败: ${error.message}`;
                outputElement.appendChild(errorDiv);

                // 自动滚动到底部
                scrollTerminalToBottom(outputElement);
            }
        }

        function resetToDefault() {
            if (launcherInstance) {
                launcherInstance.resetToDefault();
            }
        }

        // 插件管理全局函数
        function updatePlugin(pluginName) {
            console.log('updatePlugin called for:', pluginName);
            if (launcherInstance && launcherInstance.updatePlugin) {
                launcherInstance.updatePlugin(pluginName);
            } else {
                setTimeout(() => {
                    if (launcherInstance && launcherInstance.updatePlugin) {
                        launcherInstance.updatePlugin(pluginName);
                    }
                }, 100);
            }
        }

        function showPluginVersions(pluginName) {
            console.log('Global showPluginVersions called for:', pluginName);
            console.log('launcherInstance available:', !!launcherInstance);

            if (launcherInstance && launcherInstance.showPluginVersions) {
                launcherInstance.showPluginVersions(pluginName);
            } else {
                console.warn('launcherInstance not ready, retrying in 100ms...');
                setTimeout(() => {
                    if (launcherInstance && launcherInstance.showPluginVersions) {
                        launcherInstance.showPluginVersions(pluginName);
                    } else {
                        console.error('launcherInstance still not available after retry');
                        alert('启动器尚未完全加载，请稍后再试');
                    }
                }, 100);
            }
        }





        function installPlugin(pluginName, pluginUrl) {
            if (launcherInstance) {
                launcherInstance.installPlugin(pluginName, pluginUrl);
            }
        }

        function switchPluginVersion(version) {
            if (launcherInstance) {
                launcherInstance.switchPluginVersion(version);
            }
        }

        function closeVersionSwitchModal() {
            console.log('closeVersionSwitchModal called');
            const modal = document.getElementById('version-switch-modal');
            if (modal) {
                modal.classList.remove('show');
                // 添加淡出动画
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
                console.log('Modal closed successfully');
            } else {
                console.error('Modal not found');
            }
            if (launcherInstance) {
                launcherInstance.currentVersionSwitchPlugin = null;
            }
        }

        // 添加键盘支持
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('version-switch-modal');
                if (modal && modal.classList.contains('show')) {
                    closeVersionSwitchModal();
                }
            }
        });

        // 版本管理全局函数


        function cancelVersionSwitch() {
            document.getElementById('version-confirm-modal').classList.remove('show');
            if (launcherInstance) {
                launcherInstance.pendingVersionSwitch = null;
            }
        }

        function confirmVersionSwitch() {
            document.getElementById('version-confirm-modal').classList.remove('show');
            if (launcherInstance && launcherInstance.pendingVersionSwitch) {
                launcherInstance.performVersionSwitch(launcherInstance.pendingVersionSwitch);
            }
        }



        function switchPluginVersion(version) {
            if (launcherInstance && launcherInstance.switchPluginVersion) {
                launcherInstance.switchPluginVersion(version);
            } else {
                setTimeout(() => {
                    if (launcherInstance && launcherInstance.switchPluginVersion) {
                        launcherInstance.switchPluginVersion(version);
                    }
                }, 100);
            }
        }



        function togglePlugin(pluginName, currentEnabled) {
            // 将字符串转换为布尔值
            const isEnabled = currentEnabled === 'true' || currentEnabled === true;

            if (launcherInstance && launcherInstance.togglePlugin) {
                launcherInstance.togglePlugin(pluginName, isEnabled);
            } else {
                setTimeout(() => {
                    if (launcherInstance && launcherInstance.togglePlugin) {
                        launcherInstance.togglePlugin(pluginName, isEnabled);
                    }
                }, 100);
            }
        }

        function uninstallPlugin(pluginName) {
            if (launcherInstance && launcherInstance.uninstallPlugin) {
                launcherInstance.uninstallPlugin(pluginName);
            } else {
                setTimeout(() => {
                    if (launcherInstance && launcherInstance.uninstallPlugin) {
                        launcherInstance.uninstallPlugin(pluginName);
                    }
                }, 100);
            }
        }



        function installPlugin(pluginName, pluginUrl) {
            if (launcherInstance && launcherInstance.installPlugin) {
                launcherInstance.installPlugin(pluginName, pluginUrl);
            } else {
                setTimeout(() => {
                    if (launcherInstance && launcherInstance.installPlugin) {
                        launcherInstance.installPlugin(pluginName, pluginUrl);
                    }
                }, 100);
            }
        }


    </script>
</body>
</html>