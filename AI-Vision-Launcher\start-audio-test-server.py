#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器，用于测试音效系统
解决浏览器CORS策略限制问题
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

class AudioTestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP处理器，添加CORS头"""
    
    def end_headers(self):
        # 添加CORS头，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[音效测试服务器] {format % args}")

def start_server(port=8405):
    """启动音效测试服务器"""
    
    # 确保在正确的目录中运行
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🎵 音效测试服务器启动中...")
    print(f"📁 工作目录: {script_dir}")
    print(f"🌐 端口: {port}")
    
    try:
        with socketserver.TCPServer(("", port), AudioTestHandler) as httpd:
            print(f"✅ 服务器启动成功!")
            print(f"🔗 测试页面地址: http://localhost:{port}/mech-audio-test.html")
            print("📝 按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}/mech-audio-test.html')
                print("🌐 已自动打开浏览器")
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {e}")
                print(f"请手动访问: http://localhost:{port}/mech-audio-test.html")
            
            print("-" * 50)
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:  # Windows: 端口已被占用
            print(f"❌ 端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_server(port + 1)
        else:
            print(f"❌ 服务器启动失败: {e}")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)

if __name__ == "__main__":
    print("🎵 AI Vision Launcher - 音效测试服务器")
    print("=" * 50)
    
    # 检查音效文件是否存在
    custom_sounds_dir = Path("assets/sounds/custom")
    if custom_sounds_dir.exists():
        sound_files = list(custom_sounds_dir.glob("*.WAV"))
        print(f"📂 找到 {len(sound_files)} 个自定义音效文件:")
        for sound_file in sound_files:
            print(f"   🎵 {sound_file.name}")
    else:
        print("⚠️  未找到自定义音效目录")
    
    print("=" * 50)
    start_server()
