#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Vision Launcher 音效配置工具服务器
Audio Configuration Tool Server
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import json
import urllib.parse
import shutil
from pathlib import Path

class AudioConfigHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP处理器，添加CORS头和文件上传功能"""

    def end_headers(self):
        # 添加CORS头，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        """处理GET请求"""
        if self.path == '/api/list-audio-files':
            self.handle_list_audio_files()
        else:
            # 调用父类的GET处理方法
            super().do_GET()

    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/upload-audio':
            self.handle_upload_audio()
        elif self.path == '/api/update-audio-config':
            self.handle_update_config()
        elif self.path == '/api/save-config':
            self.handle_save_config()
        else:
            self.send_error(404, "API endpoint not found")

    def handle_upload_audio(self):
        """处理音频文件上传"""
        try:
            # 获取内容长度
            content_length = int(self.headers['Content-Length'])

            # 读取POST数据
            post_data = self.rfile.read(content_length)

            # 解析multipart/form-data
            boundary = self.headers['Content-Type'].split('boundary=')[1]
            files = self.parse_multipart(post_data, boundary)

            if 'audioFile' not in files:
                self.send_json_response(400, {'error': '没有找到音频文件'})
                return

            file_data = files['audioFile']
            filename = file_data['filename']
            content = file_data['content']

            # 验证文件类型
            allowed_extensions = ['.wav', '.mp3', '.ogg']
            file_ext = Path(filename).suffix.lower()
            if file_ext not in allowed_extensions:
                self.send_json_response(400, {'error': f'不支持的文件格式: {file_ext}'})
                return

            # 保存文件到custom目录
            custom_dir = Path('assets/sounds/custom')
            custom_dir.mkdir(parents=True, exist_ok=True)

            file_path = custom_dir / filename
            with open(file_path, 'wb') as f:
                f.write(content)

            self.send_json_response(200, {
                'success': True,
                'message': f'文件 {filename} 上传成功',
                'path': str(file_path),
                'filename': filename
            })

        except Exception as e:
            print(f"文件上传错误: {e}")
            self.send_json_response(500, {'error': f'文件上传失败: {str(e)}'})

    def handle_list_audio_files(self):
        """处理音频文件列表请求"""
        try:
            custom_dir = Path('assets/sounds/custom')
            if not custom_dir.exists():
                self.send_json_response(200, [])
                return

            # 获取所有音频文件
            audio_files = []
            for ext in ['*.wav', '*.WAV', '*.mp3', '*.MP3', '*.ogg', '*.OGG']:
                audio_files.extend(custom_dir.glob(ext))

            # 去重并排序
            file_names = sorted(list(set([f.name for f in audio_files])))

            self.send_json_response(200, file_names)

        except Exception as e:
            print(f"获取文件列表错误: {e}")
            self.send_json_response(500, {'error': f'获取文件列表失败: {str(e)}'})

    def handle_save_config(self):
        """处理音效配置保存到文件"""
        try:
            # 获取内容长度
            content_length = int(self.headers['Content-Length'])

            # 读取POST数据
            post_data = self.rfile.read(content_length)
            config_data = json.loads(post_data.decode('utf-8'))

            # 保存配置到文件
            config_file = Path('audio-config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            print(f"配置已保存到文件: {config_file}")

            self.send_json_response(200, {
                'success': True,
                'message': '配置保存成功',
                'file': str(config_file)
            })

        except Exception as e:
            print(f"保存配置错误: {e}")
            self.send_json_response(500, {'error': f'保存配置失败: {str(e)}'})

    def handle_update_config(self):
        """处理音效配置更新"""
        try:
            # 获取内容长度
            content_length = int(self.headers['Content-Length'])

            # 读取POST数据
            post_data = self.rfile.read(content_length)
            config_data = json.loads(post_data.decode('utf-8'))

            # 这里可以将配置保存到文件或数据库
            # 目前只是返回成功响应
            self.send_json_response(200, {
                'success': True,
                'message': '配置更新成功'
            })

        except Exception as e:
            print(f"配置更新错误: {e}")
            self.send_json_response(500, {'error': f'配置更新失败: {str(e)}'})

    def parse_multipart(self, data, boundary):
        """解析multipart/form-data"""
        files = {}
        boundary = boundary.encode()
        parts = data.split(b'--' + boundary)

        for part in parts:
            if b'Content-Disposition' not in part:
                continue

            # 分离头部和内容
            if b'\r\n\r\n' in part:
                headers, content = part.split(b'\r\n\r\n', 1)
                content = content.rstrip(b'\r\n')
            else:
                continue

            # 解析Content-Disposition头
            headers_str = headers.decode('utf-8', errors='ignore')
            if 'name="audioFile"' in headers_str:
                # 提取文件名
                filename = ''
                for line in headers_str.split('\n'):
                    if 'filename=' in line:
                        filename = line.split('filename=')[1].strip().strip('"')
                        break

                files['audioFile'] = {
                    'filename': filename,
                    'content': content
                }

        return files

    def send_json_response(self, status_code, data):
        """发送JSON响应"""
        response = json.dumps(data, ensure_ascii=False).encode('utf-8')
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(response)))
        self.end_headers()
        self.wfile.write(response)

    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[音效配置工具] {format % args}")

def start_server(port=8406):
    """启动音效配置工具服务器"""
    
    # 确保在正确的目录中运行
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🎵 AI Vision Launcher - 音效配置工具")
    print("=" * 50)
    print(f"📁 工作目录: {script_dir}")
    print(f"🌐 端口: {port}")
    
    try:
        with socketserver.TCPServer(("", port), AudioConfigHandler) as httpd:
            print(f"✅ 服务器启动成功!")
            print(f"🔗 配置工具地址: http://localhost:{port}/audio-config-tool.html")
            print("📝 按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}/audio-config-tool.html')
                print("🌐 已自动打开浏览器")
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {e}")
                print(f"请手动访问: http://localhost:{port}/audio-config-tool.html")
            
            print("-" * 50)
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:  # Windows: 端口已被占用
            print(f"❌ 端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_server(port + 1)
        else:
            print(f"❌ 服务器启动失败: {e}")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)

if __name__ == "__main__":
    print("🎵 AI Vision Launcher - 音效配置工具服务器")
    print("=" * 50)
    
    # 检查音效文件目录
    custom_sounds_dir = Path("assets/sounds/custom")
    if custom_sounds_dir.exists():
        sound_files = list(custom_sounds_dir.glob("*.WAV")) + list(custom_sounds_dir.glob("*.wav"))
        print(f"📂 找到 {len(sound_files)} 个音效文件:")
        for sound_file in sound_files:
            print(f"   🎵 {sound_file.name}")
    else:
        print("⚠️  未找到音效目录，将自动创建")
        custom_sounds_dir.mkdir(parents=True, exist_ok=True)
    
    print("=" * 50)
    start_server()
