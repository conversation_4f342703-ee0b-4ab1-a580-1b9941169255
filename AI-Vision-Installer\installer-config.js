/**
 * AI-Vision 智能安装器配置
 * 配置安装器的各项参数和组件版本
 */

const INSTALLER_CONFIG = {
    // 安装器版本
    version: '1.0.0',
    
    // 安装配置
    installation: {
        defaultPath: 'C:\\AI-Vision',
        allowCustomPath: true,
        requiredSpace: 53687091200, // 50GB in bytes
        recommendedSpace: 64424509440, // 60GB in bytes
        createShortcut: true,
        addToStartMenu: true
    },
    
    // Python环境配置
    python: {
        version: '3.12.9',
        type: 'portable', // 便携式Python
        filename: 'python-3.12.9-embed-amd64.zip',
        size: 170000000, // ~162MB
        downloadUrl: 'https://www.python.org/ftp/python/3.12.9/python-3.12.9-embed-amd64.zip',
        sha256: '' // 需要获取实际的SHA256值
    },
    
    // PyTorch配置
    pytorch: {
        cudaVersion: '12.8',
        torchVersion: '2.7.1', // 最新版本
        packages: [
            'torch==2.7.1+cu128',
            'torchvision==0.22.1+cu128', 
            'torchaudio==2.7.1+cu128'
        ],
        indexUrl: 'https://download.pytorch.org/whl/cu128',
        fallbackIndexUrl: 'https://download.pytorch.org/whl/torch_stable.html',
        estimatedSize: 3500000000 // ~3.5GB
    },
    
    // ComfyUI配置
    comfyui: {
        repository: 'https://github.com/comfyanonymous/ComfyUI',
        branch: 'master',
        filename: 'ComfyUI-master.zip',
        estimatedSize: 500000000 // ~500MB
    },
    
    // 下载镜像配置
    mirrors: {
        python: {
            official: 'https://www.python.org/ftp/python/',
            china: 'https://npm.taobao.org/mirrors/python/',
            backup: 'https://registry.npmmirror.com/binary.html?path=python/'
        },
        pytorch: {
            official: 'https://download.pytorch.org/whl/cu128',
            china: 'https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/',
            backup: 'https://mirrors.aliyun.com/pytorch/'
        },
        comfyui: {
            github: 'https://github.com/comfyanonymous/ComfyUI/archive/',
            china: 'https://mirror.ghproxy.com/https://github.com/comfyanonymous/ComfyUI/archive/',
            backup: 'https://gitee.com/mirrors/comfyui/repository/archive/'
        }
    },
    
    // 依赖包配置
    dependencies: {
        // ComfyUI核心依赖
        core: [
            'Pillow>=10.0.0',
            'numpy>=1.24.0,<2.0.0',
            'safetensors>=0.4.0',
            'transformers>=4.35.0',
            'tokenizers>=0.15.0',
            'sentencepiece>=0.1.99',
            'opencv-python>=********',
            'psutil>=5.9.5',
            'kornia>=0.7.0',
            'spandrel>=0.3.0',
            'soundfile>=0.12.1',
            'einops>=0.7.0',
            'diffusers>=0.25.0',
            'accelerate>=0.25.0',
            'xformers>=0.0.23'
        ],
        
        // 后端服务依赖
        backend: [
            'fastapi==0.104.1',
            'uvicorn[standard]==0.24.0',
            'websockets==12.0',
            'aiofiles==23.2.1',
            'python-multipart==0.0.6',
            'requests>=2.31.0',
            'gitpython>=3.1.40'
        ]
    },
    
    // 安装步骤配置
    installSteps: [
        { id: 'welcome', name: '欢迎', weight: 0 },
        { id: 'path', name: '选择安装路径', weight: 0 },
        { id: 'environment', name: '环境检测', weight: 5 },
        { id: 'download-python', name: '下载Python', weight: 10 },
        { id: 'extract-python', name: '配置Python环境', weight: 5 },
        { id: 'download-comfyui', name: '下载ComfyUI', weight: 15 },
        { id: 'extract-comfyui', name: '配置ComfyUI', weight: 5 },
        { id: 'install-pytorch', name: '安装PyTorch', weight: 30 },
        { id: 'install-dependencies', name: '安装依赖包', weight: 20 },
        { id: 'configure', name: '配置环境', weight: 5 },
        { id: 'finalize', name: '完成安装', weight: 5 }
    ]
};

// 导出配置
if (typeof module !== 'undefined') {
    module.exports = INSTALLER_CONFIG;
}
